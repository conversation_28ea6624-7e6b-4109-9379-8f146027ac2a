{"rustc": 15497389221046826682, "features": "[\"32-column-tables\", \"default\", \"r2d2\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"__with_asan_tests\", \"chrono\", \"default\", \"extras\", \"huge-tables\", \"i-implement-a-third-party-backend-and-opt-into-breaking-changes\", \"ipnet-address\", \"large-tables\", \"mysql\", \"mysql_backend\", \"mysqlclient-src\", \"network-address\", \"numeric\", \"postgres\", \"postgres_backend\", \"pq-src\", \"quickcheck\", \"r2d2\", \"returning_clauses_for_sqlite_3_35\", \"serde_json\", \"sqlite\", \"time\", \"unstable\", \"uuid\", \"with-deprecated\", \"without-deprecated\"]", "target": 17967542459835189317, "profile": 2040997289075261528, "path": 14459625480790577565, "deps": [[6722490998346977199, "r2d2", false, 5546959031575905224], [11640477302413400882, "diesel_derives", false, 17538576416215285861], [16675652872862304210, "libsqlite3_sys", false, 2895471710445269016]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/diesel-45bd30933f5a0504/dep-lib-diesel", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
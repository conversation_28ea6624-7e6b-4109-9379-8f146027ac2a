{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 2040997289075261528, "path": 10893226843374794412, "deps": [[15788444815745660356, "zstd_safe", false, 14404079693100804623]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/zstd-2677f8f81aa5f655/dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
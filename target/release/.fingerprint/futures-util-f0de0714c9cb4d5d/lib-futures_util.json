{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 7069439876703407402, "deps": [[5103565458935487, "futures_io", false, 999204489395201764], [1615478164327904835, "pin_utils", false, 18442387332740425600], [1811549171721445101, "futures_channel", false, 3632859331638188697], [1906322745568073236, "pin_project_lite", false, 12655911550839818583], [6955678925937229351, "slab", false, 2465935685135412346], [7013762810557009322, "futures_sink", false, 7226964883456872835], [7620660491849607393, "futures_core", false, 6260929462617917082], [10565019901765856648, "futures_macro", false, 4069028065657731453], [15932120279885307830, "memchr", false, 1352171920280400605], [16240732885093539806, "futures_task", false, 6429567579291219301]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-util-f0de0714c9cb4d5d/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 15497389221046826682, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 2786229274095121045, "deps": [[5466618496199522463, "crc32fast", false, 11333772142852928327], [7636735136738807108, "miniz_oxide", false, 9821533756201800722]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/flate2-bf014b31143adc38/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
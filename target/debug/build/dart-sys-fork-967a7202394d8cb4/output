OUT_DIR = Some(/Users/<USER>/DevEcoStudioProjects/Haier/base/log/target/debug/build/dart-sys-fork-967a7202394d8cb4/out)
OPT_LEVEL = Some(0)
TARGET = Some(aarch64-apple-darwin)
HOST = Some(aarch64-apple-darwin)
cargo:rerun-if-env-changed=CC_aarch64-apple-darwin
CC_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=CC_aarch64_apple_darwin
CC_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
cargo:rerun-if-env-changed=MACOSX_DEPLOYMENT_TARGET
MACOSX_DEPLOYMENT_TARGET = None
cargo:rerun-if-env-changed=CFLAGS_aarch64-apple-darwin
CFLAGS_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=CFLAGS_aarch64_apple_darwin
CFLAGS_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:warning=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/dart-sys-fork-4.1.1/dart-sdk/include/dart_api_dl.c:33:31: warning: unused parameter 'object' [-Wunused-parameter]
cargo:warning=   33 |     Dart_WeakPersistentHandle object, intptr_t external_size) {
cargo:warning=      |                               ^
cargo:warning=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/dart-sys-fork-4.1.1/dart-sdk/include/dart_api_dl.c:33:48: warning: unused parameter 'external_size' [-Wunused-parameter]
cargo:warning=   33 |     Dart_WeakPersistentHandle object, intptr_t external_size) {
cargo:warning=      |                                                ^
cargo:warning=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/dart-sys-fork-4.1.1/dart-sdk/include/dart_api_dl.c:38:28: warning: unused parameter 'object' [-Wunused-parameter]
cargo:warning=   38 |     Dart_FinalizableHandle object,
cargo:warning=      |                            ^
cargo:warning=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/dart-sys-fork-4.1.1/dart-sdk/include/dart_api_dl.c:39:17: warning: unused parameter 'strong_ref_to_object' [-Wunused-parameter]
cargo:warning=   39 |     Dart_Handle strong_ref_to_object,
cargo:warning=      |                 ^
cargo:warning=/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/dart-sys-fork-4.1.1/dart-sdk/include/dart_api_dl.c:40:14: warning: unused parameter 'external_allocation_size' [-Wunused-parameter]
cargo:warning=   40 |     intptr_t external_allocation_size) {
cargo:warning=      |              ^
cargo:warning=5 warnings generated.
cargo:rerun-if-env-changed=AR_aarch64-apple-darwin
AR_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=AR_aarch64_apple_darwin
AR_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS_aarch64-apple-darwin
ARFLAGS_aarch64-apple-darwin = None
cargo:rerun-if-env-changed=ARFLAGS_aarch64_apple_darwin
ARFLAGS_aarch64_apple_darwin = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rustc-link-lib=static=dart_api_dl
cargo:rustc-link-search=native=/Users/<USER>/DevEcoStudioProjects/Haier/base/log/target/debug/build/dart-sys-fork-967a7202394d8cb4/out

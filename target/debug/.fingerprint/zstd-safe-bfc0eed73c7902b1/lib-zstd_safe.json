{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 9000908083626857979, "path": 4561130558736313901, "deps": [[8373447648276846408, "zstd_sys", false, 5222581121148012178], [15788444815745660356, "build_script_build", false, 356736490695578365]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-bfc0eed73c7902b1/dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"$message_type":"diagnostic","message":"unused variable: `cx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs","byte_start":1840,"byte_end":1842,"line_start":63,"line_end":63,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs","byte_start":1840,"byte_end":1842,"line_start":63,"line_end":63,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":"_cx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `cx`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs:63:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_cx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `cx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/usdk_utils/generic_future.rs","byte_start":863,"byte_end":865,"line_start":26,"line_end":26,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"usdk_rust/rust_usdk/src/usdk_utils/generic_future.rs","byte_start":863,"byte_end":865,"line_start":26,"line_end":26,"column_start":35,"column_end":37,"is_primary":true,"text":[{"text":"    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {","highlight_start":35,"highlight_end":37}],"label":null,"suggested_replacement":"_cx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `cx`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/usdk_utils/generic_future.rs:26:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn poll(self: Pin<&mut Self>, cx: &mut std::task::Context<'_>) -> Poll<Self::Output> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_cx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `app_type_name_map` and `device_id_map` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":85,"byte_end":110,"line_start":5,"line_end":5,"column_start":12,"column_end":37,"is_primary":false,"text":[{"text":"pub struct DiscoveryDeviceNameHelper {","highlight_start":12,"highlight_end":37}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":117,"byte_end":134,"line_start":6,"line_end":6,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    app_type_name_map: HashMap<String, HashMap<i32, bool>>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":177,"byte_end":190,"line_start":7,"line_end":7,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    device_id_map: HashMap<String, i32>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`DiscoveryDeviceNameHelper` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `app_type_name_map` and `device_id_map` are never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DiscoveryDeviceNameHelper {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_type_name_map: HashMap<String, HashMap<i32, bool>>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    device_id_map: HashMap<String, i32>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `DiscoveryDeviceNameHelper` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new`, `get_device_index`, `get_device_name`, and `clear_device_name` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":217,"byte_end":247,"line_start":10,"line_end":10,"column_start":1,"column_end":31,"is_primary":false,"text":[{"text":"impl DiscoveryDeviceNameHelper {","highlight_start":1,"highlight_end":31}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":261,"byte_end":264,"line_start":11,"line_end":11,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":431,"byte_end":447,"line_start":18,"line_end":18,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn get_device_index(&mut self, device_id: &str, app_type_name: &str) -> i32 {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":1641,"byte_end":1656,"line_start":53,"line_end":53,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn get_device_name(&mut self, device_id: &str, app_type_name: &str) -> String {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs","byte_start":2040,"byte_end":2057,"line_start":63,"line_end":63,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"    pub fn clear_device_name(&mut self, device_id: &str, app_type_name: &str) {","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new`, `get_device_index`, `get_device_name`, and `clear_device_name` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_bind/discovery/device_name_helper.rs:11:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DiscoveryDeviceNameHelper {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_device_index(&mut self, device_id: &str, app_type_name: &str) -> i32 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_device_name(&mut self, device_id: &str, app_type_name: &str) -> String {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn clear_device_name(&mut self, device_id: &str, app_type_name: &str) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `C_ERR_ENUM_UNDEFINED` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs","byte_start":508,"byte_end":528,"line_start":17,"line_end":17,"column_start":7,"column_end":27,"is_primary":true,"text":[{"text":"const C_ERR_ENUM_UNDEFINED: i32 = 20001;","highlight_start":7,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `C_ERR_ENUM_UNDEFINED` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_callback/callback.rs:17:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst C_ERR_ENUM_UNDEFINED: i32 = 20001;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `SUCCESS` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":52,"byte_end":59,"line_start":2,"line_end":2,"column_start":22,"column_end":29,"is_primary":true,"text":[{"text":"    pub(crate) const SUCCESS: i32 = 0;","highlight_start":22,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `SUCCESS` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:2:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const SUCCESS: i32 = 0;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RUST_OTHER_ERROR` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":126,"byte_end":142,"line_start":5,"line_end":5,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"    pub(crate) const RUST_OTHER_ERROR: i32 = 108;","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RUST_OTHER_ERROR` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:5:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const RUST_OTHER_ERROR: i32 = 108;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `SERDE_JSON_ERR` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":181,"byte_end":195,"line_start":7,"line_end":7,"column_start":22,"column_end":36,"is_primary":true,"text":[{"text":"    pub(crate) const SERDE_JSON_ERR: i32 = 109;","highlight_start":22,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `SERDE_JSON_ERR` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:7:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const SERDE_JSON_ERR: i32 = 109;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `DEVICE_ERR` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":229,"byte_end":239,"line_start":8,"line_end":8,"column_start":22,"column_end":32,"is_primary":true,"text":[{"text":"    pub(crate) const DEVICE_ERR: i32 = 110;","highlight_start":22,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `DEVICE_ERR` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:8:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) const DEVICE_ERR: i32 = 110;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `UHSD_BIND_DEFAULT_TIMEOUT` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/features/constant.rs","byte_start":320,"byte_end":345,"line_start":12,"line_end":12,"column_start":15,"column_end":40,"is_primary":true,"text":[{"text":"    pub const UHSD_BIND_DEFAULT_TIMEOUT: u16 = 30;","highlight_start":15,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `UHSD_BIND_DEFAULT_TIMEOUT` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/features/constant.rs:12:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const UHSD_BIND_DEFAULT_TIMEOUT: u16 = 30;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a mutable reference to mutable static is discouraged","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_injection.rs","byte_start":1034,"byte_end":1054,"line_start":27,"line_end":27,"column_start":13,"column_end":33,"is_primary":true,"text":[{"text":"            INSTANCE.deref_mut()","highlight_start":13,"highlight_end":33}],"label":"mutable reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"mutable references to mutable statics are dangerous; it's undefined behavior if any other pointer to the static is used or if any other reference is created for the static while the mutable reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a mutable reference to mutable static is discouraged\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_injection.rs:27:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            INSTANCE.deref_mut()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mmutable reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: mutable references to mutable statics are dangerous; it's undefined behavior if any other pointer to the static is used or if any other reference is created for the static while the mutable reference lives\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a mutable reference to mutable static is discouraged","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_manager.rs","byte_start":1181,"byte_end":1201,"line_start":28,"line_end":28,"column_start":13,"column_end":33,"is_primary":true,"text":[{"text":"            INSTANCE.deref_mut()","highlight_start":13,"highlight_end":33}],"label":"mutable reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"mutable references to mutable statics are dangerous; it's undefined behavior if any other pointer to the static is used or if any other reference is created for the static while the mutable reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a mutable reference to mutable static is discouraged\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_manager.rs:28:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            INSTANCE.deref_mut()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mmutable reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: mutable references to mutable statics are dangerous; it's undefined behavior if any other pointer to the static is used or if any other reference is created for the static while the mutable reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":4372,"byte_end":4378,"line_start":126,"line_end":126,"column_start":74,"column_end":80,"is_primary":true,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":74,"highlight_end":80}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":4327,"byte_end":4371,"line_start":126,"line_end":126,"column_start":29,"column_end":73,"is_primary":false,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":29,"highlight_end":73}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dangling_pointers_from_temporaries)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:126:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dangling_pointers_from_temporaries)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":4509,"byte_end":4515,"line_start":129,"line_end":129,"column_start":82,"column_end":88,"is_primary":true,"text":[{"text":"            traceId: req_info.trace_id.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":82,"highlight_end":88}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":4449,"byte_end":4508,"line_start":129,"line_end":129,"column_start":22,"column_end":81,"is_primary":false,"text":[{"text":"            traceId: req_info.trace_id.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":22,"highlight_end":81}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:129:82\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            traceId: req_info.trace_id.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6057,"byte_end":6063,"line_start":173,"line_end":173,"column_start":91,"column_end":97,"is_primary":true,"text":[{"text":"            deviceTmpId: req_info.device_tmp_id.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":91,"highlight_end":97}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":5992,"byte_end":6056,"line_start":173,"line_end":173,"column_start":26,"column_end":90,"is_primary":false,"text":[{"text":"            deviceTmpId: req_info.device_tmp_id.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":26,"highlight_end":90}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:173:91\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            deviceTmpId: req_info.device_tmp_id.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6182,"byte_end":6188,"line_start":175,"line_end":175,"column_start":74,"column_end":80,"is_primary":true,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":74,"highlight_end":80}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6137,"byte_end":6181,"line_start":175,"line_end":175,"column_start":29,"column_end":73,"is_primary":false,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":29,"highlight_end":73}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:175:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6312,"byte_end":6318,"line_start":178,"line_end":178,"column_start":75,"column_end":81,"is_primary":true,"text":[{"text":"            ssid: req_info.ssid.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":75,"highlight_end":81}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6256,"byte_end":6311,"line_start":178,"line_end":178,"column_start":19,"column_end":74,"is_primary":false,"text":[{"text":"            ssid: req_info.ssid.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":19,"highlight_end":74}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:178:75\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ssid: req_info.ssid.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6402,"byte_end":6408,"line_start":179,"line_end":179,"column_start":81,"column_end":87,"is_primary":true,"text":[{"text":"            passwd: req_info.password.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":81,"highlight_end":87}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6342,"byte_end":6401,"line_start":179,"line_end":179,"column_start":21,"column_end":80,"is_primary":false,"text":[{"text":"            passwd: req_info.password.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":21,"highlight_end":80}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:179:81\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            passwd: req_info.password.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6532,"byte_end":6538,"line_start":181,"line_end":181,"column_start":74,"column_end":80,"is_primary":true,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":74,"highlight_end":80}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6487,"byte_end":6531,"line_start":181,"line_end":181,"column_start":29,"column_end":73,"is_primary":false,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":29,"highlight_end":73}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:181:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m181\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6713,"byte_end":6719,"line_start":185,"line_end":185,"column_start":74,"column_end":80,"is_primary":true,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":74,"highlight_end":80}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6668,"byte_end":6712,"line_start":185,"line_end":185,"column_start":29,"column_end":73,"is_primary":false,"text":[{"text":"                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":29,"highlight_end":73}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:185:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Some(it) => it.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"a dangling pointer will be produced because the temporary `CString` will be dropped","code":{"code":"dangling_pointers_from_temporaries","explanation":null},"level":"warning","spans":[{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6983,"byte_end":6989,"line_start":192,"line_end":192,"column_start":82,"column_end":88,"is_primary":true,"text":[{"text":"            traceId: req_info.trace_id.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":82,"highlight_end":88}],"label":"this pointer will immediately be invalid","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"usdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs","byte_start":6923,"byte_end":6982,"line_start":192,"line_end":192,"column_start":22,"column_end":81,"is_primary":false,"text":[{"text":"            traceId: req_info.trace_id.as_str().to_cstring().unwrap_or_default().as_ptr(),","highlight_start":22,"highlight_end":81}],"label":"this `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for more information, see <https://doc.rust-lang.org/reference/destructors.html>","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: a dangling pointer will be produced because the temporary `CString` will be dropped\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0musdk_rust/rust_usdk/src/toolkit_ffi/uhsd_types.rs:192:82\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            traceId: req_info.trace_id.as_str().to_cstring().unwrap_or_default().as_ptr(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthis pointer will immediately be invalid\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis `CString` is deallocated at the end of the statement, bind it to a variable to extend its lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: pointers do not have a lifetime; when calling `as_ptr` the `CString` will be deallocated at the end of the statement because nothing is referencing it as far as the type system is concerned\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: you must make sure that the variable you bind the `CString` to lives at least as long as the pointer returned by the call to `as_ptr`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: in particular, if this pointer is returned from the current function, binding the `CString` inside the function will not suffice\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/reference/destructors.html>\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"21 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 21 warnings emitted\u001b[0m\n\n"}

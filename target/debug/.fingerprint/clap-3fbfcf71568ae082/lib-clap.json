{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 5129752230091461406, "path": 7014022529426593968, "deps": [[14814905555676593471, "clap_builder", false, 13672375526862008802]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-3fbfcf71568ae082/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"$message_type":"diagnostic","message":"unused variable: `resource_info`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":9361,"byte_end":9374,"line_start":289,"line_end":289,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":9361,"byte_end":9374,"line_start":289,"line_end":289,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":"_resource_info","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `resource_info`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/resource_test/resource_test.rs:289:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_resource_info`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `resource_info`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":11010,"byte_end":11023,"line_start":343,"line_end":343,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {}","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":11010,"byte_end":11023,"line_start":343,"line_end":343,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {}","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":"_resource_info","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `resource_info`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/resource_test/resource_test.rs:343:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_resource_info`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":11040,"byte_end":11048,"line_start":343,"line_end":343,"column_start":65,"column_end":73,"is_primary":true,"text":[{"text":"    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {}","highlight_start":65,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":11040,"byte_end":11048,"line_start":343,"line_end":343,"column_start":65,"column_end":73,"is_primary":true,"text":[{"text":"    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {}","highlight_start":65,"highlight_end":73}],"label":null,"suggested_replacement":"_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `progress`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/resource_test/resource_test.rs:343:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn on_progress_changed(&self, resource_info: &ResourceInfo, progress: usize) {}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `CANCEL_PROGRESS` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/resource_test/resource_test.rs","byte_start":9253,"byte_end":9268,"line_start":286,"line_end":286,"column_start":7,"column_end":22,"is_primary":true,"text":[{"text":"const CANCEL_PROGRESS: usize = 66;","highlight_start":7,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `CANCEL_PROGRESS` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/resource_test/resource_test.rs:286:7\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst CANCEL_PROGRESS: usize = 66;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"4 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 4 warnings emitted\u001b[0m\n\n"}

{"$message_type":"diagnostic","message":"constant `RESULT_STRING_VALUE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":31,"byte_end":50,"line_start":2,"line_end":2,"column_start":15,"column_end":34,"is_primary":true,"text":[{"text":"    pub const RESULT_STRING_VALUE: &str = \"stringValue\";","highlight_start":15,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RESULT_STRING_VALUE` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:2:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const RESULT_STRING_VALUE: &str = \"stringValue\";\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RESULT_BOOLEAN_VALUE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":88,"byte_end":108,"line_start":3,"line_end":3,"column_start":15,"column_end":35,"is_primary":true,"text":[{"text":"    pub const RESULT_BOOLEAN_VALUE: &str = \"booleanValue\";","highlight_start":15,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RESULT_BOOLEAN_VALUE` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:3:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const RESULT_BOOLEAN_VALUE: &str = \"booleanValue\";\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `RESULT_NUMBER_VALUE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":147,"byte_end":166,"line_start":4,"line_end":4,"column_start":15,"column_end":34,"is_primary":true,"text":[{"text":"    pub const RESULT_NUMBER_VALUE: &str = \"numberValue\";","highlight_start":15,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `RESULT_NUMBER_VALUE` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:4:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const RESULT_NUMBER_VALUE: &str = \"numberValue\";\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `FUNCTION_CALL_SUCCESS` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":222,"byte_end":243,"line_start":8,"line_end":8,"column_start":15,"column_end":36,"is_primary":true,"text":[{"text":"    pub const FUNCTION_CALL_SUCCESS: i32 = 0;","highlight_start":15,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `FUNCTION_CALL_SUCCESS` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:8:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const FUNCTION_CALL_SUCCESS: i32 = 0;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `FUNCTION_NOT_FIND_RES` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":268,"byte_end":289,"line_start":9,"line_end":9,"column_start":15,"column_end":36,"is_primary":true,"text":[{"text":"    pub const FUNCTION_NOT_FIND_RES: i32 = 100001;","highlight_start":15,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `FUNCTION_NOT_FIND_RES` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:9:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const FUNCTION_NOT_FIND_RES: i32 = 100001;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `FUNCTION_CALL_FAILED` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":319,"byte_end":339,"line_start":10,"line_end":10,"column_start":15,"column_end":35,"is_primary":true,"text":[{"text":"    pub const FUNCTION_CALL_FAILED: i32 = 100002;","highlight_start":15,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `FUNCTION_CALL_FAILED` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:10:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const FUNCTION_CALL_FAILED: i32 = 100002;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_INIT_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":389,"byte_end":409,"line_start":14,"line_end":14,"column_start":15,"column_end":35,"is_primary":true,"text":[{"text":"    pub const ACTION_INIT_RESOURCE: &str = \"init_resource\";","highlight_start":15,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_INIT_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:14:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_INIT_RESOURCE: &str = \"init_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_PRESET_RESOURCE_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":449,"byte_end":476,"line_start":15,"line_end":15,"column_start":15,"column_end":42,"is_primary":true,"text":[{"text":"    pub const ACTION_PRESET_RESOURCE_LIST: &str = \"preset_resource_list\";","highlight_start":15,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_PRESET_RESOURCE_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:15:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_PRESET_RESOURCE_LIST: &str = \"preset_resource_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_PRESET_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":523,"byte_end":545,"line_start":16,"line_end":16,"column_start":15,"column_end":37,"is_primary":true,"text":[{"text":"    pub const ACTION_PRESET_RESOURCE: &str = \"preset_resource\";","highlight_start":15,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_PRESET_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:16:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_PRESET_RESOURCE: &str = \"preset_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_SEARCH_NORMAL_RESOURCE_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":587,"byte_end":621,"line_start":17,"line_end":17,"column_start":15,"column_end":49,"is_primary":true,"text":[{"text":"    pub const ACTION_SEARCH_NORMAL_RESOURCE_LIST: &str = \"search_normal_resource_list\";","highlight_start":15,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_SEARCH_NORMAL_RESOURCE_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:17:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_SEARCH_NORMAL_RESOURCE_LIST: &str = \"search_normal_resource_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_SEARCH_DEVICE_RESOURCE_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":675,"byte_end":709,"line_start":18,"line_end":18,"column_start":15,"column_end":49,"is_primary":true,"text":[{"text":"    pub const ACTION_SEARCH_DEVICE_RESOURCE_LIST: &str = \"search_device_resource_list\";","highlight_start":15,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_SEARCH_DEVICE_RESOURCE_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:18:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_SEARCH_DEVICE_RESOURCE_LIST: &str = \"search_device_resource_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_CANCEL` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":763,"byte_end":776,"line_start":19,"line_end":19,"column_start":15,"column_end":28,"is_primary":true,"text":[{"text":"    pub const ACTION_CANCEL: &str = \"cancel\";","highlight_start":15,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_CANCEL` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:19:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_CANCEL: &str = \"cancel\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_REQUEST_NORMAL_RESOURCE_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":809,"byte_end":844,"line_start":20,"line_end":20,"column_start":15,"column_end":50,"is_primary":true,"text":[{"text":"    pub const ACTION_REQUEST_NORMAL_RESOURCE_LIST: &str = \"request_normal_resource_list\";","highlight_start":15,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_REQUEST_NORMAL_RESOURCE_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:20:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_REQUEST_NORMAL_RESOURCE_LIST: &str = \"request_normal_resource_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_REQUEST_DEVICE_RESOURCE_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":899,"byte_end":934,"line_start":21,"line_end":21,"column_start":15,"column_end":50,"is_primary":true,"text":[{"text":"    pub const ACTION_REQUEST_DEVICE_RESOURCE_LIST: &str = \"request_device_resource_list\";","highlight_start":15,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_REQUEST_DEVICE_RESOURCE_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:21:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_REQUEST_DEVICE_RESOURCE_LIST: &str = \"request_device_resource_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_INSTALL` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":989,"byte_end":1003,"line_start":22,"line_end":22,"column_start":15,"column_end":29,"is_primary":true,"text":[{"text":"    pub const ACTION_INSTALL: &str = \"install\";","highlight_start":15,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_INSTALL` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:22:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_INSTALL: &str = \"install\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_UNINSTALL` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1037,"byte_end":1053,"line_start":23,"line_end":23,"column_start":15,"column_end":31,"is_primary":true,"text":[{"text":"    pub const ACTION_UNINSTALL: &str = \"uninstall\";","highlight_start":15,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_UNINSTALL` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:23:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_UNINSTALL: &str = \"uninstall\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_QUERY_AND_INSTALL_NORMAL_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1089,"byte_end":1129,"line_start":24,"line_end":24,"column_start":15,"column_end":55,"is_primary":true,"text":[{"text":"    pub const ACTION_QUERY_AND_INSTALL_NORMAL_RESOURCE: &str = \"query_and_install_normal_resource\";","highlight_start":15,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_QUERY_AND_INSTALL_NORMAL_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:24:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_QUERY_AND_INSTALL_NORMAL_RESOURCE: &str = \"query_and_install_normal_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_QUERY_AND_INSTALL_DEVICE_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1189,"byte_end":1229,"line_start":25,"line_end":25,"column_start":15,"column_end":55,"is_primary":true,"text":[{"text":"    pub const ACTION_QUERY_AND_INSTALL_DEVICE_RESOURCE: &str = \"query_and_install_device_resource\";","highlight_start":15,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_QUERY_AND_INSTALL_DEVICE_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:25:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_QUERY_AND_INSTALL_DEVICE_RESOURCE: &str = \"query_and_install_device_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_CLEAN_LOCAL_RESOURCES` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1289,"byte_end":1317,"line_start":26,"line_end":26,"column_start":15,"column_end":43,"is_primary":true,"text":[{"text":"    pub const ACTION_CLEAN_LOCAL_RESOURCES: &str = \"clean_local_resources\";","highlight_start":15,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_CLEAN_LOCAL_RESOURCES` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:26:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_CLEAN_LOCAL_RESOURCES: &str = \"clean_local_resources\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_GET_ENTIRE_RESOURCE_LIST` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1365,"byte_end":1396,"line_start":27,"line_end":27,"column_start":15,"column_end":46,"is_primary":true,"text":[{"text":"    pub const ACTION_GET_ENTIRE_RESOURCE_LIST: &str = \"get_entire_resource_list\";","highlight_start":15,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_GET_ENTIRE_RESOURCE_LIST` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:27:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_GET_ENTIRE_RESOURCE_LIST: &str = \"get_entire_resource_list\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_GET_LATEST_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1447,"byte_end":1473,"line_start":28,"line_end":28,"column_start":15,"column_end":41,"is_primary":true,"text":[{"text":"    pub const ACTION_GET_LATEST_RESOURCE: &str = \"get_latest_resource\";","highlight_start":15,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_GET_LATEST_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:28:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_GET_LATEST_RESOURCE: &str = \"get_latest_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_GET_LATEST_INSTALLED_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1519,"byte_end":1555,"line_start":29,"line_end":29,"column_start":15,"column_end":51,"is_primary":true,"text":[{"text":"    pub const ACTION_GET_LATEST_INSTALLED_RESOURCE: &str = \"get_latest_installed_resource\";","highlight_start":15,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_GET_LATEST_INSTALLED_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:29:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_GET_LATEST_INSTALLED_RESOURCE: &str = \"get_latest_installed_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_GET_RESOURCE` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1611,"byte_end":1630,"line_start":30,"line_end":30,"column_start":15,"column_end":34,"is_primary":true,"text":[{"text":"    pub const ACTION_GET_RESOURCE: &str = \"get_resource\";","highlight_start":15,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_GET_RESOURCE` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:30:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_GET_RESOURCE: &str = \"get_resource\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `ACTION_GET_RESOURCE_INSTALL_PATH` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/src/features/constant.rs","byte_start":1669,"byte_end":1701,"line_start":31,"line_end":31,"column_start":15,"column_end":47,"is_primary":true,"text":[{"text":"    pub const ACTION_GET_RESOURCE_INSTALL_PATH: &str = \"get_resource_install_path\";","highlight_start":15,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `ACTION_GET_RESOURCE_INSTALL_PATH` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/src/features/constant.rs:31:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const ACTION_GET_RESOURCE_INSTALL_PATH: &str = \"get_resource_install_path\";\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"24 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 24 warnings emitted\u001b[0m\n\n"}

{"$message_type":"diagnostic","message":"unused import: `log::LevelFilter`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/cucumber.rs","byte_start":107,"byte_end":123,"line_start":7,"line_end":7,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use log::LevelFilter;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/cucumber.rs","byte_start":103,"byte_end":125,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::LevelFilter;","highlight_start":1,"highlight_end":22},{"text":"use rust_resource::models::resource_info::ResourceInfo;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `log::LevelFilter`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/cucumber.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::LevelFilter;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `InstallCallback`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/cucumber.rs","byte_start":234,"byte_end":249,"line_start":9,"line_end":9,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"use steps::common_res_call_back::{CommonResCallback, InstallCallback, InstallResultHolder};","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/cucumber.rs","byte_start":232,"byte_end":249,"line_start":9,"line_end":9,"column_start":52,"column_end":69,"is_primary":true,"text":[{"text":"use steps::common_res_call_back::{CommonResCallback, InstallCallback, InstallResultHolder};","highlight_start":52,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `InstallCallback`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/cucumber.rs:9:54\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse steps::common_res_call_back::{CommonResCallback, InstallCallback, InstallResultHolder};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":97,"byte_end":101,"line_start":4,"line_end":4,"column_start":37,"column_end":41,"is_primary":true,"text":[{"text":"use crate::utils::resource_holder::{self, ResourceHolder};","highlight_start":37,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":97,"byte_end":103,"line_start":4,"line_end":4,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"use crate::utils::resource_holder::{self, ResourceHolder};","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":96,"byte_end":97,"line_start":4,"line_end":4,"column_start":36,"column_end":37,"is_primary":true,"text":[{"text":"use crate::utils::resource_holder::{self, ResourceHolder};","highlight_start":36,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":117,"byte_end":118,"line_start":4,"line_end":4,"column_start":57,"column_end":58,"is_primary":true,"text":[{"text":"use crate::utils::resource_holder::{self, ResourceHolder};","highlight_start":57,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `self`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:4:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::resource_holder::{self, ResourceHolder};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `combine_device_condition`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":157,"byte_end":181,"line_start":6,"line_end":6,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"    combine_device_condition, combine_normal_condition, get_condition_dict, get_request_resource_list_from_steps,","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":157,"byte_end":183,"line_start":6,"line_end":6,"column_start":5,"column_end":31,"is_primary":true,"text":[{"text":"    combine_device_condition, combine_normal_condition, get_condition_dict, get_request_resource_list_from_steps,","highlight_start":5,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `combine_device_condition`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    combine_device_condition, combine_normal_condition, get_condition_dict, get_request_resource_list_from_steps,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::DataHolder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":274,"byte_end":291,"line_start":8,"line_end":8,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use crate::DataHolder;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":270,"byte_end":293,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::DataHolder;","highlight_start":1,"highlight_end":23},{"text":"use crate::{utils::steps_utils::get_background_resource_list_from_steps, MyWorld};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::DataHolder`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::DataHolder;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::data_source::resource_data_source::ResourceDataSource`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":534,"byte_end":602,"line_start":15,"line_end":15,"column_start":5,"column_end":73,"is_primary":true,"text":[{"text":"use rust_resource::data_source::resource_data_source::ResourceDataSource;","highlight_start":5,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":530,"byte_end":604,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::data_source::resource_data_source::ResourceDataSource;","highlight_start":1,"highlight_end":74},{"text":"use rust_resource::models::condition::from_func::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::data_source::resource_data_source::ResourceDataSource`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::data_source::resource_data_source::ResourceDataSource;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DeviceResourceCondition` and `NormalResourceCondition`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":843,"byte_end":866,"line_start":20,"line_end":20,"column_start":40,"column_end":63,"is_primary":true,"text":[{"text":"use rust_resource::models::condition::{DeviceResourceCondition, NormalResourceCondition};","highlight_start":40,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":868,"byte_end":891,"line_start":20,"line_end":20,"column_start":65,"column_end":88,"is_primary":true,"text":[{"text":"use rust_resource::models::condition::{DeviceResourceCondition, NormalResourceCondition};","highlight_start":65,"highlight_end":88}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":804,"byte_end":894,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::models::condition::{DeviceResourceCondition, NormalResourceCondition};","highlight_start":1,"highlight_end":90},{"text":"use rust_resource::models::query_info::query_condition::condition;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `DeviceResourceCondition` and `NormalResourceCondition`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:20:40\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::models::condition::{DeviceResourceCondition, NormalResourceCondition};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::models::query_info::query_condition::condition`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":898,"byte_end":959,"line_start":21,"line_end":21,"column_start":5,"column_end":66,"is_primary":true,"text":[{"text":"use rust_resource::models::query_info::query_condition::condition;","highlight_start":5,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":894,"byte_end":961,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::models::query_info::query_condition::condition;","highlight_start":1,"highlight_end":67},{"text":"use rust_resource::models::resource_info::ResourceInfo;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::models::query_info::query_condition::condition`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::models::query_info::query_condition::condition;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fmt::format`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":4,"byte_end":20,"line_start":1,"line_end":1,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use std::fmt::format;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":0,"byte_end":22,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fmt::format;","highlight_start":1,"highlight_end":22},{"text":"use std::sync::{Arc, Mutex};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::fmt::format`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fmt::format;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Arc` and `Mutex`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":38,"byte_end":41,"line_start":2,"line_end":2,"column_start":17,"column_end":20,"is_primary":true,"text":[{"text":"use std::sync::{Arc, Mutex};","highlight_start":17,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":43,"byte_end":48,"line_start":2,"line_end":2,"column_start":22,"column_end":27,"is_primary":true,"text":[{"text":"use std::sync::{Arc, Mutex};","highlight_start":22,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":22,"byte_end":51,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::{Arc, Mutex};","highlight_start":1,"highlight_end":29},{"text":"use std::vec;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Arc` and `Mutex`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:2:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::{Arc, Mutex};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":160,"byte_end":164,"line_start":6,"line_end":6,"column_start":37,"column_end":41,"is_primary":true,"text":[{"text":"use crate::utils::resource_holder::{self, ResourceHolder, ANY_RESULT_FAILED_KEY, ANY_RESULT_KEY};","highlight_start":37,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":160,"byte_end":166,"line_start":6,"line_end":6,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"use crate::utils::resource_holder::{self, ResourceHolder, ANY_RESULT_FAILED_KEY, ANY_RESULT_KEY};","highlight_start":37,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `self`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:6:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::resource_holder::{self, ResourceHolder, ANY_RESULT_FAILED_KEY, ANY_RESULT_KEY};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `get_resource_query_vec`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":554,"byte_end":576,"line_start":11,"line_end":11,"column_start":75,"column_end":97,"is_primary":true,"text":[{"text":"    get_resource_list_from_steps_for_task, get_resource_query_from_steps, get_resource_query_vec,","highlight_start":75,"highlight_end":97}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":552,"byte_end":576,"line_start":11,"line_end":11,"column_start":73,"column_end":97,"is_primary":true,"text":[{"text":"    get_resource_list_from_steps_for_task, get_resource_query_from_steps, get_resource_query_vec,","highlight_start":73,"highlight_end":97}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `get_resource_query_vec`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:11:75\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    get_resource_list_from_steps_for_task, get_resource_query_from_steps, get_resource_query_vec,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DataHolder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":593,"byte_end":603,"line_start":13,"line_end":13,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":593,"byte_end":605,"line_start":13,"line_end":13,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":592,"byte_end":593,"line_start":13,"line_end":13,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":612,"byte_end":613,"line_start":13,"line_end":13,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DataHolder`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:13:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{DataHolder, MyWorld};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `diesel::query_builder::Query`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":684,"byte_end":712,"line_start":16,"line_end":16,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"use diesel::query_builder::Query;","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":680,"byte_end":714,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use diesel::query_builder::Query;","highlight_start":1,"highlight_end":34},{"text":"use diesel::result;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `diesel::query_builder::Query`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse diesel::query_builder::Query;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `diesel::result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":718,"byte_end":732,"line_start":17,"line_end":17,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use diesel::result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":714,"byte_end":734,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use diesel::result;","highlight_start":1,"highlight_end":20},{"text":"use mry::ArgMatcher::Any;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `diesel::result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse diesel::result;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ResourceError` and `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":792,"byte_end":796,"line_start":19,"line_end":19,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"use rust_resource::api::error::{self, ResourceError};","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":798,"byte_end":811,"line_start":19,"line_end":19,"column_start":39,"column_end":52,"is_primary":true,"text":[{"text":"use rust_resource::api::error::{self, ResourceError};","highlight_start":39,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":760,"byte_end":814,"line_start":19,"line_end":20,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::api::error::{self, ResourceError};","highlight_start":1,"highlight_end":54},{"text":"use rust_resource::api::resource_manager::ResourceManager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ResourceError` and `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:19:33\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::api::error::{self, ResourceError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::api::resource_manager::ResourceManager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":818,"byte_end":871,"line_start":20,"line_end":20,"column_start":5,"column_end":58,"is_primary":true,"text":[{"text":"use rust_resource::api::resource_manager::ResourceManager;","highlight_start":5,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":814,"byte_end":873,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::api::resource_manager::ResourceManager;","highlight_start":1,"highlight_end":59},{"text":"use rust_resource::cache;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::api::resource_manager::ResourceManager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::api::resource_manager::ResourceManager;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::cache`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":877,"byte_end":897,"line_start":21,"line_end":21,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use rust_resource::cache;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":873,"byte_end":899,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::cache;","highlight_start":1,"highlight_end":26},{"text":"use rust_resource::cache::error::DatabaseError;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::cache`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:21:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::cache;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::cache::error::DatabaseError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":903,"byte_end":945,"line_start":22,"line_end":22,"column_start":5,"column_end":47,"is_primary":true,"text":[{"text":"use rust_resource::cache::error::DatabaseError;","highlight_start":5,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":899,"byte_end":947,"line_start":22,"line_end":23,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::cache::error::DatabaseError;","highlight_start":1,"highlight_end":48},{"text":"use rust_resource::handlers::time_handler::TimeHandler;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::cache::error::DatabaseError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:22:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::cache::error::DatabaseError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":4,"byte_end":23,"line_start":1,"line_end":1,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":0,"byte_end":25,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::utils::steps_utils::get_resource_list_from_steps_for_relation`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":81,"byte_end":149,"line_start":4,"line_end":4,"column_start":5,"column_end":73,"is_primary":true,"text":[{"text":"use crate::utils::steps_utils::get_resource_list_from_steps_for_relation;","highlight_start":5,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":77,"byte_end":151,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::utils::steps_utils::get_resource_list_from_steps_for_relation;","highlight_start":1,"highlight_end":74},{"text":"use crate::{DataHolder, MyWorld};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::utils::steps_utils::get_resource_list_from_steps_for_relation`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::steps_utils::get_resource_list_from_steps_for_relation;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DataHolder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":163,"byte_end":173,"line_start":5,"line_end":5,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":163,"byte_end":175,"line_start":5,"line_end":5,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":162,"byte_end":163,"line_start":5,"line_end":5,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":182,"byte_end":183,"line_start":5,"line_end":5,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use crate::{DataHolder, MyWorld};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DataHolder`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:5:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{DataHolder, MyWorld};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `cucumber::gherkin::Step`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":189,"byte_end":212,"line_start":6,"line_end":6,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":185,"byte_end":214,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":1,"highlight_end":29},{"text":"use cucumber::{given, then, when};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `cucumber::gherkin::Step`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::gherkin::Step;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `when`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":242,"byte_end":246,"line_start":7,"line_end":7,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":240,"byte_end":246,"line_start":7,"line_end":7,"column_start":27,"column_end":33,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":27,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `when`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:7:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{given, then, when};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `chrono::format`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":132,"byte_end":146,"line_start":4,"line_end":4,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use chrono::format;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":128,"byte_end":148,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::format;","highlight_start":1,"highlight_end":20},{"text":"use cucumber::gherkin::Step;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `chrono::format`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::format;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `when`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":205,"byte_end":209,"line_start":6,"line_end":6,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":203,"byte_end":209,"line_start":6,"line_end":6,"column_start":27,"column_end":33,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":27,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `when`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:6:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{given, then, when};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `mockall::predicate::le`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":216,"byte_end":238,"line_start":7,"line_end":7,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"use mockall::predicate::le;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":212,"byte_end":240,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use mockall::predicate::le;","highlight_start":1,"highlight_end":28},{"text":"use mry::ArgMatcher::Any;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `mockall::predicate::le`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse mockall::predicate::le;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":270,"byte_end":295,"line_start":9,"line_end":9,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":266,"byte_end":297,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::path::Path;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::path::Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":301,"byte_end":316,"line_start":10,"line_end":10,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use std::path::Path;","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":297,"byte_end":318,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::path::Path;","highlight_start":1,"highlight_end":21},{"text":"use std::vec;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::path::Path`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::path::Path;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `InstallResultHolder` and `InstallResultItem`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":51,"byte_end":70,"line_start":1,"line_end":1,"column_start":52,"column_end":71,"is_primary":true,"text":[{"text":"use super::common_res_call_back::{InstallCallback, InstallResultHolder, InstallResultItem};","highlight_start":52,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":72,"byte_end":89,"line_start":1,"line_end":1,"column_start":73,"column_end":90,"is_primary":true,"text":[{"text":"use super::common_res_call_back::{InstallCallback, InstallResultHolder, InstallResultItem};","highlight_start":73,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":49,"byte_end":89,"line_start":1,"line_end":1,"column_start":50,"column_end":90,"is_primary":true,"text":[{"text":"use super::common_res_call_back::{InstallCallback, InstallResultHolder, InstallResultItem};","highlight_start":50,"highlight_end":90}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":33,"byte_end":34,"line_start":1,"line_end":1,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use super::common_res_call_back::{InstallCallback, InstallResultHolder, InstallResultItem};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":89,"byte_end":90,"line_start":1,"line_end":1,"column_start":90,"column_end":91,"is_primary":true,"text":[{"text":"use super::common_res_call_back::{InstallCallback, InstallResultHolder, InstallResultItem};","highlight_start":90,"highlight_end":91}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `InstallResultHolder` and `InstallResultItem`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/combine_steps.rs:1:52\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse super::common_res_call_back::{InstallCallback, InstallResultHolder, InstallResultItem};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `cucumber::gherkin::Step`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":213,"byte_end":236,"line_start":5,"line_end":5,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":209,"byte_end":238,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use cucumber::gherkin::Step;","highlight_start":1,"highlight_end":29},{"text":"use cucumber::{given, then, when};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `cucumber::gherkin::Step`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/combine_steps.rs:5:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::gherkin::Step;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `given` and `then`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":253,"byte_end":258,"line_start":6,"line_end":6,"column_start":16,"column_end":21,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":16,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":260,"byte_end":264,"line_start":6,"line_end":6,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":253,"byte_end":266,"line_start":6,"line_end":6,"column_start":16,"column_end":29,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":16,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":252,"byte_end":253,"line_start":6,"line_end":6,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":270,"byte_end":271,"line_start":6,"line_end":6,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"use cucumber::{given, then, when};","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `given` and `then`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/combine_steps.rs:6:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{given, then, when};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ResourceInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":387,"byte_end":399,"line_start":8,"line_end":8,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"use rust_resource::models::resource_info::{ResourceFilter, ResourceInfo, ResourceType};","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":385,"byte_end":399,"line_start":8,"line_end":8,"column_start":58,"column_end":72,"is_primary":true,"text":[{"text":"use rust_resource::models::resource_info::{ResourceFilter, ResourceInfo, ResourceType};","highlight_start":58,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ResourceInfo`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/combine_steps.rs:8:60\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::models::resource_info::{ResourceFilter, ResourceInfo, ResourceType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::cache::database::ResourceDatabase`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":332,"byte_end":380,"line_start":9,"line_end":9,"column_start":5,"column_end":53,"is_primary":true,"text":[{"text":"use rust_resource::cache::database::ResourceDatabase;","highlight_start":5,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":328,"byte_end":382,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::cache::database::ResourceDatabase;","highlight_start":1,"highlight_end":54},{"text":"use rust_resource::cache::error::DatabaseError;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::cache::database::ResourceDatabase`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::cache::database::ResourceDatabase;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::cache::error::DatabaseError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":386,"byte_end":428,"line_start":10,"line_end":10,"column_start":5,"column_end":47,"is_primary":true,"text":[{"text":"use rust_resource::cache::error::DatabaseError;","highlight_start":5,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":382,"byte_end":430,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::cache::error::DatabaseError;","highlight_start":1,"highlight_end":48},{"text":"use rust_resource::handlers::file_handler::SystemFileError;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::cache::error::DatabaseError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::cache::error::DatabaseError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::handlers::file_handler::SystemFileError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":434,"byte_end":488,"line_start":11,"line_end":11,"column_start":5,"column_end":59,"is_primary":true,"text":[{"text":"use rust_resource::handlers::file_handler::SystemFileError;","highlight_start":5,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":430,"byte_end":490,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::handlers::file_handler::SystemFileError;","highlight_start":1,"highlight_end":60},{"text":"use rust_resource::models::resource_info::{ResourceInfo, ResourceType};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::handlers::file_handler::SystemFileError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::handlers::file_handler::SystemFileError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `mry::ArgMatcher::Any`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":278,"byte_end":298,"line_start":9,"line_end":9,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use mry::ArgMatcher::Any;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":274,"byte_end":300,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use mry::ArgMatcher::Any;","highlight_start":1,"highlight_end":26},{"text":"use rust_resource::api::resource_manager::ResourceManager;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `mry::ArgMatcher::Any`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/preset_steps.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse mry::ArgMatcher::Any;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::handlers::file_handler::SystemFileError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":363,"byte_end":417,"line_start":11,"line_end":11,"column_start":5,"column_end":59,"is_primary":true,"text":[{"text":"use rust_resource::handlers::file_handler::SystemFileError;","highlight_start":5,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":359,"byte_end":419,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::handlers::file_handler::SystemFileError;","highlight_start":1,"highlight_end":60},{"text":"use rust_resource::models::resource_info::ResourceType;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::handlers::file_handler::SystemFileError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/preset_steps.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::handlers::file_handler::SystemFileError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::pipelines::error::PipelineError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":479,"byte_end":525,"line_start":13,"line_end":13,"column_start":5,"column_end":51,"is_primary":true,"text":[{"text":"use rust_resource::pipelines::error::PipelineError;","highlight_start":5,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":475,"byte_end":527,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::pipelines::error::PipelineError;","highlight_start":1,"highlight_end":52},{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::pipelines::error::PipelineError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/preset_steps.rs:13:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::pipelines::error::PipelineError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `super::task_steps::record_install_result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":615,"byte_end":655,"line_start":17,"line_end":17,"column_start":5,"column_end":45,"is_primary":true,"text":[{"text":"use super::task_steps::record_install_result;","highlight_start":5,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/preset_steps.rs","byte_start":611,"byte_end":657,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use super::task_steps::record_install_result;","highlight_start":1,"highlight_end":46},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `super::task_steps::record_install_result`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/preset_steps.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse super::task_steps::record_install_result;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ResourceError`, `handlers::relation_handler::MockRelationHandler`, `models::resource_info::ResourceInfo`, and `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":461,"byte_end":465,"line_start":12,"line_end":12,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    api::error::{self, ResourceError},","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":467,"byte_end":480,"line_start":12,"line_end":12,"column_start":24,"column_end":37,"is_primary":true,"text":[{"text":"    api::error::{self, ResourceError},","highlight_start":24,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":487,"byte_end":534,"line_start":13,"line_end":13,"column_start":5,"column_end":52,"is_primary":true,"text":[{"text":"    handlers::relation_handler::MockRelationHandler,","highlight_start":5,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":540,"byte_end":575,"line_start":14,"line_end":14,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    models::resource_info::ResourceInfo,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":423,"byte_end":580,"line_start":11,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rust_resource::{","highlight_start":1,"highlight_end":21},{"text":"    api::error::{self, ResourceError},","highlight_start":1,"highlight_end":39},{"text":"    handlers::relation_handler::MockRelationHandler,","highlight_start":1,"highlight_end":53},{"text":"    models::resource_info::ResourceInfo,","highlight_start":1,"highlight_end":41},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ResourceError`, `handlers::relation_handler::MockRelationHandler`, `models::resource_info::ResourceInfo`, and `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:12:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    api::error::{self, ResourceError},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    handlers::relation_handler::MockRelationHandler,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    models::resource_info::ResourceInfo,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `get_request_resource_list_from_steps`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/request_steps.rs","byte_start":173,"byte_end":209,"line_start":4,"line_end":4,"column_start":5,"column_end":41,"is_primary":true,"text":[{"text":"    get_request_resource_list_from_steps, get_resource_list_from_steps_for_task,","highlight_start":5,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/request_steps.rs","byte_start":167,"byte_end":209,"line_start":3,"line_end":4,"column_start":84,"column_end":41,"is_primary":true,"text":[{"text":"    combine_device_resource_condition, combine_normal_condition, get_condition_dict,","highlight_start":84,"highlight_end":85},{"text":"    get_request_resource_list_from_steps, get_resource_list_from_steps_for_task,","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `get_request_resource_list_from_steps`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/request_steps.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    get_request_resource_list_from_steps, get_resource_list_from_steps_for_task,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `e`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":3551,"byte_end":3552,"line_start":92,"line_end":92,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"        Err(e) => {","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":3551,"byte_end":3552,"line_start":92,"line_end":92,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"        Err(e) => {","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"_e","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `e`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:92:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Err(e) => {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_e`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":3767,"byte_end":3772,"line_start":99,"line_end":99,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"async fn then_clean_all_data(world: &mut MyWorld, time_str: String) {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":3767,"byte_end":3772,"line_start":99,"line_end":99,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"async fn then_clean_all_data(world: &mut MyWorld, time_str: String) {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:99:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m99\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn then_clean_all_data(world: &mut MyWorld, time_str: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":4428,"byte_end":4433,"line_start":116,"line_end":116,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"async fn then_delete_file_times(world: &mut MyWorld, time_str: String) {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":4428,"byte_end":4433,"line_start":116,"line_end":116,"column_start":33,"column_end":38,"is_primary":true,"text":[{"text":"async fn then_delete_file_times(world: &mut MyWorld, time_str: String) {","highlight_start":33,"highlight_end":38}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:116:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn then_delete_file_times(world: &mut MyWorld, time_str: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":4797,"byte_end":4802,"line_start":125,"line_end":125,"column_start":44,"column_end":49,"is_primary":true,"text":[{"text":"async fn then_delete_file_times_with_paths(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":44,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":4797,"byte_end":4802,"line_start":125,"line_end":125,"column_start":44,"column_end":49,"is_primary":true,"text":[{"text":"async fn then_delete_file_times_with_paths(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":44,"highlight_end":49}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:125:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn then_delete_file_times_with_paths(world: &mut MyWorld, time_str: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `err`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/request_steps.rs","byte_start":1450,"byte_end":1453,"line_start":31,"line_end":31,"column_start":21,"column_end":24,"is_primary":true,"text":[{"text":"                Err(err) => {}","highlight_start":21,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/request_steps.rs","byte_start":1450,"byte_end":1453,"line_start":31,"line_end":31,"column_start":21,"column_end":24,"is_primary":true,"text":[{"text":"                Err(err) => {}","highlight_start":21,"highlight_end":24}],"label":null,"suggested_replacement":"_err","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `err`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/request_steps.rs:31:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Err(err) => {}\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_err`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `err`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/request_steps.rs","byte_start":1955,"byte_end":1958,"line_start":42,"line_end":42,"column_start":21,"column_end":24,"is_primary":true,"text":[{"text":"                Err(err) => {}","highlight_start":21,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/request_steps.rs","byte_start":1955,"byte_end":1958,"line_start":42,"line_end":42,"column_start":21,"column_end":24,"is_primary":true,"text":[{"text":"                Err(err) => {}","highlight_start":21,"highlight_end":24}],"label":null,"suggested_replacement":"_err","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `err`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/request_steps.rs:42:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Err(err) => {}\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_err`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `World`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":672,"byte_end":677,"line_start":15,"line_end":15,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"use cucumber::{given, then, World};","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `World`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:15:29\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cucumber::{given, then, World};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ResourceFilter`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/combine_steps.rs","byte_start":371,"byte_end":385,"line_start":8,"line_end":8,"column_start":44,"column_end":58,"is_primary":true,"text":[{"text":"use rust_resource::models::resource_info::{ResourceFilter, ResourceInfo, ResourceType};","highlight_start":44,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ResourceFilter`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/combine_steps.rs:8:44\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::models::resource_info::{ResourceFilter, ResourceInfo, ResourceType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rust_resource::handlers::relation_handler::RelationHandler`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":363,"byte_end":421,"line_start":10,"line_end":10,"column_start":5,"column_end":63,"is_primary":true,"text":[{"text":"use rust_resource::handlers::relation_handler::RelationHandler;","highlight_start":5,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rust_resource::handlers::relation_handler::RelationHandler`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rust_resource::handlers::relation_handler::RelationHandler;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":1116,"byte_end":1121,"line_start":25,"line_end":25,"column_start":34,"column_end":39,"is_primary":true,"text":[{"text":"fn given_deployed_interface_list(world: &mut MyWorld, interface: String, step: &Step) {","highlight_start":34,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":1116,"byte_end":1121,"line_start":25,"line_end":25,"column_start":34,"column_end":39,"is_primary":true,"text":[{"text":"fn given_deployed_interface_list(world: &mut MyWorld, interface: String, step: &Step) {","highlight_start":34,"highlight_end":39}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:25:34\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_deployed_interface_list(world: &mut MyWorld, interface: String, step: &Step) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":1497,"byte_end":1502,"line_start":32,"line_end":32,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"fn given_database_query_condition(world: &mut MyWorld, condition_str: String, step: &Step) {","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":1497,"byte_end":1502,"line_start":32,"line_end":32,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"fn given_database_query_condition(world: &mut MyWorld, condition_str: String, step: &Step) {","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:32:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_database_query_condition(world: &mut MyWorld, condition_str: String, step: &Step) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":2640,"byte_end":2641,"line_start":61,"line_end":61,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        .returns_with(|a| Ok(vec![])); ","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":2640,"byte_end":2641,"line_start":61,"line_end":61,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"        .returns_with(|a| Ok(vec![])); ","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:61:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .returns_with(|a| Ok(vec![])); \u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":2828,"byte_end":2833,"line_start":67,"line_end":67,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":2828,"byte_end":2833,"line_start":67,"line_end":67,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:67:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `condition_str`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":2895,"byte_end":2908,"line_start":70,"line_end":70,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    condition_str: String,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":2895,"byte_end":2908,"line_start":70,"line_end":70,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    condition_str: String,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_condition_str","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `condition_str`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:70:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    condition_str: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_condition_str`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":3093,"byte_end":3098,"line_start":77,"line_end":77,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/data_source_steps.rs","byte_start":3093,"byte_end":3098,"line_start":77,"line_end":77,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/data_source_steps.rs:77:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":1265,"byte_end":1270,"line_start":27,"line_end":27,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"fn then_check_database_query_all_resource_called_times(world: &mut MyWorld, times: String) {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":1265,"byte_end":1270,"line_start":27,"line_end":27,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"fn then_check_database_query_all_resource_called_times(world: &mut MyWorld, times: String) {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:27:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_check_database_query_all_resource_called_times(world: &mut MyWorld, times: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":1585,"byte_end":1590,"line_start":35,"line_end":35,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"fn given_database_query_all_resource_result(world: &mut MyWorld, result: String) {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":1585,"byte_end":1590,"line_start":35,"line_end":35,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"fn given_database_query_all_resource_result(world: &mut MyWorld, result: String) {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:35:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_database_query_all_resource_result(world: &mut MyWorld, result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":2121,"byte_end":2126,"line_start":48,"line_end":48,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":2121,"byte_end":2126,"line_start":48,"line_end":48,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:48:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3095,"byte_end":3100,"line_start":77,"line_end":77,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3095,"byte_end":3100,"line_start":77,"line_end":77,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:77:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `resource_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3666,"byte_end":3679,"line_start":98,"line_end":98,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let resource_type = match type_str.as_ref() {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3666,"byte_end":3679,"line_start":98,"line_end":98,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let resource_type = match type_str.as_ref() {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_resource_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `resource_type`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:98:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let resource_type = match type_str.as_ref() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_resource_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3576,"byte_end":3581,"line_start":93,"line_end":93,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3576,"byte_end":3581,"line_start":93,"line_end":93,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:93:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m93\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3601,"byte_end":3605,"line_start":94,"line_end":94,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    name: String,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":3601,"byte_end":3605,"line_start":94,"line_end":94,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    name: String,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `name`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:94:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    name: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":4740,"byte_end":4745,"line_start":127,"line_end":127,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":4740,"byte_end":4745,"line_start":127,"line_end":127,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:127:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5062,"byte_end":5063,"line_start":137,"line_end":137,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5062,"byte_end":5063,"line_start":137,"line_end":137,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:137:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `b`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5065,"byte_end":5066,"line_start":137,"line_end":137,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5065,"byte_end":5066,"line_start":137,"line_end":137,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"_b","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `b`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:137:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_b`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `c`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5068,"byte_end":5069,"line_start":137,"line_end":137,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5068,"byte_end":5069,"line_start":137,"line_end":137,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"_c","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `c`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:137:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_c`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5746,"byte_end":5747,"line_start":149,"line_end":149,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5746,"byte_end":5747,"line_start":149,"line_end":149,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:149:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `b`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5749,"byte_end":5750,"line_start":149,"line_end":149,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5749,"byte_end":5750,"line_start":149,"line_end":149,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"_b","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `b`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:149:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_b`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `c`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5752,"byte_end":5753,"line_start":149,"line_end":149,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5752,"byte_end":5753,"line_start":149,"line_end":149,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"_c","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `c`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:149:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_c`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5961,"byte_end":5966,"line_start":154,"line_end":154,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"fn given_database_query_by_name_type_result(world: &mut MyWorld, step: &Step) {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":5961,"byte_end":5966,"line_start":154,"line_end":154,"column_start":45,"column_end":50,"is_primary":true,"text":[{"text":"fn given_database_query_by_name_type_result(world: &mut MyWorld, step: &Step) {","highlight_start":45,"highlight_end":50}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:154:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_database_query_by_name_type_result(world: &mut MyWorld, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":6435,"byte_end":6440,"line_start":165,"line_end":165,"column_start":54,"column_end":59,"is_primary":true,"text":[{"text":"fn given_database_query_resource_by_condition_result(world: &mut MyWorld, step: &Step) {","highlight_start":54,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":6435,"byte_end":6440,"line_start":165,"line_end":165,"column_start":54,"column_end":59,"is_primary":true,"text":[{"text":"fn given_database_query_resource_by_condition_result(world: &mut MyWorld, step: &Step) {","highlight_start":54,"highlight_end":59}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:165:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m165\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_database_query_resource_by_condition_result(world: &mut MyWorld, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":6776,"byte_end":6781,"line_start":173,"line_end":173,"column_start":60,"column_end":65,"is_primary":true,"text":[{"text":"fn then_check_database_query_by_name_and_type_called_times(world: &mut MyWorld, times: String) {","highlight_start":60,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":6776,"byte_end":6781,"line_start":173,"line_end":173,"column_start":60,"column_end":65,"is_primary":true,"text":[{"text":"fn then_check_database_query_by_name_and_type_called_times(world: &mut MyWorld, times: String) {","highlight_start":60,"highlight_end":65}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:173:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_check_database_query_by_name_and_type_called_times(world: &mut MyWorld, times: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":7202,"byte_end":7207,"line_start":182,"line_end":182,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":7202,"byte_end":7207,"line_start":182,"line_end":182,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:182:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":7996,"byte_end":8001,"line_start":205,"line_end":205,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":7996,"byte_end":8001,"line_start":205,"line_end":205,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:205:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `condition` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":9576,"byte_end":9585,"line_start":254,"line_end":254,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut condition = \"\".to_string();","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `condition` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:254:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut condition = \"\".to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":9401,"byte_end":9406,"line_start":247,"line_end":247,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":9401,"byte_end":9406,"line_start":247,"line_end":247,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:247:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":10647,"byte_end":10652,"line_start":280,"line_end":280,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":10647,"byte_end":10652,"line_start":280,"line_end":280,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:280:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `condition`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":10691,"byte_end":10700,"line_start":282,"line_end":282,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    condition: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":10691,"byte_end":10700,"line_start":282,"line_end":282,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    condition: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_condition","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `condition`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:282:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    condition: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_condition`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":11020,"byte_end":11025,"line_start":292,"line_end":292,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"fn then_check_database_query_called_times(world: &mut MyWorld, times: String) {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":11020,"byte_end":11025,"line_start":292,"line_end":292,"column_start":43,"column_end":48,"is_primary":true,"text":[{"text":"fn then_check_database_query_called_times(world: &mut MyWorld, times: String) {","highlight_start":43,"highlight_end":48}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:292:43\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_check_database_query_called_times(world: &mut MyWorld, times: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `resource`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":12474,"byte_end":12482,"line_start":334,"line_end":334,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let resource = exp_resource_list.first().unwrap().clone();","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":12474,"byte_end":12482,"line_start":334,"line_end":334,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let resource = exp_resource_list.first().unwrap().clone();","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_resource","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `resource`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:334:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m334\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let resource = exp_resource_list.first().unwrap().clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_resource`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":12299,"byte_end":12304,"line_start":331,"line_end":331,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"fn then_update_datebase_resource_list(world: &mut MyWorld, times: String, step: &Step) {","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":12299,"byte_end":12304,"line_start":331,"line_end":331,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"fn then_update_datebase_resource_list(world: &mut MyWorld, times: String, step: &Step) {","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:331:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m331\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_update_datebase_resource_list(world: &mut MyWorld, times: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":12775,"byte_end":12780,"line_start":342,"line_end":342,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"fn given_database_query_condition_result(world: &mut MyWorld, result: String) {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":12775,"byte_end":12780,"line_start":342,"line_end":342,"column_start":42,"column_end":47,"is_primary":true,"text":[{"text":"fn given_database_query_condition_result(world: &mut MyWorld, result: String) {","highlight_start":42,"highlight_end":47}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:342:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m342\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_database_query_condition_result(world: &mut MyWorld, result: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13050,"byte_end":13051,"line_start":348,"line_end":348,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13050,"byte_end":13051,"line_start":348,"line_end":348,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:348:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a: String, b, c| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `b`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13061,"byte_end":13062,"line_start":348,"line_end":348,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13061,"byte_end":13062,"line_start":348,"line_end":348,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"_b","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `b`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:348:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a: String, b, c| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_b`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `c`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13064,"byte_end":13065,"line_start":348,"line_end":348,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13064,"byte_end":13065,"line_start":348,"line_end":348,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"_c","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `c`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:348:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a: String, b, c| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_c`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13429,"byte_end":13430,"line_start":358,"line_end":358,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13429,"byte_end":13430,"line_start":358,"line_end":358,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:358:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a: String, b, c| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `b`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13440,"byte_end":13441,"line_start":358,"line_end":358,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13440,"byte_end":13441,"line_start":358,"line_end":358,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"_b","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `b`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:358:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a: String, b, c| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_b`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `c`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13443,"byte_end":13444,"line_start":358,"line_end":358,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13443,"byte_end":13444,"line_start":358,"line_end":358,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"            .returns_with(|a: String, b, c| {","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"_c","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `c`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:358:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m358\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a: String, b, c| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_c`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13691,"byte_end":13696,"line_start":365,"line_end":365,"column_start":50,"column_end":55,"is_primary":true,"text":[{"text":"fn given_database_query_resource_by_query_result(world: &mut MyWorld, result: String) {","highlight_start":50,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":13691,"byte_end":13696,"line_start":365,"line_end":365,"column_start":50,"column_end":55,"is_primary":true,"text":[{"text":"fn given_database_query_resource_by_query_result(world: &mut MyWorld, result: String) {","highlight_start":50,"highlight_end":55}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:365:50\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m365\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_database_query_resource_by_query_result(world: &mut MyWorld, result: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":14849,"byte_end":14854,"line_start":397,"line_end":397,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":14849,"byte_end":14854,"line_start":397,"line_end":397,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:397:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m397\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":25698,"byte_end":25703,"line_start":707,"line_end":707,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"fn verify_delete_resource_list_times(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":25698,"byte_end":25703,"line_start":707,"line_end":707,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"fn verify_delete_resource_list_times(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:707:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m707\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn verify_delete_resource_list_times(world: &mut MyWorld, time_str: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":26227,"byte_end":26232,"line_start":718,"line_end":718,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"fn verify_delete_query_condition_times(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":26227,"byte_end":26232,"line_start":718,"line_end":718,"column_start":40,"column_end":45,"is_primary":true,"text":[{"text":"fn verify_delete_query_condition_times(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":40,"highlight_end":45}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:718:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m718\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn verify_delete_query_condition_times(world: &mut MyWorld, time_str: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `step`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":26266,"byte_end":26270,"line_start":718,"line_end":718,"column_start":79,"column_end":83,"is_primary":true,"text":[{"text":"fn verify_delete_query_condition_times(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":79,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":26266,"byte_end":26270,"line_start":718,"line_end":718,"column_start":79,"column_end":83,"is_primary":true,"text":[{"text":"fn verify_delete_query_condition_times(world: &mut MyWorld, time_str: String, step: &Step) {","highlight_start":79,"highlight_end":83}],"label":null,"suggested_replacement":"_step","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `step`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:718:79\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m718\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn verify_delete_query_condition_times(world: &mut MyWorld, time_str: String, step: &Step) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_step`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":360,"byte_end":365,"line_start":11,"line_end":11,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"fn given_download_spend_time(world: &mut MyWorld, sec: String) {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":360,"byte_end":365,"line_start":11,"line_end":11,"column_start":30,"column_end":35,"is_primary":true,"text":[{"text":"fn given_download_spend_time(world: &mut MyWorld, sec: String) {","highlight_start":30,"highlight_end":35}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:11:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_download_spend_time(world: &mut MyWorld, sec: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":686,"byte_end":691,"line_start":20,"line_end":20,"column_start":26,"column_end":31,"is_primary":true,"text":[{"text":"fn given_download_result(world: &mut MyWorld, url: String, result: String) {","highlight_start":26,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/download_steps.rs","byte_start":686,"byte_end":691,"line_start":20,"line_end":20,"column_start":26,"column_end":31,"is_primary":true,"text":[{"text":"fn given_download_result(world: &mut MyWorld, url: String, result: String) {","highlight_start":26,"highlight_end":31}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/download_steps.rs:20:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_download_result(world: &mut MyWorld, url: String, result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":683,"byte_end":688,"line_start":26,"line_end":26,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"fn given_file_system_path(world: &mut MyWorld, is_exist: String, file_type: String, step: &Step) {","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":683,"byte_end":688,"line_start":26,"line_end":26,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"fn given_file_system_path(world: &mut MyWorld, is_exist: String, file_type: String, step: &Step) {","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:26:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_file_system_path(world: &mut MyWorld, is_exist: String, file_type: String, step: &Step) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1258,"byte_end":1263,"line_start":41,"line_end":41,"column_start":26,"column_end":31,"is_primary":true,"text":[{"text":"fn given_file_create_dir(world: &mut MyWorld, result: String) {","highlight_start":26,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1258,"byte_end":1263,"line_start":41,"line_end":41,"column_start":26,"column_end":31,"is_primary":true,"text":[{"text":"fn given_file_create_dir(world: &mut MyWorld, result: String) {","highlight_start":26,"highlight_end":31}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:41:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_file_create_dir(world: &mut MyWorld, result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1481,"byte_end":1486,"line_start":48,"line_end":48,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"fn given_file_md5_hash(world: &mut MyWorld, file_path: String, md5: String) {","highlight_start":24,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1481,"byte_end":1486,"line_start":48,"line_end":48,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"fn given_file_md5_hash(world: &mut MyWorld, file_path: String, md5: String) {","highlight_start":24,"highlight_end":29}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:48:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_file_md5_hash(world: &mut MyWorld, file_path: String, md5: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1727,"byte_end":1732,"line_start":55,"line_end":55,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"fn given_unzip_result(world: &mut MyWorld, file_path: String, result: String) {","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1727,"byte_end":1732,"line_start":55,"line_end":55,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"fn given_unzip_result(world: &mut MyWorld, file_path: String, result: String) {","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:55:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_unzip_result(world: &mut MyWorld, file_path: String, result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1994,"byte_end":1999,"line_start":62,"line_end":62,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"fn given_rename_result(world: &mut MyWorld, src: String, dst: String, result: String) {","highlight_start":24,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":1994,"byte_end":1999,"line_start":62,"line_end":62,"column_start":24,"column_end":29,"is_primary":true,"text":[{"text":"fn given_rename_result(world: &mut MyWorld, src: String, dst: String, result: String) {","highlight_start":24,"highlight_end":29}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:62:24\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_rename_result(world: &mut MyWorld, src: String, dst: String, result: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":2742,"byte_end":2747,"line_start":82,"line_end":82,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"fn given_directory_contents(world: &mut MyWorld, dir: String, path_types: String) {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":2742,"byte_end":2747,"line_start":82,"line_end":82,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"fn given_directory_contents(world: &mut MyWorld, dir: String, path_types: String) {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:82:29\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_directory_contents(world: &mut MyWorld, dir: String, path_types: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":3363,"byte_end":3368,"line_start":99,"line_end":99,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"fn given_directory_contents_by_table(world: &mut MyWorld, dir: String, step: &Step) {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":3363,"byte_end":3368,"line_start":99,"line_end":99,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"fn given_directory_contents_by_table(world: &mut MyWorld, dir: String, step: &Step) {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:99:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m99\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_directory_contents_by_table(world: &mut MyWorld, dir: String, step: &Step) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":4092,"byte_end":4097,"line_start":119,"line_end":119,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"fn then_file_create_dir_called_times(world: &mut MyWorld, times: String, path: String) {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":4092,"byte_end":4097,"line_start":119,"line_end":119,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"fn then_file_create_dir_called_times(world: &mut MyWorld, times: String, path: String) {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:119:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_file_create_dir_called_times(world: &mut MyWorld, times: String, path: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":8799,"byte_end":8804,"line_start":253,"line_end":253,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"fn given_file_delete_result(world: &mut MyWorld, file_path: String, result: String) {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/file_steps.rs","byte_start":8799,"byte_end":8804,"line_start":253,"line_end":253,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"fn given_file_delete_result(world: &mut MyWorld, file_path: String, result: String) {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/file_steps.rs:253:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn given_file_delete_result(world: &mut MyWorld, file_path: String, result: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `flag`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2065,"byte_end":2069,"line_start":55,"line_end":55,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let flag = is_valid_string_equal(&is_preset, \"是\");","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2065,"byte_end":2069,"line_start":55,"line_end":55,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let flag = is_valid_string_equal(&is_preset, \"是\");","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_flag","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `flag`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:55:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m55\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let flag = is_valid_string_equal(&is_preset, \"是\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_flag`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":1956,"byte_end":1961,"line_start":53,"line_end":53,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"fn then_check_relate_called_times(world: &mut MyWorld, times: String, is_preset: String) {","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":1956,"byte_end":1961,"line_start":53,"line_end":53,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"fn then_check_relate_called_times(world: &mut MyWorld, times: String, is_preset: String) {","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:53:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn then_check_relate_called_times(world: &mut MyWorld, times: String, is_preset: String) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `flag`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2583,"byte_end":2587,"line_start":72,"line_end":72,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let flag = is_valid_string_equal(&is_preset, \"是\");","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2583,"byte_end":2587,"line_start":72,"line_end":72,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let flag = is_valid_string_equal(&is_preset, \"是\");","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_flag","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `flag`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:72:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let flag = is_valid_string_equal(&is_preset, \"是\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_flag`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `world`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2447,"byte_end":2452,"line_start":66,"line_end":66,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2447,"byte_end":2452,"line_start":66,"line_end":66,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    world: &mut MyWorld,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_world","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `world`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:66:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    world: &mut MyWorld,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_world`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `step`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2514,"byte_end":2518,"line_start":69,"line_end":69,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    step: &Step,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/releation_steps.rs","byte_start":2514,"byte_end":2518,"line_start":69,"line_end":69,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    step: &Step,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_step","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `step`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/releation_steps.rs:69:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    step: &Step,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_step`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":8946,"byte_end":8947,"line_start":217,"line_end":217,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":8946,"byte_end":8947,"line_start":217,"line_end":217,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:217:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m217\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `b`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":8949,"byte_end":8950,"line_start":217,"line_end":217,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":8949,"byte_end":8950,"line_start":217,"line_end":217,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"_b","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `b`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:217:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m217\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_b`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `c`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":8952,"byte_end":8953,"line_start":217,"line_end":217,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":8952,"byte_end":8953,"line_start":217,"line_end":217,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"_c","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `c`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:217:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m217\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b, c| Ok(ResourceHolder::get_instance().query_list.pop().unwrap()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_c`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":9133,"byte_end":9134,"line_start":221,"line_end":221,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b| {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":9133,"byte_end":9134,"line_start":221,"line_end":221,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"            .returns_with(|a, b| {","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:221:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `b`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":9136,"byte_end":9137,"line_start":221,"line_end":221,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b| {","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":9136,"byte_end":9137,"line_start":221,"line_end":221,"column_start":31,"column_end":32,"is_primary":true,"text":[{"text":"            .returns_with(|a, b| {","highlight_start":31,"highlight_end":32}],"label":null,"suggested_replacement":"_b","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `b`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:221:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(|a, b| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_b`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":17066,"byte_end":17067,"line_start":423,"line_end":423,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"            .returns_with(move |a| {","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":17066,"byte_end":17067,"line_start":423,"line_end":423,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"            .returns_with(move |a| {","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:423:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m423\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(move |a| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":17899,"byte_end":17900,"line_start":449,"line_end":449,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"            .returns_with(move |a| {","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":17899,"byte_end":17900,"line_start":449,"line_end":449,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"            .returns_with(move |a| {","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:449:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m449\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(move |a| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `a`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":18332,"byte_end":18333,"line_start":462,"line_end":462,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"            .returns_with(move |a| {","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":18332,"byte_end":18333,"line_start":462,"line_end":462,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"            .returns_with(move |a| {","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"_a","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `a`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:462:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m462\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .returns_with(move |a| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_a`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `is_preset` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/steps_utils.rs","byte_start":1460,"byte_end":1469,"line_start":43,"line_end":43,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"            let mut is_preset = false;","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `is_preset` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/steps_utils.rs:43:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut is_preset = false;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `is_preset` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/steps_utils.rs","byte_start":5082,"byte_end":5091,"line_start":137,"line_end":137,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"            let mut is_preset = false;","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `is_preset` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/steps_utils.rs:137:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let mut is_preset = false;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `find_resource_by_name_type_version` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/dependence/database_steps.rs","byte_start":8718,"byte_end":8752,"line_start":229,"line_end":229,"column_start":4,"column_end":38,"is_primary":true,"text":[{"text":"fn find_resource_by_name_type_version(","highlight_start":4,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `find_resource_by_name_type_version` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/dependence/database_steps.rs:229:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn find_resource_by_name_type_version(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `get_resource_list_from_preset_configs` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/steps_utils.rs","byte_start":9874,"byte_end":9911,"line_start":257,"line_end":257,"column_start":8,"column_end":45,"is_primary":true,"text":[{"text":"pub fn get_resource_list_from_preset_configs(","highlight_start":8,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `get_resource_list_from_preset_configs` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/steps_utils.rs:257:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn get_resource_list_from_preset_configs(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `get_resource_query_vec` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/steps_utils.rs","byte_start":11857,"byte_end":11879,"line_start":313,"line_end":313,"column_start":8,"column_end":30,"is_primary":true,"text":[{"text":"pub fn get_resource_query_vec(step: &Step) -> Vec<ResourceQuery> {","highlight_start":8,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `get_resource_query_vec` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/steps_utils.rs:313:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m313\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn get_resource_query_vec(step: &Step) -> Vec<ResourceQuery> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `mapVec` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":2633,"byte_end":2639,"line_start":66,"line_end":66,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let mapVec = get_step_name_type(step);","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/steps/local_cache_steps.rs","byte_start":2633,"byte_end":2639,"line_start":66,"line_end":66,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let mapVec = get_step_name_type(step);","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"map_vec","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `mapVec` should have a snake case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/steps/local_cache_steps.rs:66:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mapVec = get_step_name_type(step);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `map_vec`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a mutable reference to mutable static is discouraged","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":3448,"byte_end":3461,"line_start":77,"line_end":77,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":31}],"label":"mutable reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"mutable references to mutable statics are dangerous; it's undefined behavior if any other pointer to the static is used or if any other reference is created for the static while the mutable reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `&raw mut` instead to create a raw pointer","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/resource_holder.rs","byte_start":3448,"byte_end":3453,"line_start":77,"line_end":77,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"        unsafe { &mut INSTANCE }","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":"&raw mut ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a mutable reference to mutable static is discouraged\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/resource_holder.rs:77:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        unsafe { &mut INSTANCE }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mmutable reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: mutable references to mutable statics are dangerous; it's undefined behavior if any other pointer to the static is used or if any other reference is created for the static while the mutable reference lives\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `&raw mut` instead to create a raw pointer\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        unsafe { &\u001b[0m\u001b[0m\u001b[38;5;10mraw \u001b[0m\u001b[0mmut INSTANCE }\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[38;5;10m+++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `ResourceQuery` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/steps_utils.rs","byte_start":12088,"byte_end":12101,"line_start":317,"line_end":317,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"            let ResourceQuery = combine_query(row.get(0).unwrap().to_string());","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"resource_rust/rust_resource/tests/utils/steps_utils.rs","byte_start":12088,"byte_end":12101,"line_start":317,"line_end":317,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"            let ResourceQuery = combine_query(row.get(0).unwrap().to_string());","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":"resource_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable `ResourceQuery` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mresource_rust/rust_resource/tests/utils/steps_utils.rs:317:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m317\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let ResourceQuery = combine_query(row.get(0).unwrap().to_string());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to snake case: `resource_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"127 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 127 warnings emitted\u001b[0m\n\n"}

{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 17883862002600103897, "profile": 7367519227731807215, "path": 16796022283764695770, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zstd-safe-bc40cf87b5eae8fe/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
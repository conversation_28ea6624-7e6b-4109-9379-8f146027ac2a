{"$message_type":"diagnostic","message":"couldn't read `logger/rust_logger/src/generated/logger_generated.rs`: No such file or directory (os error 2)","code":null,"level":"error","spans":[{"file_name":"logger/rust_logger/src/ffi.rs","byte_start":419,"byte_end":460,"line_start":17,"line_end":17,"column_start":5,"column_end":46,"is_primary":true,"text":[{"text":"    include!(\"generated/logger_generated.rs\");","highlight_start":5,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"logger/rust_logger/src/ffi.rs","byte_start":419,"byte_end":460,"line_start":17,"line_end":17,"column_start":5,"column_end":46,"is_primary":false,"text":[{"text":"    include!(\"generated/logger_generated.rs\");","highlight_start":5,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"include!","def_site_span":{"file_name":"/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/src/rust/library/core/src/macros/mod.rs","byte_start":53536,"byte_end":53556,"line_start":1513,"line_end":1513,"column_start":5,"column_end":25,"is_primary":false,"text":[{"text":"    macro_rules! include {","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: couldn't read `logger/rust_logger/src/generated/logger_generated.rs`: No such file or directory (os error 2)\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mlogger/rust_logger/src/ffi.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    include!(\"generated/logger_generated.rs\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error\u001b[0m\n\n"}

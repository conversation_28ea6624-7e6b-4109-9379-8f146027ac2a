{"rustc": 15497389221046826682, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5347358027863023418, "path": 5258966399381635768, "deps": [[555019317135488525, "regex_automata", false, 14135140298966569278], [2779309023524819297, "aho_corasick", false, 5437991011273831000], [9408802513701742484, "regex_syntax", false, 2515871006322500781], [15932120279885307830, "memchr", false, 10747288866485021377]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-b260340bcacf5ce8/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 15497389221046826682, "features": "[\"32-column-tables\", \"default\", \"r2d2\", \"sqlite\", \"with-deprecated\"]", "declared_features": "[\"128-column-tables\", \"32-column-tables\", \"64-column-tables\", \"__with_asan_tests\", \"chrono\", \"default\", \"extras\", \"huge-tables\", \"i-implement-a-third-party-backend-and-opt-into-breaking-changes\", \"ipnet-address\", \"large-tables\", \"mysql\", \"mysql_backend\", \"mysqlclient-src\", \"network-address\", \"numeric\", \"postgres\", \"postgres_backend\", \"pq-src\", \"quickcheck\", \"r2d2\", \"returning_clauses_for_sqlite_3_35\", \"serde_json\", \"sqlite\", \"time\", \"unstable\", \"uuid\", \"with-deprecated\", \"without-deprecated\"]", "target": 17967542459835189317, "profile": 5347358027863023418, "path": 14459625480790577565, "deps": [[6722490998346977199, "r2d2", false, 13268247374806415145], [11640477302413400882, "diesel_derives", false, 407163992368023447], [16675652872862304210, "libsqlite3_sys", false, 1958972575761729130]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/diesel-2c8d8f0c5fb54359/dep-lib-diesel", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 15497389221046826682, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 5347358027863023418, "path": 2786229274095121045, "deps": [[5466618496199522463, "crc32fast", false, 12103303085045123870], [7636735136738807108, "miniz_oxide", false, 16957346546398636939]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-3d1672ab0c1dbbd9/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 15497389221046826682, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 9373674451205098548, "deps": [[1988483478007900009, "unicode_ident", false, 9427152784126922483], [3060637413840920116, "proc_macro2", false, 7608282993562232869], [17990358020177143287, "quote", false, 6760751990977140778]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-e6610938c1ae6eeb/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
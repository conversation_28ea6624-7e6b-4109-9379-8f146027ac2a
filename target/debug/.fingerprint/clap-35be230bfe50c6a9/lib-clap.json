{"rustc": 15497389221046826682, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\", \"wrap_help\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 5129752230091461406, "path": 7014022529426593968, "deps": [[4925398738524877221, "clap_derive", false, 13374147924126010579], [14814905555676593471, "clap_builder", false, 8061127949961731453]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-35be230bfe50c6a9/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
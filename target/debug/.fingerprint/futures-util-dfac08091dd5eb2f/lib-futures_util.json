{"rustc": 15497389221046826682, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 7069439876703407402, "deps": [[5103565458935487, "futures_io", false, 16443705585737626891], [1615478164327904835, "pin_utils", false, 1866736041211598894], [1811549171721445101, "futures_channel", false, 366948916561084832], [1906322745568073236, "pin_project_lite", false, 14947904571586367982], [6955678925937229351, "slab", false, 3040466203498628111], [7013762810557009322, "futures_sink", false, 13819606790738987674], [7620660491849607393, "futures_core", false, 1383777745930229697], [10565019901765856648, "futures_macro", false, 13638103424617020003], [15932120279885307830, "memchr", false, 10747288866485021377], [16240732885093539806, "futures_task", false, 2619731068726926274]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-dfac08091dd5eb2f/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/with_sensitive_data:typical
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="419" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,419 86,419 "/>
<text x="77" y="363" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,363 86,363 "/>
<text x="77" y="307" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,307 86,307 "/>
<text x="77" y="252" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,252 86,252 "/>
<text x="77" y="196" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,196 86,196 "/>
<text x="77" y="140" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,140 86,140 "/>
<text x="77" y="85" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,85 86,85 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="125" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.92
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="125,473 125,478 "/>
<text x="245" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.94
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="245,473 245,478 "/>
<text x="365" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.96
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="365,473 365,478 "/>
<text x="486" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.98
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="486,473 486,478 "/>
<text x="606" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="606,473 606,478 "/>
<text x="726" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="726,473 726,478 "/>
<text x="846" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="846,473 846,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,470 95,470 97,469 98,468 100,467 102,465 103,464 105,462 107,460 109,458 110,456 112,454 114,451 115,448 117,445 119,442 120,439 122,435 124,431 125,427 127,422 129,417 131,413 132,408 134,402 136,397 137,391 139,386 141,380 142,374 144,368 146,362 147,357 149,351 151,345 153,339 154,333 156,327 158,322 159,317 161,311 163,306 164,301 166,297 168,292 169,288 171,284 173,280 175,277 176,273 178,270 180,267 181,264 183,262 185,259 186,257 188,255 190,253 191,251 193,249 195,248 197,246 198,245 200,243 202,242 203,240 205,239 207,237 208,236 210,234 212,233 214,231 215,230 217,228 219,226 220,225 222,223 224,221 225,220 227,218 229,216 230,214 232,213 234,211 236,209 237,208 239,206 241,204 242,203 244,202 246,200 247,199 249,198 251,197 252,196 254,195 256,194 258,193 259,192 261,191 263,190 264,190 266,189 268,188 269,187 271,187 273,186 274,185 276,184 278,183 280,182 281,181 283,180 285,178 286,177 288,175 290,174 291,172 293,170 295,168 296,165 298,163 300,160 302,158 303,155 305,152 307,149 308,146 310,143 312,139 313,136 315,133 317,129 318,126 320,123 322,120 324,117 325,114 327,111 329,109 330,106 332,104 334,102 335,100 337,98 339,97 341,96 342,94 344,94 346,93 347,92 349,92 351,92 352,92 354,92 356,92 357,92 359,93 361,93 363,94 364,94 366,95 368,95 369,96 371,97 373,97 374,98 376,98 378,99 379,100 381,100 383,101 385,101 386,102 388,103 390,103 391,104 393,104 395,105 396,105 398,106 400,106 401,107 403,107 405,108 407,108 408,109 410,109 412,110 413,111 415,112 417,112 418,113 420,114 422,115 423,116 425,117 427,118 429,119 430,120 432,121 434,122 435,124 437,125 439,126 440,127 442,128 444,130 445,131 447,132 449,133 451,135 452,136 454,137 456,138 457,140 459,141 461,142 462,143 464,145 466,146 468,147 469,148 471,150 473,151 474,152 476,154 478,155 479,156 481,158 483,159 484,160 486,161 488,163 490,164 491,165 493,167 495,168 496,170 498,171 500,172 501,174 503,175 505,177 506,178 508,179 510,181 512,182 513,184 515,185 517,187 518,188 520,190 522,191 523,193 525,195 527,196 528,198 530,199 532,201 534,202 535,204 537,205 539,207 540,208 542,210 544,212 545,213 547,215 549,216 550,218 552,220 554,221 556,223 557,225 559,226 561,228 562,230 564,232 566,233 567,235 569,237 571,239 573,241 574,243 576,244 578,246 579,248 581,250 583,252 584,254 586,256 588,258 589,260 591,262 593,264 595,266 596,268 598,270 600,272 601,274 603,276 605,278 606,279 608,281 610,283 611,285 613,286 615,288 617,290 618,291 620,293 622,295 623,296 625,298 627,299 628,300 630,302 632,303 633,304 635,306 637,307 639,308 640,309 642,310 644,312 645,313 647,314 649,315 650,316 652,318 654,319 655,320 657,321 659,323 661,324 662,325 664,326 666,328 667,329 669,330 671,332 672,333 674,334 676,336 677,337 679,338 681,340 683,341 684,343 686,344 688,345 689,347 691,348 693,349 694,351 696,352 698,353 700,355 701,356 703,357 705,358 706,360 708,361 710,362 711,363 713,364 715,366 716,367 718,368 720,369 722,370 723,372 725,373 727,374 728,375 730,377 732,378 733,379 735,380 737,382 738,383 740,384 742,385 744,387 745,388 747,389 749,390 750,391 752,392 754,393 755,394 757,395 759,396 760,397 762,398 764,399 766,400 767,401 769,402 771,403 772,404 774,404 776,405 777,406 779,406 781,407 782,408 784,409 786,409 788,410 789,411 791,411 793,412 794,412 796,413 798,414 799,414 801,415 803,416 804,416 806,417 808,417 810,418 811,418 813,419 815,420 816,420 818,421 820,422 821,422 823,423 825,424 827,424 828,425 830,426 832,426 833,427 835,428 837,428 838,429 840,430 842,431 843,431 845,432 847,433 849,433 850,434 852,434 854,435 855,436 857,436 859,437 860,437 862,438 864,438 865,439 867,439 869,440 871,440 872,441 874,441 876,442 877,442 879,443 881,443 882,443 884,444 886,444 887,445 889,445 891,446 893,446 894,446 896,447 898,447 899,448 901,448 903,449 904,449 906,449 908,450 909,450 911,451 913,451 915,451 916,452 918,452 920,453 921,453 923,453 925,454 926,454 928,454 930,455 932,455 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,301 166,297 168,292 169,288 171,284 173,280 175,277 176,273 178,270 180,267 181,264 183,262 185,259 186,257 188,255 190,253 191,251 193,249 195,248 197,246 198,245 200,243 202,242 203,240 205,239 207,237 208,236 210,234 212,233 214,231 215,230 217,228 219,226 220,225 222,223 224,221 225,220 227,218 229,216 230,214 232,213 234,211 236,209 237,208 239,206 241,204 242,203 244,202 246,200 247,199 249,198 251,197 252,196 254,195 256,194 258,193 259,192 261,191 263,190 264,190 266,189 268,188 269,187 271,187 273,186 274,185 276,184 278,183 280,182 281,181 283,180 285,178 286,177 288,175 290,174 291,172 293,170 295,168 296,165 298,163 300,160 302,158 303,155 305,152 307,149 308,146 310,143 312,139 313,136 315,133 317,129 318,126 320,123 322,120 324,117 325,114 327,111 329,109 330,106 332,104 334,102 335,100 337,98 339,97 341,96 342,94 344,94 346,93 347,92 349,92 351,92 352,92 354,92 356,92 357,92 359,93 361,93 363,94 364,94 366,95 368,95 369,96 371,97 373,97 374,98 376,98 378,99 379,100 381,100 383,101 385,101 386,102 388,103 390,103 391,104 393,104 395,105 396,105 398,106 400,106 401,107 403,107 405,108 407,108 408,109 410,109 412,110 413,111 415,112 417,112 418,113 420,114 422,115 423,116 425,117 427,118 429,119 430,120 432,121 434,122 435,124 437,125 439,126 440,127 442,128 444,130 445,131 447,132 449,133 451,135 452,136 454,137 456,138 457,140 459,141 461,142 462,143 464,145 466,146 468,147 469,148 471,150 473,151 474,152 476,154 478,155 479,156 481,158 483,159 484,160 486,161 488,163 490,164 491,165 493,167 495,168 496,170 498,171 500,172 501,174 503,175 505,177 506,178 508,179 510,181 512,182 513,184 515,185 517,187 518,188 520,190 522,191 523,193 525,195 527,196 528,198 530,199 532,201 534,202 535,204 537,205 539,207 540,208 542,210 544,212 545,213 547,215 549,216 550,218 552,220 554,221 556,223 557,225 559,226 561,228 562,230 564,232 566,233 567,235 569,237 571,239 573,241 574,243 576,244 578,246 579,248 581,250 583,252 584,254 586,256 588,258 589,260 591,262 593,264 595,266 596,268 598,270 600,272 601,274 603,276 605,278 606,279 608,281 610,283 611,285 613,286 615,288 617,290 618,291 620,293 622,295 623,296 625,298 627,299 628,300 630,302 632,303 633,304 635,306 637,307 639,308 640,309 642,310 644,312 645,313 647,314 649,315 650,316 652,318 654,319 655,320 657,321 659,323 661,324 662,325 664,326 666,328 667,329 669,330 671,332 672,333 674,334 676,336 677,337 679,338 681,340 683,341 684,343 686,344 688,345 689,347 691,348 693,349 694,351 696,352 698,353 700,355 701,356 703,357 705,358 706,360 708,361 710,362 711,363 713,364 715,366 716,367 718,368 720,369 722,370 723,372 725,373 727,374 728,375 730,377 732,378 733,379 735,380 737,382 738,383 740,384 742,385 744,387 745,388 747,389 749,390 750,391 752,392 754,393 755,394 757,395 759,396 760,397 762,398 764,399 766,400 767,401 769,402 771,403 772,404 774,404 776,405 777,406 779,406 781,407 782,408 784,409 786,409 788,410 789,411 791,411 793,412 794,412 796,413 798,414 799,414 801,415 803,416 804,416 806,417 808,417 810,418 811,418 813,419 815,420 816,420 818,421 820,422 821,422 823,423 825,424 827,424 828,425 830,426 832,426 833,427 835,428 837,428 838,429 840,430 842,431 843,431 845,432 847,433 849,433 850,434 852,434 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="443,473 443,129 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

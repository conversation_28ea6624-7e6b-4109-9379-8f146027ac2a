<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/with_sensitive_data:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="407" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.001
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,407 86,407 "/>
<text x="77" y="341" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,341 86,341 "/>
<text x="77" y="275" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.003
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,275 86,275 "/>
<text x="77" y="209" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,209 86,209 "/>
<text x="77" y="142" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,142 86,142 "/>
<text x="77" y="76" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,76 86,76 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="93" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="93,473 93,478 "/>
<text x="184" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="184,473 184,478 "/>
<text x="275" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="275,473 275,478 "/>
<text x="367" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="367,473 367,478 "/>
<text x="458" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="458,473 458,478 "/>
<text x="549" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
250
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="549,473 549,478 "/>
<text x="640" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
300
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="640,473 640,478 "/>
<text x="731" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
350
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="731,473 731,478 "/>
<text x="822" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
400
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="822,473 822,478 "/>
<text x="914" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
450
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="914,473 914,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,471 98,471 100,470 102,470 103,469 105,468 107,468 109,466 110,465 112,464 114,462 115,461 117,459 119,456 120,454 122,451 124,448 125,445 127,442 129,438 131,434 132,430 134,426 136,422 137,417 139,412 141,407 142,402 144,398 146,393 147,388 149,383 151,378 153,374 154,369 156,365 158,361 159,357 161,353 163,349 164,346 166,342 168,339 169,336 171,334 173,331 175,328 176,326 178,323 180,321 181,319 183,317 185,315 186,314 188,312 190,311 191,310 193,309 195,308 197,308 198,308 200,308 202,309 203,310 205,311 207,313 208,315 210,317 212,320 214,322 215,325 217,329 219,332 220,336 222,340 224,344 225,348 227,352 229,357 230,361 232,366 234,370 236,375 237,379 239,383 241,388 242,392 244,396 246,400 247,404 249,407 251,410 252,413 254,416 256,418 258,421 259,422 261,423 263,424 264,424 266,424 268,423 269,421 271,419 273,416 274,412 276,408 278,402 280,396 281,390 283,382 285,374 286,365 288,356 290,346 291,335 293,325 295,314 296,303 298,292 300,281 302,270 303,260 305,251 307,242 308,234 310,227 312,220 313,215 315,211 317,209 318,207 320,207 322,208 324,210 325,213 327,217 329,223 330,229 332,236 334,244 335,252 337,261 339,270 341,279 342,288 344,297 346,306 347,314 349,323 351,330 352,337 354,343 356,348 357,353 359,356 361,359 363,361 364,362 366,363 368,362 369,361 371,359 373,357 374,354 376,351 378,348 379,345 381,341 383,338 385,335 386,332 388,329 390,327 391,326 393,325 395,325 396,325 398,326 400,328 401,330 403,333 405,337 407,340 408,345 410,349 412,354 413,359 415,364 417,368 418,373 420,378 422,382 423,387 425,391 427,394 429,397 430,400 432,403 434,405 435,407 437,408 439,409 440,410 442,411 444,412 445,412 447,412 449,413 451,413 452,413 454,413 456,412 457,412 459,412 461,411 462,410 464,409 466,408 468,406 469,403 471,400 473,396 474,392 476,386 478,380 479,373 481,365 483,356 484,347 486,336 488,324 490,312 491,299 493,286 495,272 496,258 498,243 500,229 501,215 503,202 505,189 506,177 508,165 510,155 512,145 513,137 515,130 517,124 518,119 520,115 522,112 523,109 525,108 527,107 528,106 530,105 532,105 534,105 535,104 537,103 539,103 540,102 542,100 544,99 545,97 547,96 549,94 550,93 552,92 554,92 556,92 557,92 559,93 561,95 562,98 564,101 566,106 567,111 569,116 571,122 573,129 574,136 576,144 578,151 579,159 581,167 583,175 584,183 586,190 588,198 589,205 591,212 593,220 595,226 596,233 598,240 600,247 601,253 603,260 605,266 606,273 608,280 610,286 611,293 613,299 615,305 617,312 618,318 620,324 622,329 623,335 625,340 627,344 628,348 630,352 632,356 633,358 635,361 637,362 639,363 640,364 642,364 644,363 645,362 647,360 649,357 650,353 652,349 654,345 655,340 657,334 659,328 661,322 662,315 664,308 666,301 667,293 669,286 671,279 672,272 674,265 676,259 677,252 679,247 681,241 683,237 684,232 686,228 688,225 689,222 691,219 693,218 694,216 696,215 698,215 700,215 701,215 703,216 705,217 706,219 708,221 710,224 711,227 713,231 715,235 716,239 718,244 720,249 722,254 723,259 725,265 727,271 728,277 730,283 732,290 733,296 735,302 737,309 738,315 740,321 742,328 744,334 745,340 747,345 749,351 750,356 752,362 754,367 755,371 757,376 759,380 760,384 762,387 764,390 766,393 767,396 769,398 771,400 772,401 774,402 776,403 777,403 779,403 781,403 782,402 784,402 786,400 788,399 789,398 791,396 793,394 794,392 796,390 798,388 799,386 801,384 803,382 804,380 806,379 808,377 810,376 811,375 813,374 815,373 816,373 818,372 820,372 821,373 823,373 825,374 827,375 828,376 830,377 832,379 833,380 835,382 837,384 838,387 840,389 842,392 843,394 845,397 847,400 849,402 850,405 852,408 854,411 855,414 857,417 859,419 860,422 862,424 864,427 865,429 867,431 869,433 871,435 872,437 874,439 876,440 877,442 879,443 881,444 882,445 884,446 886,446 887,447 889,447 891,447 893,447 894,447 896,447 898,447 899,447 901,447 903,447 904,446 906,446 908,446 909,446 911,446 913,446 915,445 916,445 918,446 920,446 921,446 923,446 925,446 926,447 928,447 930,448 932,448 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,346 166,342 168,339 169,336 171,334 173,331 175,328 176,326 178,323 180,321 181,319 183,317 185,315 186,314 188,312 190,311 191,310 193,309 195,308 197,308 198,308 200,308 202,309 203,310 205,311 207,313 208,315 210,317 212,320 214,322 215,325 217,329 219,332 220,336 222,340 224,344 225,348 227,352 229,357 230,361 232,366 234,370 236,375 237,379 239,383 241,388 242,392 244,396 246,400 247,404 249,407 251,410 252,413 254,416 256,418 258,421 259,422 261,423 263,424 264,424 266,424 268,423 269,421 271,419 273,416 274,412 276,408 278,402 280,396 281,390 283,382 285,374 286,365 288,356 290,346 291,335 293,325 295,314 296,303 298,292 300,281 302,270 303,260 305,251 307,242 308,234 310,227 312,220 313,215 315,211 317,209 318,207 320,207 322,208 324,210 325,213 327,217 329,223 330,229 332,236 334,244 335,252 337,261 339,270 341,279 342,288 344,297 346,306 347,314 349,323 351,330 352,337 354,343 356,348 357,353 359,356 361,359 363,361 364,362 366,363 368,362 369,361 371,359 373,357 374,354 376,351 378,348 379,345 381,341 383,338 385,335 386,332 388,329 390,327 391,326 393,325 395,325 396,325 398,326 400,328 401,330 403,333 405,337 407,340 408,345 410,349 412,354 413,359 415,364 417,368 418,373 420,378 422,382 423,387 425,391 427,394 429,397 430,400 432,403 434,405 435,407 437,408 439,409 440,410 442,411 444,412 445,412 447,412 449,413 451,413 452,413 454,413 456,412 457,412 459,412 461,411 462,410 464,409 466,408 468,406 469,403 471,400 473,396 474,392 476,386 478,380 479,373 481,365 483,356 484,347 486,336 488,324 490,312 491,299 493,286 495,272 496,258 498,243 500,229 501,215 503,202 505,189 506,177 508,165 510,155 512,145 513,137 515,130 517,124 518,119 520,115 522,112 523,109 525,108 527,107 528,106 530,105 532,105 534,105 535,104 537,103 539,103 540,102 542,100 544,99 545,97 547,96 549,94 550,93 552,92 554,92 556,92 557,92 559,93 561,95 562,98 564,101 566,106 567,111 569,116 571,122 573,129 574,136 576,144 578,151 579,159 581,167 583,175 584,183 586,190 588,198 589,205 591,212 593,220 595,226 596,233 598,240 600,247 601,253 603,260 605,266 606,273 608,280 610,286 611,293 613,299 615,305 617,312 618,318 620,324 622,329 623,335 625,340 627,344 628,348 630,352 632,356 633,358 635,361 637,362 639,363 640,364 642,364 644,363 645,362 647,360 649,357 650,353 652,349 654,345 655,340 657,334 659,328 661,322 662,315 664,308 666,301 667,293 669,286 671,279 672,272 674,265 676,259 677,252 679,247 681,241 683,237 684,232 686,228 688,225 689,222 691,219 693,218 694,216 696,215 698,215 700,215 701,215 703,216 705,217 706,219 708,221 710,224 711,227 713,231 715,235 716,239 718,244 720,249 722,254 723,259 725,265 727,271 728,277 730,283 732,290 733,296 735,302 737,309 738,315 740,321 742,328 744,334 745,340 747,345 749,351 750,356 752,362 754,367 755,371 757,376 759,380 760,384 762,387 764,390 766,393 767,396 769,398 771,400 772,401 774,402 776,403 777,403 779,403 781,403 782,402 784,402 786,400 788,399 789,398 791,396 793,394 794,392 796,390 798,388 799,386 801,384 803,382 804,380 806,379 808,377 810,376 811,375 813,374 815,373 816,373 818,372 820,372 821,373 823,373 825,374 827,375 828,376 830,377 832,379 833,380 835,382 837,384 838,387 840,389 842,392 843,394 845,397 847,400 849,402 850,405 852,408 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="561,473 561,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

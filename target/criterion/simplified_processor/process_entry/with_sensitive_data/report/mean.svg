<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/with_sensitive_data:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="430" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,430 86,430 "/>
<text x="77" y="384" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,384 86,384 "/>
<text x="77" y="337" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,337 86,337 "/>
<text x="77" y="291" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,291 86,291 "/>
<text x="77" y="244" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,244 86,244 "/>
<text x="77" y="198" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,198 86,198 "/>
<text x="77" y="151" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,151 86,151 "/>
<text x="77" y="104" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,104 86,104 "/>
<text x="77" y="58" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,58 86,58 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="119" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.92
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="119,473 119,478 "/>
<text x="262" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.94
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="262,473 262,478 "/>
<text x="404" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.96
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="404,473 404,478 "/>
<text x="546" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.98
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="546,473 546,478 "/>
<text x="688" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="688,473 688,478 "/>
<text x="830" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="830,473 830,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,470 95,469 97,467 98,466 100,465 102,464 103,462 105,460 107,458 109,456 110,454 112,452 114,449 115,447 117,444 119,441 120,438 122,434 124,431 125,427 127,423 129,419 131,415 132,410 134,406 136,401 137,397 139,392 141,387 142,382 144,377 146,372 147,367 149,361 151,356 153,351 154,346 156,341 158,336 159,331 161,326 163,321 164,316 166,311 168,307 169,302 171,298 173,293 175,289 176,285 178,281 180,278 181,274 183,271 185,267 186,264 188,261 190,258 191,255 193,253 195,250 197,248 198,245 200,243 202,241 203,239 205,236 207,234 208,233 210,231 212,229 214,227 215,225 217,224 219,222 220,220 222,219 224,217 225,216 227,214 229,213 230,211 232,210 234,209 236,207 237,206 239,205 241,204 242,203 244,202 246,201 247,200 249,199 251,198 252,197 254,197 256,196 258,195 259,194 261,193 263,193 264,192 266,191 268,190 269,189 271,188 273,187 274,186 276,184 278,183 280,182 281,180 283,179 285,177 286,175 288,173 290,171 291,168 293,166 295,164 296,161 298,158 300,155 302,152 303,149 305,146 307,143 308,140 310,137 312,134 313,130 315,127 317,124 318,121 320,118 322,115 324,113 325,110 327,108 329,105 330,103 332,101 334,99 335,98 337,96 339,95 341,94 342,93 344,93 346,92 347,92 349,92 351,92 352,92 354,93 356,93 357,94 359,94 361,95 363,96 364,96 366,97 368,98 369,99 371,100 373,100 374,101 376,102 378,103 379,103 381,104 383,105 385,105 386,106 388,107 390,107 391,108 393,108 395,109 396,110 398,110 400,111 401,112 403,112 405,113 407,114 408,115 410,116 412,117 413,118 415,119 417,120 418,121 420,122 422,123 423,124 425,126 427,127 429,128 430,130 432,131 434,132 435,134 437,135 439,137 440,138 442,140 444,142 445,143 447,145 449,146 451,147 452,149 454,150 456,151 457,153 459,154 461,155 462,156 464,157 466,158 468,158 469,159 471,160 473,161 474,161 476,162 478,163 479,163 481,164 483,165 484,166 486,166 488,167 490,168 491,169 493,170 495,172 496,173 498,174 500,176 501,177 503,178 505,180 506,182 508,183 510,185 512,187 513,188 515,190 517,192 518,193 520,195 522,197 523,198 525,200 527,201 528,203 530,204 532,206 534,208 535,209 537,211 539,212 540,214 542,215 544,217 545,218 547,220 549,221 550,223 552,225 554,226 556,228 557,230 559,231 561,233 562,235 564,237 566,239 567,241 569,243 571,245 573,247 574,249 576,251 578,253 579,255 581,257 583,260 584,262 586,264 588,266 589,268 591,270 593,272 595,274 596,276 598,278 600,280 601,281 603,283 605,285 606,287 608,288 610,290 611,291 613,293 615,294 617,296 618,297 620,299 622,300 623,302 625,303 627,305 628,306 630,308 632,309 633,310 635,312 637,313 639,315 640,316 642,318 644,319 645,321 647,322 649,324 650,325 652,327 654,328 655,330 657,331 659,333 661,334 662,335 664,337 666,338 667,340 669,341 671,342 672,344 674,345 676,347 677,348 679,349 681,351 683,352 684,353 686,355 688,356 689,357 691,359 693,360 694,361 696,362 698,364 700,365 701,366 703,367 705,368 706,369 708,370 710,371 711,373 713,374 715,375 716,376 718,377 720,378 722,378 723,379 725,380 727,381 728,382 730,383 732,384 733,385 735,386 737,387 738,388 740,389 742,390 744,391 745,392 747,393 749,394 750,395 752,396 754,397 755,398 757,399 759,400 760,400 762,401 764,402 766,403 767,404 769,405 771,406 772,407 774,407 776,408 777,409 779,410 781,411 782,411 784,412 786,413 788,414 789,414 791,415 793,416 794,417 796,417 798,418 799,419 801,419 803,420 804,421 806,421 808,422 810,423 811,423 813,424 815,424 816,425 818,426 820,426 821,427 823,428 825,428 827,429 828,429 830,430 832,431 833,431 835,432 837,432 838,433 840,433 842,434 843,435 845,435 847,436 849,436 850,437 852,437 854,438 855,438 857,439 859,439 860,440 862,440 864,441 865,442 867,442 869,443 871,443 872,444 874,444 876,445 877,445 879,446 881,446 882,447 884,447 886,448 887,448 889,449 891,449 893,450 894,450 896,450 898,451 899,451 901,452 903,452 904,452 906,453 908,453 909,453 911,454 913,454 915,454 916,455 918,455 920,455 921,456 923,456 925,456 926,457 928,457 930,457 932,458 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,316 166,311 168,307 169,302 171,298 173,293 175,289 176,285 178,281 180,278 181,274 183,271 185,267 186,264 188,261 190,258 191,255 193,253 195,250 197,248 198,245 200,243 202,241 203,239 205,236 207,234 208,233 210,231 212,229 214,227 215,225 217,224 219,222 220,220 222,219 224,217 225,216 227,214 229,213 230,211 232,210 234,209 236,207 237,206 239,205 241,204 242,203 244,202 246,201 247,200 249,199 251,198 252,197 254,197 256,196 258,195 259,194 261,193 263,193 264,192 266,191 268,190 269,189 271,188 273,187 274,186 276,184 278,183 280,182 281,180 283,179 285,177 286,175 288,173 290,171 291,168 293,166 295,164 296,161 298,158 300,155 302,152 303,149 305,146 307,143 308,140 310,137 312,134 313,130 315,127 317,124 318,121 320,118 322,115 324,113 325,110 327,108 329,105 330,103 332,101 334,99 335,98 337,96 339,95 341,94 342,93 344,93 346,92 347,92 349,92 351,92 352,92 354,93 356,93 357,94 359,94 361,95 363,96 364,96 366,97 368,98 369,99 371,100 373,100 374,101 376,102 378,103 379,103 381,104 383,105 385,105 386,106 388,107 390,107 391,108 393,108 395,109 396,110 398,110 400,111 401,112 403,112 405,113 407,114 408,115 410,116 412,117 413,118 415,119 417,120 418,121 420,122 422,123 423,124 425,126 427,127 429,128 430,130 432,131 434,132 435,134 437,135 439,137 440,138 442,140 444,142 445,143 447,145 449,146 451,147 452,149 454,150 456,151 457,153 459,154 461,155 462,156 464,157 466,158 468,158 469,159 471,160 473,161 474,161 476,162 478,163 479,163 481,164 483,165 484,166 486,166 488,167 490,168 491,169 493,170 495,172 496,173 498,174 500,176 501,177 503,178 505,180 506,182 508,183 510,185 512,187 513,188 515,190 517,192 518,193 520,195 522,197 523,198 525,200 527,201 528,203 530,204 532,206 534,208 535,209 537,211 539,212 540,214 542,215 544,217 545,218 547,220 549,221 550,223 552,225 554,226 556,228 557,230 559,231 561,233 562,235 564,237 566,239 567,241 569,243 571,245 573,247 574,249 576,251 578,253 579,255 581,257 583,260 584,262 586,264 588,266 589,268 591,270 593,272 595,274 596,276 598,278 600,280 601,281 603,283 605,285 606,287 608,288 610,290 611,291 613,293 615,294 617,296 618,297 620,299 622,300 623,302 625,303 627,305 628,306 630,308 632,309 633,310 635,312 637,313 639,315 640,316 642,318 644,319 645,321 647,322 649,324 650,325 652,327 654,328 655,330 657,331 659,333 661,334 662,335 664,337 666,338 667,340 669,341 671,342 672,344 674,345 676,347 677,348 679,349 681,351 683,352 684,353 686,355 688,356 689,357 691,359 693,360 694,361 696,362 698,364 700,365 701,366 703,367 705,368 706,369 708,370 710,371 711,373 713,374 715,375 716,376 718,377 720,378 722,378 723,379 725,380 727,381 728,382 730,383 732,384 733,385 735,386 737,387 738,388 740,389 742,390 744,391 745,392 747,393 749,394 750,395 752,396 754,397 755,398 757,399 759,400 760,400 762,401 764,402 766,403 767,404 769,405 771,406 772,407 774,407 776,408 777,409 779,410 781,411 782,411 784,412 786,413 788,414 789,414 791,415 793,416 794,417 796,417 798,418 799,419 801,419 803,420 804,421 806,421 808,422 810,423 811,423 813,424 815,424 816,425 818,426 820,426 821,427 823,428 825,428 827,429 828,429 830,430 832,431 833,431 835,432 837,432 838,433 840,433 842,434 843,435 845,435 847,436 849,436 850,437 852,437 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="439,473 439,138 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/with_sensitive_data:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="450" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,450 86,450 "/>
<text x="77" y="405" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,405 86,405 "/>
<text x="77" y="361" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,361 86,361 "/>
<text x="77" y="317" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,317 86,317 "/>
<text x="77" y="272" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,272 86,272 "/>
<text x="77" y="228" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,228 86,228 "/>
<text x="77" y="184" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.07
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,184 86,184 "/>
<text x="77" y="139" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,139 86,139 "/>
<text x="77" y="95" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.09
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,95 86,95 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="163" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="163,473 163,478 "/>
<text x="244" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="244,473 244,478 "/>
<text x="325" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
26
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="325,473 325,478 "/>
<text x="406" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
28
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="406,473 406,478 "/>
<text x="487" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="487,473 487,478 "/>
<text x="568" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
32
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="568,473 568,478 "/>
<text x="649" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
34
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="649,473 649,478 "/>
<text x="729" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
36
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="729,473 729,478 "/>
<text x="810" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
38
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="810,473 810,478 "/>
<text x="891" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="891,473 891,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,469 93,468 95,467 97,466 98,465 100,464 102,463 103,461 105,460 107,459 109,458 110,456 112,455 114,454 115,453 117,452 119,450 120,449 122,448 124,447 125,446 127,445 129,444 131,443 132,442 134,441 136,440 137,439 139,438 141,437 142,436 144,435 146,434 147,433 149,431 151,430 153,429 154,428 156,427 158,426 159,426 161,425 163,424 164,423 166,422 168,422 169,421 171,420 173,420 175,419 176,418 178,417 180,417 181,416 183,415 185,413 186,412 188,411 190,409 191,407 193,405 195,403 197,401 198,399 200,397 202,394 203,392 205,390 207,387 208,385 210,383 212,381 214,379 215,377 217,375 219,373 220,372 222,370 224,369 225,368 227,367 229,366 230,365 232,364 234,363 236,362 237,361 239,360 241,359 242,357 244,356 246,355 247,354 249,352 251,351 252,349 254,348 256,346 258,345 259,343 261,342 263,340 264,339 266,337 268,336 269,334 271,333 273,331 274,329 276,327 278,325 280,322 281,320 283,317 285,314 286,311 288,307 290,304 291,300 293,296 295,292 296,288 298,284 300,280 302,276 303,272 305,268 307,265 308,262 310,259 312,256 313,254 315,253 317,252 318,251 320,251 322,251 324,251 325,252 327,254 329,255 330,257 332,259 334,261 335,263 337,265 339,267 341,269 342,270 344,272 346,273 347,274 349,274 351,274 352,274 354,274 356,273 357,272 359,271 361,269 363,268 364,266 366,264 368,262 369,260 371,259 373,257 374,255 376,254 378,252 379,251 381,250 383,250 385,249 386,248 388,248 390,248 391,247 393,247 395,247 396,247 398,246 400,246 401,245 403,245 405,244 407,244 408,243 410,242 412,240 413,239 415,238 417,236 418,235 420,233 422,231 423,230 425,228 427,226 429,225 430,223 432,221 434,219 435,218 437,216 439,214 440,213 442,211 444,209 445,207 447,205 449,203 451,200 452,198 454,195 456,192 457,188 459,185 461,181 462,177 464,173 466,168 468,164 469,159 471,154 473,149 474,144 476,138 478,133 479,128 481,124 483,119 484,115 486,111 488,107 490,104 491,101 493,99 495,97 496,96 498,94 500,94 501,94 503,94 505,94 506,95 508,96 510,97 512,99 513,100 515,102 517,104 518,106 520,108 522,110 523,111 525,113 527,115 528,116 530,117 532,118 534,119 535,120 537,120 539,120 540,120 542,120 544,119 545,118 547,117 549,116 550,115 552,114 554,112 556,111 557,110 559,108 561,107 562,105 564,104 566,103 567,102 569,101 571,100 573,99 574,98 576,98 578,97 579,97 581,97 583,98 584,98 586,99 588,100 589,101 591,103 593,104 595,106 596,108 598,110 600,112 601,114 603,116 605,118 606,119 608,121 610,123 611,124 613,125 615,126 617,127 618,127 620,127 622,127 623,127 625,127 627,127 628,127 630,127 632,127 633,127 635,127 637,127 639,127 640,128 642,129 644,130 645,132 647,133 649,135 650,138 652,140 654,143 655,145 657,148 659,151 661,155 662,158 664,161 666,164 667,168 669,171 671,174 672,178 674,181 676,184 677,188 679,191 681,194 683,197 684,200 686,204 688,207 689,210 691,213 693,216 694,218 696,221 698,224 700,227 701,230 703,232 705,235 706,237 708,240 710,242 711,244 713,246 715,248 716,250 718,252 720,254 722,255 723,257 725,259 727,260 728,261 730,263 732,264 733,265 735,267 737,268 738,270 740,271 742,273 744,275 745,277 747,279 749,281 750,283 752,286 754,288 755,291 757,294 759,296 760,299 762,302 764,305 766,308 767,311 769,314 771,317 772,320 774,323 776,326 777,329 779,332 781,334 782,337 784,340 786,342 788,345 789,347 791,350 793,352 794,354 796,357 798,359 799,361 801,363 803,365 804,367 806,369 808,371 810,373 811,376 813,378 815,380 816,382 818,385 820,387 821,390 823,392 825,395 827,397 828,400 830,403 832,405 833,408 835,410 837,412 838,415 840,417 842,419 843,421 845,423 847,425 849,426 850,428 852,429 854,430 855,432 857,433 859,434 860,435 862,436 864,437 865,438 867,438 869,439 871,440 872,441 874,442 876,443 877,443 879,444 881,445 882,446 884,447 886,448 887,449 889,450 891,451 893,452 894,453 896,455 898,456 899,457 901,458 903,459 904,460 906,461 908,462 909,463 911,464 913,465 915,466 916,466 918,467 920,468 921,469 923,469 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,423 166,422 168,422 169,421 171,420 173,420 175,419 176,418 178,417 180,417 181,416 183,415 185,413 186,412 188,411 190,409 191,407 193,405 195,403 197,401 198,399 200,397 202,394 203,392 205,390 207,387 208,385 210,383 212,381 214,379 215,377 217,375 219,373 220,372 222,370 224,369 225,368 227,367 229,366 230,365 232,364 234,363 236,362 237,361 239,360 241,359 242,357 244,356 246,355 247,354 249,352 251,351 252,349 254,348 256,346 258,345 259,343 261,342 263,340 264,339 266,337 268,336 269,334 271,333 273,331 274,329 276,327 278,325 280,322 281,320 283,317 285,314 286,311 288,307 290,304 291,300 293,296 295,292 296,288 298,284 300,280 302,276 303,272 305,268 307,265 308,262 310,259 312,256 313,254 315,253 317,252 318,251 320,251 322,251 324,251 325,252 327,254 329,255 330,257 332,259 334,261 335,263 337,265 339,267 341,269 342,270 344,272 346,273 347,274 349,274 351,274 352,274 354,274 356,273 357,272 359,271 361,269 363,268 364,266 366,264 368,262 369,260 371,259 373,257 374,255 376,254 378,252 379,251 381,250 383,250 385,249 386,248 388,248 390,248 391,247 393,247 395,247 396,247 398,246 400,246 401,245 403,245 405,244 407,244 408,243 410,242 412,240 413,239 415,238 417,236 418,235 420,233 422,231 423,230 425,228 427,226 429,225 430,223 432,221 434,219 435,218 437,216 439,214 440,213 442,211 444,209 445,207 447,205 449,203 451,200 452,198 454,195 456,192 457,188 459,185 461,181 462,177 464,173 466,168 468,164 469,159 471,154 473,149 474,144 476,138 478,133 479,128 481,124 483,119 484,115 486,111 488,107 490,104 491,101 493,99 495,97 496,96 498,94 500,94 501,94 503,94 505,94 506,95 508,96 510,97 512,99 513,100 515,102 517,104 518,106 520,108 522,110 523,111 525,113 527,115 528,116 530,117 532,118 534,119 535,120 537,120 539,120 540,120 542,120 544,119 545,118 547,117 549,116 550,115 552,114 554,112 556,111 557,110 559,108 561,107 562,105 564,104 566,103 567,102 569,101 571,100 573,99 574,98 576,98 578,97 579,97 581,97 583,98 584,98 586,99 588,100 589,101 591,103 593,104 595,106 596,108 598,110 600,112 601,114 603,116 605,118 606,119 608,121 610,123 611,124 613,125 615,126 617,127 618,127 620,127 622,127 623,127 625,127 627,127 628,127 630,127 632,127 633,127 635,127 637,127 639,127 640,128 642,129 644,130 645,132 647,133 649,135 650,138 652,140 654,143 655,145 657,148 659,151 661,155 662,158 664,161 666,164 667,168 669,171 671,174 672,178 674,181 676,184 677,188 679,191 681,194 683,197 684,200 686,204 688,207 689,210 691,213 693,216 694,218 696,221 698,224 700,227 701,230 703,232 705,235 706,237 708,240 710,242 711,244 713,246 715,248 716,250 718,252 720,254 722,255 723,257 725,259 727,260 728,261 730,263 732,264 733,265 735,267 737,268 738,270 740,271 742,273 744,275 745,277 747,279 749,281 750,283 752,286 754,288 755,291 757,294 759,296 760,299 762,302 764,305 766,308 767,311 769,314 771,317 772,320 774,323 776,326 777,329 779,332 781,334 782,337 784,340 786,342 788,345 789,347 791,350 793,352 794,354 796,357 798,359 799,361 801,363 803,365 804,367 806,369 808,371 810,373 811,376 813,378 815,380 816,382 818,385 820,387 821,390 823,392 825,395 827,397 828,400 830,403 832,405 833,408 835,410 837,412 838,415 840,417 842,419 843,421 845,423 847,425 849,426 850,428 852,429 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="609,473 609,122 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

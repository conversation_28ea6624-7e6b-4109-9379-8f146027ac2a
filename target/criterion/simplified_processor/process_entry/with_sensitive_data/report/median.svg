<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/with_sensitive_data:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="429" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,429 86,429 "/>
<text x="77" y="382" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,382 86,382 "/>
<text x="77" y="335" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,335 86,335 "/>
<text x="77" y="288" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,288 86,288 "/>
<text x="77" y="241" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,241 86,241 "/>
<text x="77" y="194" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,194 86,194 "/>
<text x="77" y="148" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,148 86,148 "/>
<text x="77" y="101" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,101 86,101 "/>
<text x="77" y="54" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
180
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,54 86,54 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="156" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.916
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="156,473 156,478 "/>
<text x="244" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.918
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="244,473 244,478 "/>
<text x="332" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.92
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="332,473 332,478 "/>
<text x="419" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.922
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="419,473 419,478 "/>
<text x="507" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.924
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="507,473 507,478 "/>
<text x="595" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.926
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="595,473 595,478 "/>
<text x="683" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.928
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="683,473 683,478 "/>
<text x="771" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.93
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="771,473 771,478 "/>
<text x="859" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.932
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="859,473 859,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,472 98,471 100,471 102,471 103,471 105,470 107,470 109,470 110,469 112,469 114,468 115,467 117,466 119,465 120,464 122,463 124,461 125,460 127,458 129,456 131,454 132,452 134,450 136,447 137,445 139,442 141,440 142,437 144,435 146,433 147,431 149,429 151,427 153,426 154,425 156,424 158,424 159,423 161,424 163,424 164,425 166,425 168,426 169,428 171,429 173,430 175,432 176,433 178,434 180,435 181,436 183,437 185,438 186,439 188,440 190,440 191,440 193,441 195,441 197,441 198,441 200,441 202,441 203,441 205,441 207,441 208,441 210,441 212,440 214,440 215,440 217,439 219,439 220,439 222,438 224,438 225,438 227,437 229,437 230,437 232,436 234,436 236,436 237,435 239,435 241,435 242,434 244,434 246,433 247,432 249,432 251,431 252,430 254,430 256,429 258,428 259,427 261,426 263,425 264,424 266,423 268,422 269,421 271,420 273,419 274,418 276,417 278,416 280,415 281,414 283,413 285,412 286,411 288,410 290,409 291,408 293,406 295,405 296,403 298,402 300,400 302,398 303,395 305,393 307,390 308,388 310,385 312,382 313,379 315,376 317,373 318,370 320,367 322,365 324,362 325,360 327,358 329,356 330,355 332,354 334,353 335,352 337,352 339,353 341,353 342,354 344,355 346,356 347,357 349,358 351,359 352,360 354,361 356,362 357,363 359,364 361,365 363,365 364,365 366,365 368,365 369,365 371,365 373,364 374,364 376,363 378,362 379,361 381,360 383,359 385,358 386,357 388,356 390,355 391,355 393,354 395,353 396,352 398,352 400,351 401,350 403,350 405,349 407,349 408,348 410,347 412,346 413,345 415,344 417,343 418,342 420,341 422,339 423,337 425,335 427,334 429,332 430,329 432,327 434,325 435,322 437,320 439,317 440,314 442,311 444,309 445,305 447,302 449,299 451,295 452,291 454,287 456,283 457,278 459,273 461,268 462,263 464,258 466,252 468,247 469,242 471,236 473,232 474,227 476,223 478,220 479,217 481,215 483,214 484,214 486,214 488,215 490,217 491,220 493,223 495,227 496,231 498,235 500,239 501,243 503,247 505,251 506,254 508,257 510,260 512,262 513,263 515,265 517,266 518,266 520,266 522,266 523,265 525,264 527,263 528,261 530,259 532,256 534,253 535,250 537,246 539,242 540,237 542,232 544,226 545,220 547,215 549,209 550,203 552,197 554,192 556,187 557,183 559,178 561,175 562,171 564,169 566,166 567,164 569,162 571,160 573,158 574,156 576,154 578,152 579,149 581,146 583,143 584,139 586,135 588,130 589,125 591,120 593,115 595,110 596,106 598,101 600,98 601,95 603,93 605,92 606,92 608,94 610,98 611,103 613,109 615,117 617,126 618,137 620,148 622,161 623,174 625,188 627,202 628,216 630,229 632,243 633,255 635,267 637,278 639,288 640,298 642,306 644,313 645,320 647,325 649,330 650,334 652,338 654,341 655,344 657,346 659,348 661,350 662,351 664,352 666,354 667,354 669,355 671,356 672,356 674,357 676,357 677,358 679,358 681,358 683,359 684,359 686,360 688,361 689,361 691,362 693,363 694,364 696,364 698,365 700,366 701,366 703,367 705,367 706,367 708,367 710,367 711,367 713,366 715,365 716,365 718,363 720,362 722,361 723,359 725,357 727,355 728,353 730,351 732,349 733,347 735,344 737,342 738,340 740,338 742,336 744,334 745,333 747,332 749,331 750,331 752,331 754,331 755,332 757,334 759,336 760,338 762,341 764,344 766,348 767,352 769,356 771,361 772,365 774,370 776,374 777,379 779,383 781,387 782,391 784,394 786,398 788,401 789,403 791,406 793,408 794,410 796,412 798,413 799,415 801,416 803,417 804,418 806,419 808,420 810,421 811,421 813,422 815,422 816,422 818,423 820,423 821,423 823,423 825,423 827,422 828,422 830,422 832,422 833,422 835,422 837,421 838,422 840,422 842,422 843,422 845,423 847,424 849,424 850,425 852,426 854,427 855,429 857,430 859,431 860,432 862,433 864,435 865,436 867,437 869,438 871,439 872,440 874,442 876,442 877,443 879,444 881,445 882,446 884,447 886,447 887,448 889,449 891,449 893,450 894,451 896,451 898,452 899,452 901,453 903,453 904,454 906,454 908,455 909,455 911,455 913,456 915,456 916,456 918,457 920,457 921,458 923,458 925,458 926,459 928,459 930,460 932,460 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,425 166,425 168,426 169,428 171,429 173,430 175,432 176,433 178,434 180,435 181,436 183,437 185,438 186,439 188,440 190,440 191,440 193,441 195,441 197,441 198,441 200,441 202,441 203,441 205,441 207,441 208,441 210,441 212,440 214,440 215,440 217,439 219,439 220,439 222,438 224,438 225,438 227,437 229,437 230,437 232,436 234,436 236,436 237,435 239,435 241,435 242,434 244,434 246,433 247,432 249,432 251,431 252,430 254,430 256,429 258,428 259,427 261,426 263,425 264,424 266,423 268,422 269,421 271,420 273,419 274,418 276,417 278,416 280,415 281,414 283,413 285,412 286,411 288,410 290,409 291,408 293,406 295,405 296,403 298,402 300,400 302,398 303,395 305,393 307,390 308,388 310,385 312,382 313,379 315,376 317,373 318,370 320,367 322,365 324,362 325,360 327,358 329,356 330,355 332,354 334,353 335,352 337,352 339,353 341,353 342,354 344,355 346,356 347,357 349,358 351,359 352,360 354,361 356,362 357,363 359,364 361,365 363,365 364,365 366,365 368,365 369,365 371,365 373,364 374,364 376,363 378,362 379,361 381,360 383,359 385,358 386,357 388,356 390,355 391,355 393,354 395,353 396,352 398,352 400,351 401,350 403,350 405,349 407,349 408,348 410,347 412,346 413,345 415,344 417,343 418,342 420,341 422,339 423,337 425,335 427,334 429,332 430,329 432,327 434,325 435,322 437,320 439,317 440,314 442,311 444,309 445,305 447,302 449,299 451,295 452,291 454,287 456,283 457,278 459,273 461,268 462,263 464,258 466,252 468,247 469,242 471,236 473,232 474,227 476,223 478,220 479,217 481,215 483,214 484,214 486,214 488,215 490,217 491,220 493,223 495,227 496,231 498,235 500,239 501,243 503,247 505,251 506,254 508,257 510,260 512,262 513,263 515,265 517,266 518,266 520,266 522,266 523,265 525,264 527,263 528,261 530,259 532,256 534,253 535,250 537,246 539,242 540,237 542,232 544,226 545,220 547,215 549,209 550,203 552,197 554,192 556,187 557,183 559,178 561,175 562,171 564,169 566,166 567,164 569,162 571,160 573,158 574,156 576,154 578,152 579,149 581,146 583,143 584,139 586,135 588,130 589,125 591,120 593,115 595,110 596,106 598,101 600,98 601,95 603,93 605,92 606,92 608,94 610,98 611,103 613,109 615,117 617,126 618,137 620,148 622,161 623,174 625,188 627,202 628,216 630,229 632,243 633,255 635,267 637,278 639,288 640,298 642,306 644,313 645,320 647,325 649,330 650,334 652,338 654,341 655,344 657,346 659,348 661,350 662,351 664,352 666,354 667,354 669,355 671,356 672,356 674,357 676,357 677,358 679,358 681,358 683,359 684,359 686,360 688,361 689,361 691,362 693,363 694,364 696,364 698,365 700,366 701,366 703,367 705,367 706,367 708,367 710,367 711,367 713,366 715,365 716,365 718,363 720,362 722,361 723,359 725,357 727,355 728,353 730,351 732,349 733,347 735,344 737,342 738,340 740,338 742,336 744,334 745,333 747,332 749,331 750,331 752,331 754,331 755,332 757,334 759,336 760,338 762,341 764,344 766,348 767,352 769,356 771,361 772,365 774,370 776,374 777,379 779,383 781,387 782,391 784,394 786,398 788,401 789,403 791,406 793,408 794,410 796,412 798,413 799,415 801,416 803,417 804,418 806,419 808,420 810,421 811,421 813,422 815,422 816,422 818,423 820,423 821,423 823,423 825,423 827,422 828,422 830,422 832,422 833,422 835,422 837,421 838,422 840,422 842,422 843,422 845,423 847,424 849,424 850,425 852,426 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="558,473 558,181 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

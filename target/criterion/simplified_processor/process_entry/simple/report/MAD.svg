<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/simple:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="429" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,429 86,429 "/>
<text x="77" y="366" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,366 86,366 "/>
<text x="77" y="303" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,303 86,303 "/>
<text x="77" y="240" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,240 86,240 "/>
<text x="77" y="178" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,178 86,178 "/>
<text x="77" y="115" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,115 86,115 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="138" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="138,473 138,478 "/>
<text x="230" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="230,473 230,478 "/>
<text x="323" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="323,473 323,478 "/>
<text x="415" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="415,473 415,478 "/>
<text x="508" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
26
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="508,473 508,478 "/>
<text x="600" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
28
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="600,473 600,478 "/>
<text x="692" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="692,473 692,478 "/>
<text x="785" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
32
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="785,473 785,478 "/>
<text x="877" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
34
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="877,473 877,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,471 92,470 93,469 95,469 97,468 98,468 100,467 102,466 103,466 105,465 107,464 109,463 110,463 112,462 114,461 115,460 117,459 119,459 120,458 122,457 124,456 125,455 127,454 129,453 131,452 132,451 134,450 136,449 137,448 139,447 141,446 142,445 144,444 146,443 147,441 149,440 151,439 153,438 154,437 156,435 158,434 159,433 161,431 163,430 164,429 166,428 168,426 169,425 171,424 173,422 175,421 176,420 178,419 180,418 181,416 183,415 185,414 186,413 188,412 190,411 191,410 193,409 195,408 197,407 198,405 200,404 202,403 203,402 205,400 207,399 208,398 210,396 212,395 214,393 215,392 217,390 219,388 220,386 222,385 224,383 225,381 227,379 229,377 230,375 232,373 234,371 236,369 237,367 239,365 241,362 242,360 244,358 246,356 247,354 249,352 251,349 252,347 254,345 256,342 258,340 259,338 261,336 263,333 264,331 266,328 268,326 269,324 271,321 273,319 274,316 276,314 278,311 280,309 281,306 283,303 285,301 286,298 288,295 290,292 291,289 293,286 295,283 296,279 298,276 300,272 302,269 303,265 305,261 307,257 308,253 310,249 312,245 313,241 315,237 317,233 318,228 320,224 322,220 324,215 325,211 327,206 329,202 330,197 332,192 334,188 335,183 337,178 339,173 341,168 342,163 344,158 346,153 347,148 349,143 351,138 352,133 354,128 356,123 357,119 359,115 361,111 363,108 364,104 366,102 368,99 369,97 371,96 373,94 374,94 376,93 378,93 379,94 381,95 383,96 385,97 386,99 388,101 390,103 391,105 393,108 395,111 396,114 398,117 400,120 401,123 403,127 405,130 407,133 408,137 410,140 412,144 413,147 415,150 417,154 418,157 420,160 422,163 423,165 425,168 427,170 429,173 430,175 432,177 434,179 435,181 437,182 439,184 440,185 442,187 444,188 445,189 447,190 449,191 451,192 452,192 454,193 456,194 457,194 459,195 461,195 462,196 464,196 466,196 468,197 469,197 471,198 473,198 474,199 476,200 478,201 479,202 481,203 483,204 484,206 486,207 488,209 490,211 491,213 493,215 495,218 496,220 498,222 500,224 501,227 503,229 505,231 506,233 508,234 510,236 512,237 513,238 515,239 517,239 518,239 520,239 522,239 523,238 525,237 527,236 528,235 530,234 532,232 534,231 535,230 537,228 539,227 540,226 542,225 544,224 545,223 547,222 549,222 550,221 552,221 554,221 556,221 557,220 559,220 561,220 562,219 564,219 566,218 567,218 569,217 571,216 573,215 574,213 576,212 578,210 579,209 581,207 583,206 584,205 586,204 588,203 589,203 591,202 593,203 595,204 596,205 598,207 600,209 601,212 603,215 605,219 606,223 608,228 610,232 611,237 613,243 615,248 617,253 618,258 620,264 622,269 623,274 625,278 627,283 628,287 630,291 632,295 633,298 635,301 637,304 639,307 640,310 642,313 644,315 645,318 647,320 649,322 650,325 652,327 654,329 655,331 657,334 659,336 661,338 662,340 664,341 666,343 667,345 669,347 671,348 672,350 674,351 676,353 677,354 679,356 681,357 683,359 684,360 686,362 688,363 689,365 691,367 693,368 694,370 696,372 698,374 700,376 701,378 703,380 705,381 706,383 708,385 710,387 711,388 713,390 715,391 716,393 718,394 720,395 722,396 723,397 725,398 727,399 728,400 730,401 732,402 733,403 735,404 737,404 738,405 740,406 742,407 744,408 745,408 747,409 749,410 750,410 752,411 754,412 755,412 757,413 759,413 760,414 762,414 764,415 766,415 767,416 769,416 771,417 772,417 774,418 776,418 777,419 779,419 781,420 782,421 784,421 786,422 788,423 789,424 791,424 793,425 794,426 796,427 798,427 799,428 801,429 803,430 804,430 806,431 808,432 810,432 811,433 813,434 815,434 816,435 818,436 820,436 821,437 823,438 825,438 827,439 828,440 830,441 832,441 833,442 835,443 837,444 838,445 840,446 842,446 843,447 845,448 847,449 849,450 850,451 852,452 854,453 855,454 857,455 859,456 860,456 862,457 864,458 865,459 867,460 869,461 871,462 872,462 874,463 876,464 877,465 879,465 881,466 882,467 884,467 886,468 887,468 889,468 891,469 893,469 894,469 896,470 898,470 899,470 901,470 903,470 904,470 906,471 908,471 909,471 911,471 913,471 915,471 916,471 918,471 920,471 921,471 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,429 166,428 168,426 169,425 171,424 173,422 175,421 176,420 178,419 180,418 181,416 183,415 185,414 186,413 188,412 190,411 191,410 193,409 195,408 197,407 198,405 200,404 202,403 203,402 205,400 207,399 208,398 210,396 212,395 214,393 215,392 217,390 219,388 220,386 222,385 224,383 225,381 227,379 229,377 230,375 232,373 234,371 236,369 237,367 239,365 241,362 242,360 244,358 246,356 247,354 249,352 251,349 252,347 254,345 256,342 258,340 259,338 261,336 263,333 264,331 266,328 268,326 269,324 271,321 273,319 274,316 276,314 278,311 280,309 281,306 283,303 285,301 286,298 288,295 290,292 291,289 293,286 295,283 296,279 298,276 300,272 302,269 303,265 305,261 307,257 308,253 310,249 312,245 313,241 315,237 317,233 318,228 320,224 322,220 324,215 325,211 327,206 329,202 330,197 332,192 334,188 335,183 337,178 339,173 341,168 342,163 344,158 346,153 347,148 349,143 351,138 352,133 354,128 356,123 357,119 359,115 361,111 363,108 364,104 366,102 368,99 369,97 371,96 373,94 374,94 376,93 378,93 379,94 381,95 383,96 385,97 386,99 388,101 390,103 391,105 393,108 395,111 396,114 398,117 400,120 401,123 403,127 405,130 407,133 408,137 410,140 412,144 413,147 415,150 417,154 418,157 420,160 422,163 423,165 425,168 427,170 429,173 430,175 432,177 434,179 435,181 437,182 439,184 440,185 442,187 444,188 445,189 447,190 449,191 451,192 452,192 454,193 456,194 457,194 459,195 461,195 462,196 464,196 466,196 468,197 469,197 471,198 473,198 474,199 476,200 478,201 479,202 481,203 483,204 484,206 486,207 488,209 490,211 491,213 493,215 495,218 496,220 498,222 500,224 501,227 503,229 505,231 506,233 508,234 510,236 512,237 513,238 515,239 517,239 518,239 520,239 522,239 523,238 525,237 527,236 528,235 530,234 532,232 534,231 535,230 537,228 539,227 540,226 542,225 544,224 545,223 547,222 549,222 550,221 552,221 554,221 556,221 557,220 559,220 561,220 562,219 564,219 566,218 567,218 569,217 571,216 573,215 574,213 576,212 578,210 579,209 581,207 583,206 584,205 586,204 588,203 589,203 591,202 593,203 595,204 596,205 598,207 600,209 601,212 603,215 605,219 606,223 608,228 610,232 611,237 613,243 615,248 617,253 618,258 620,264 622,269 623,274 625,278 627,283 628,287 630,291 632,295 633,298 635,301 637,304 639,307 640,310 642,313 644,315 645,318 647,320 649,322 650,325 652,327 654,329 655,331 657,334 659,336 661,338 662,340 664,341 666,343 667,345 669,347 671,348 672,350 674,351 676,353 677,354 679,356 681,357 683,359 684,360 686,362 688,363 689,365 691,367 693,368 694,370 696,372 698,374 700,376 701,378 703,380 705,381 706,383 708,385 710,387 711,388 713,390 715,391 716,393 718,394 720,395 722,396 723,397 725,398 727,399 728,400 730,401 732,402 733,403 735,404 737,404 738,405 740,406 742,407 744,408 745,408 747,409 749,410 750,410 752,411 754,412 755,412 757,413 759,413 760,414 762,414 764,415 766,415 767,416 769,416 771,417 772,417 774,418 776,418 777,419 779,419 781,420 782,421 784,421 786,422 788,423 789,424 791,424 793,425 794,426 796,427 798,427 799,428 801,429 803,430 804,430 806,431 808,432 810,432 811,433 813,434 815,434 816,435 818,436 820,436 821,437 823,438 825,438 827,439 828,440 830,441 832,441 833,442 835,443 837,444 838,445 840,446 842,446 843,447 845,448 847,449 849,450 850,451 852,452 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="494,473 494,217 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

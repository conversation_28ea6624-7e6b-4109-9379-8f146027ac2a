<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/simple:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="418" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,418 86,418 "/>
<text x="77" y="362" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,362 86,362 "/>
<text x="77" y="307" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,307 86,307 "/>
<text x="77" y="252" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,252 86,252 "/>
<text x="77" y="197" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,197 86,197 "/>
<text x="77" y="142" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,142 86,142 "/>
<text x="77" y="86" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.014
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,86 86,86 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="97" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="97,473 97,478 "/>
<text x="227" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="227,473 227,478 "/>
<text x="357" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="357,473 357,478 "/>
<text x="487" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="487,473 487,478 "/>
<text x="617" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="617,473 617,478 "/>
<text x="747" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
250
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="747,473 747,478 "/>
<text x="878" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
300
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="878,473 878,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,471 97,471 98,470 100,470 102,469 103,469 105,468 107,467 109,466 110,464 112,463 114,461 115,459 117,457 119,455 120,452 122,449 124,445 125,442 127,438 129,433 131,429 132,424 134,418 136,412 137,407 139,400 141,394 142,388 144,381 146,374 147,368 149,361 151,355 153,349 154,343 156,338 158,333 159,329 161,325 163,321 164,319 166,317 168,316 169,315 171,315 173,316 175,318 176,320 178,322 180,325 181,329 183,332 185,336 186,340 188,344 190,348 191,352 193,356 195,359 197,362 198,364 200,366 202,367 203,368 205,368 207,367 208,366 210,364 212,361 214,358 215,354 217,350 219,346 220,341 222,336 224,331 225,326 227,321 229,316 230,312 232,308 234,304 236,301 237,298 239,296 241,294 242,293 244,293 246,293 247,294 249,295 251,297 252,299 254,301 256,304 258,307 259,310 261,313 263,316 264,319 266,323 268,326 269,329 271,332 273,335 274,338 276,341 278,344 280,346 281,349 283,352 285,354 286,357 288,359 290,362 291,364 293,367 295,370 296,372 298,375 300,378 302,381 303,384 305,387 307,390 308,393 310,396 312,399 313,402 315,405 317,408 318,411 320,413 322,416 324,419 325,421 327,424 329,426 330,428 332,431 334,433 335,435 337,437 339,439 341,441 342,443 344,444 346,446 347,448 349,449 351,451 352,452 354,453 356,455 357,456 359,457 361,458 363,459 364,460 366,461 368,462 369,463 371,464 373,464 374,465 376,466 378,466 379,467 381,467 383,468 385,468 386,469 388,469 390,469 391,470 393,470 395,470 396,470 398,471 400,471 401,471 403,471 405,471 407,471 408,472 410,472 412,472 413,472 415,472 417,472 418,472 420,472 422,472 423,472 425,472 427,472 429,472 430,472 432,472 434,472 435,472 437,472 439,472 440,472 442,472 444,472 445,472 447,472 449,472 451,472 452,472 454,472 456,472 457,472 459,471 461,471 462,471 464,470 466,470 468,469 469,468 471,467 473,466 474,464 476,463 478,461 479,459 481,456 483,453 484,449 486,445 488,441 490,436 491,430 493,424 495,417 496,410 498,401 500,392 501,383 503,372 505,361 506,349 508,337 510,324 512,310 513,296 515,282 517,267 518,253 520,238 522,223 523,209 525,194 527,181 528,167 530,155 532,143 534,133 535,123 537,115 539,107 540,101 542,97 544,94 545,92 547,92 549,93 550,95 552,99 554,104 556,111 557,118 559,127 561,136 562,147 564,158 566,170 567,182 569,195 571,208 573,221 574,235 576,248 578,261 579,274 581,287 583,300 584,312 586,324 588,336 589,346 591,357 593,367 595,376 596,385 598,393 600,401 601,408 603,414 605,420 606,426 608,431 610,436 611,440 613,444 615,447 617,450 618,453 620,455 622,457 623,459 625,461 627,462 628,464 630,465 632,466 633,466 635,467 637,467 639,468 640,468 642,468 644,468 645,467 647,467 649,466 650,465 652,464 654,463 655,461 657,459 659,457 661,454 662,452 664,448 666,445 667,441 669,436 671,432 672,426 674,421 676,414 677,408 679,401 681,394 683,386 684,378 686,370 688,361 689,353 691,344 693,336 694,327 696,319 698,311 700,303 701,296 703,289 705,283 706,278 708,273 710,270 711,267 713,265 715,263 716,263 718,264 720,266 722,268 723,271 725,276 727,281 728,286 730,292 732,299 733,306 735,314 737,322 738,330 740,338 742,346 744,355 745,363 747,371 749,379 750,386 752,393 754,400 755,407 757,413 759,419 760,425 762,430 764,434 766,438 767,442 769,446 771,449 772,452 774,454 776,456 777,458 779,460 781,461 782,462 784,463 786,463 788,463 789,463 791,463 793,463 794,462 796,461 798,460 799,459 801,457 803,456 804,454 806,452 808,449 810,447 811,445 813,442 815,439 816,436 818,433 820,430 821,427 823,424 825,421 827,418 828,415 830,413 832,410 833,408 835,406 837,404 838,403 840,402 842,401 843,400 845,400 847,400 849,401 850,402 852,403 854,404 855,406 857,408 859,410 860,413 862,415 864,418 865,421 867,424 869,427 871,430 872,433 874,436 876,439 877,441 879,444 881,447 882,449 884,451 886,453 887,455 889,457 891,459 893,460 894,462 896,463 898,464 899,464 901,465 903,466 904,466 906,466 908,467 909,467 911,467 913,466 915,466 916,466 918,466 920,465 921,465 923,464 925,463 926,463 928,462 930,461 932,461 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,319 166,317 168,316 169,315 171,315 173,316 175,318 176,320 178,322 180,325 181,329 183,332 185,336 186,340 188,344 190,348 191,352 193,356 195,359 197,362 198,364 200,366 202,367 203,368 205,368 207,367 208,366 210,364 212,361 214,358 215,354 217,350 219,346 220,341 222,336 224,331 225,326 227,321 229,316 230,312 232,308 234,304 236,301 237,298 239,296 241,294 242,293 244,293 246,293 247,294 249,295 251,297 252,299 254,301 256,304 258,307 259,310 261,313 263,316 264,319 266,323 268,326 269,329 271,332 273,335 274,338 276,341 278,344 280,346 281,349 283,352 285,354 286,357 288,359 290,362 291,364 293,367 295,370 296,372 298,375 300,378 302,381 303,384 305,387 307,390 308,393 310,396 312,399 313,402 315,405 317,408 318,411 320,413 322,416 324,419 325,421 327,424 329,426 330,428 332,431 334,433 335,435 337,437 339,439 341,441 342,443 344,444 346,446 347,448 349,449 351,451 352,452 354,453 356,455 357,456 359,457 361,458 363,459 364,460 366,461 368,462 369,463 371,464 373,464 374,465 376,466 378,466 379,467 381,467 383,468 385,468 386,469 388,469 390,469 391,470 393,470 395,470 396,470 398,471 400,471 401,471 403,471 405,471 407,471 408,472 410,472 412,472 413,472 415,472 417,472 418,472 420,472 422,472 423,472 425,472 427,472 429,472 430,472 432,472 434,472 435,472 437,472 439,472 440,472 442,472 444,472 445,472 447,472 449,472 451,472 452,472 454,472 456,472 457,472 459,471 461,471 462,471 464,470 466,470 468,469 469,468 471,467 473,466 474,464 476,463 478,461 479,459 481,456 483,453 484,449 486,445 488,441 490,436 491,430 493,424 495,417 496,410 498,401 500,392 501,383 503,372 505,361 506,349 508,337 510,324 512,310 513,296 515,282 517,267 518,253 520,238 522,223 523,209 525,194 527,181 528,167 530,155 532,143 534,133 535,123 537,115 539,107 540,101 542,97 544,94 545,92 547,92 549,93 550,95 552,99 554,104 556,111 557,118 559,127 561,136 562,147 564,158 566,170 567,182 569,195 571,208 573,221 574,235 576,248 578,261 579,274 581,287 583,300 584,312 586,324 588,336 589,346 591,357 593,367 595,376 596,385 598,393 600,401 601,408 603,414 605,420 606,426 608,431 610,436 611,440 613,444 615,447 617,450 618,453 620,455 622,457 623,459 625,461 627,462 628,464 630,465 632,466 633,466 635,467 637,467 639,468 640,468 642,468 644,468 645,467 647,467 649,466 650,465 652,464 654,463 655,461 657,459 659,457 661,454 662,452 664,448 666,445 667,441 669,436 671,432 672,426 674,421 676,414 677,408 679,401 681,394 683,386 684,378 686,370 688,361 689,353 691,344 693,336 694,327 696,319 698,311 700,303 701,296 703,289 705,283 706,278 708,273 710,270 711,267 713,265 715,263 716,263 718,264 720,266 722,268 723,271 725,276 727,281 728,286 730,292 732,299 733,306 735,314 737,322 738,330 740,338 742,346 744,355 745,363 747,371 749,379 750,386 752,393 754,400 755,407 757,413 759,419 760,425 762,430 764,434 766,438 767,442 769,446 771,449 772,452 774,454 776,456 777,458 779,460 781,461 782,462 784,463 786,463 788,463 789,463 791,463 793,463 794,462 796,461 798,460 799,459 801,457 803,456 804,454 806,452 808,449 810,447 811,445 813,442 815,439 816,436 818,433 820,430 821,427 823,424 825,421 827,418 828,415 830,413 832,410 833,408 835,406 837,404 838,403 840,402 842,401 843,400 845,400 847,400 849,401 850,402 852,403 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="549,473 549,93 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

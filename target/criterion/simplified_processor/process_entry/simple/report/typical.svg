<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/simple:typical
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="436" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,436 86,436 "/>
<text x="77" y="378" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,378 86,378 "/>
<text x="77" y="320" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,320 86,320 "/>
<text x="77" y="262" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,262 86,262 "/>
<text x="77" y="204" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,204 86,204 "/>
<text x="77" y="146" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,146 86,146 "/>
<text x="77" y="88" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,88 86,88 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="206" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.98
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="206,473 206,478 "/>
<text x="358" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.985
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="358,473 358,478 "/>
<text x="509" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.99
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="509,473 509,478 "/>
<text x="660" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.995
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="660,473 660,478 "/>
<text x="811" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="811,473 811,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,471 88,470 90,469 92,469 93,468 95,467 97,466 98,465 100,465 102,464 103,463 105,462 107,461 109,460 110,459 112,458 114,457 115,456 117,455 119,454 120,453 122,452 124,451 125,450 127,448 129,447 131,446 132,445 134,444 136,442 137,441 139,440 141,438 142,437 144,436 146,434 147,433 149,431 151,430 153,428 154,427 156,425 158,424 159,422 161,420 163,419 164,417 166,415 168,413 169,411 171,409 173,407 175,406 176,404 178,402 180,400 181,398 183,396 185,394 186,392 188,389 190,387 191,385 193,383 195,381 197,379 198,377 200,375 202,373 203,371 205,369 207,367 208,365 210,363 212,361 214,359 215,357 217,355 219,353 220,351 222,348 224,346 225,344 227,342 229,340 230,337 232,335 234,332 236,330 237,327 239,325 241,322 242,319 244,317 246,314 247,311 249,308 251,305 252,302 254,299 256,297 258,294 259,291 261,288 263,285 264,282 266,279 268,276 269,274 271,271 273,268 274,266 276,263 278,260 280,258 281,255 283,253 285,250 286,248 288,245 290,243 291,240 293,238 295,236 296,233 298,231 300,228 302,226 303,223 305,221 307,218 308,216 310,213 312,211 313,208 315,206 317,203 318,201 320,198 322,196 324,193 325,191 327,188 329,186 330,184 332,181 334,179 335,176 337,174 339,172 341,170 342,167 344,165 346,163 347,161 349,159 351,157 352,155 354,153 356,151 357,150 359,148 361,146 363,144 364,143 366,141 368,140 369,138 371,137 373,135 374,134 376,132 378,131 379,130 381,128 383,127 385,125 386,124 388,123 390,121 391,120 393,118 395,117 396,115 398,114 400,113 401,111 403,110 405,109 407,107 408,106 410,105 412,104 413,103 415,102 417,101 418,100 420,99 422,98 423,98 425,97 427,96 429,96 430,95 432,95 434,95 435,94 437,94 439,94 440,94 442,94 444,94 445,94 447,94 449,94 451,94 452,94 454,94 456,94 457,94 459,94 461,95 462,95 464,95 466,96 468,96 469,97 471,97 473,98 474,98 476,99 478,99 479,100 481,100 483,101 484,102 486,102 488,103 490,104 491,105 493,105 495,106 496,107 498,108 500,108 501,109 503,110 505,111 506,112 508,113 510,114 512,115 513,116 515,118 517,119 518,120 520,122 522,123 523,125 525,126 527,128 528,129 530,131 532,133 534,134 535,136 537,138 539,139 540,141 542,143 544,145 545,147 547,149 549,150 550,152 552,154 554,156 556,158 557,160 559,162 561,165 562,167 564,169 566,171 567,173 569,175 571,178 573,180 574,182 576,184 578,186 579,188 581,190 583,192 584,195 586,197 588,199 589,201 591,203 593,205 595,206 596,208 598,210 600,212 601,214 603,216 605,218 606,220 608,222 610,223 611,225 613,227 615,229 617,231 618,233 620,234 622,236 623,238 625,240 627,242 628,243 630,245 632,247 633,249 635,251 637,253 639,255 640,257 642,259 644,261 645,263 647,266 649,268 650,270 652,273 654,275 655,277 657,279 659,282 661,284 662,286 664,288 666,291 667,293 669,295 671,297 672,299 674,301 676,303 677,305 679,307 681,308 683,310 684,312 686,314 688,315 689,317 691,319 693,320 694,322 696,324 698,326 700,327 701,329 703,331 705,333 706,334 708,336 710,338 711,340 713,342 715,344 716,345 718,347 720,349 722,351 723,353 725,354 727,356 728,358 730,360 732,361 733,363 735,365 737,366 738,368 740,370 742,371 744,373 745,374 747,376 749,377 750,379 752,380 754,382 755,383 757,385 759,386 760,388 762,389 764,391 766,392 767,393 769,395 771,396 772,398 774,399 776,401 777,402 779,403 781,405 782,406 784,407 786,409 788,410 789,412 791,413 793,414 794,415 796,417 798,418 799,419 801,420 803,421 804,423 806,424 808,425 810,426 811,427 813,428 815,429 816,430 818,430 820,431 821,432 823,433 825,434 827,435 828,435 830,436 832,437 833,437 835,438 837,439 838,440 840,440 842,441 843,442 845,442 847,443 849,444 850,444 852,445 854,446 855,446 857,447 859,448 860,449 862,449 864,450 865,451 867,451 869,452 871,453 872,453 874,454 876,455 877,455 879,456 881,456 882,457 884,458 886,458 887,459 889,460 891,460 893,461 894,462 896,462 898,463 899,463 901,464 903,464 904,465 906,466 908,466 909,467 911,467 913,468 915,468 916,469 918,469 920,470 921,470 923,470 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,417 166,415 168,413 169,411 171,409 173,407 175,406 176,404 178,402 180,400 181,398 183,396 185,394 186,392 188,389 190,387 191,385 193,383 195,381 197,379 198,377 200,375 202,373 203,371 205,369 207,367 208,365 210,363 212,361 214,359 215,357 217,355 219,353 220,351 222,348 224,346 225,344 227,342 229,340 230,337 232,335 234,332 236,330 237,327 239,325 241,322 242,319 244,317 246,314 247,311 249,308 251,305 252,302 254,299 256,297 258,294 259,291 261,288 263,285 264,282 266,279 268,276 269,274 271,271 273,268 274,266 276,263 278,260 280,258 281,255 283,253 285,250 286,248 288,245 290,243 291,240 293,238 295,236 296,233 298,231 300,228 302,226 303,223 305,221 307,218 308,216 310,213 312,211 313,208 315,206 317,203 318,201 320,198 322,196 324,193 325,191 327,188 329,186 330,184 332,181 334,179 335,176 337,174 339,172 341,170 342,167 344,165 346,163 347,161 349,159 351,157 352,155 354,153 356,151 357,150 359,148 361,146 363,144 364,143 366,141 368,140 369,138 371,137 373,135 374,134 376,132 378,131 379,130 381,128 383,127 385,125 386,124 388,123 390,121 391,120 393,118 395,117 396,115 398,114 400,113 401,111 403,110 405,109 407,107 408,106 410,105 412,104 413,103 415,102 417,101 418,100 420,99 422,98 423,98 425,97 427,96 429,96 430,95 432,95 434,95 435,94 437,94 439,94 440,94 442,94 444,94 445,94 447,94 449,94 451,94 452,94 454,94 456,94 457,94 459,94 461,95 462,95 464,95 466,96 468,96 469,97 471,97 473,98 474,98 476,99 478,99 479,100 481,100 483,101 484,102 486,102 488,103 490,104 491,105 493,105 495,106 496,107 498,108 500,108 501,109 503,110 505,111 506,112 508,113 510,114 512,115 513,116 515,118 517,119 518,120 520,122 522,123 523,125 525,126 527,128 528,129 530,131 532,133 534,134 535,136 537,138 539,139 540,141 542,143 544,145 545,147 547,149 549,150 550,152 552,154 554,156 556,158 557,160 559,162 561,165 562,167 564,169 566,171 567,173 569,175 571,178 573,180 574,182 576,184 578,186 579,188 581,190 583,192 584,195 586,197 588,199 589,201 591,203 593,205 595,206 596,208 598,210 600,212 601,214 603,216 605,218 606,220 608,222 610,223 611,225 613,227 615,229 617,231 618,233 620,234 622,236 623,238 625,240 627,242 628,243 630,245 632,247 633,249 635,251 637,253 639,255 640,257 642,259 644,261 645,263 647,266 649,268 650,270 652,273 654,275 655,277 657,279 659,282 661,284 662,286 664,288 666,291 667,293 669,295 671,297 672,299 674,301 676,303 677,305 679,307 681,308 683,310 684,312 686,314 688,315 689,317 691,319 693,320 694,322 696,324 698,326 700,327 701,329 703,331 705,333 706,334 708,336 710,338 711,340 713,342 715,344 716,345 718,347 720,349 722,351 723,353 725,354 727,356 728,358 730,360 732,361 733,363 735,365 737,366 738,368 740,370 742,371 744,373 745,374 747,376 749,377 750,379 752,380 754,382 755,383 757,385 759,386 760,388 762,389 764,391 766,392 767,393 769,395 771,396 772,398 774,399 776,401 777,402 779,403 781,405 782,406 784,407 786,409 788,410 789,412 791,413 793,414 794,415 796,417 798,418 799,419 801,420 803,421 804,423 806,424 808,425 810,426 811,427 813,428 815,429 816,430 818,430 820,431 821,432 823,433 825,434 827,435 828,435 830,436 832,437 833,437 835,438 837,439 838,440 840,440 842,441 843,442 845,442 847,443 849,444 850,444 852,445 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="479,473 479,100 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/simple:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="407" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,407 86,407 "/>
<text x="77" y="338" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,338 86,338 "/>
<text x="77" y="269" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,269 86,269 "/>
<text x="77" y="200" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,200 86,200 "/>
<text x="77" y="131" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,131 86,131 "/>
<text x="77" y="62" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,62 86,62 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="101" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.98
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="101,473 101,478 "/>
<text x="209" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.99
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="209,473 209,478 "/>
<text x="317" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="317,473 317,478 "/>
<text x="426" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="426,473 426,478 "/>
<text x="534" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="534,473 534,478 "/>
<text x="642" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="642,473 642,478 "/>
<text x="751" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="751,473 751,478 "/>
<text x="859" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="859,473 859,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,469 97,468 98,467 100,466 102,465 103,464 105,462 107,461 109,459 110,457 112,455 114,453 115,450 117,448 119,445 120,442 122,438 124,435 125,431 127,427 129,423 131,418 132,413 134,408 136,403 137,398 139,392 141,386 142,380 144,373 146,367 147,360 149,353 151,346 153,339 154,331 156,324 158,316 159,308 161,301 163,293 164,285 166,277 168,270 169,262 171,254 173,247 175,239 176,232 178,225 180,218 181,211 183,204 185,198 186,192 188,186 190,180 191,174 193,169 195,164 197,159 198,155 200,150 202,146 203,143 205,139 207,136 208,133 210,131 212,128 214,126 215,125 217,123 219,122 220,121 222,121 224,120 225,120 227,121 229,121 230,122 232,123 234,125 236,126 237,128 239,130 241,133 242,135 244,138 246,141 247,144 249,147 251,151 252,154 254,158 256,162 258,166 259,170 261,174 263,178 264,182 266,186 268,191 269,195 271,199 273,203 274,207 276,211 278,215 280,219 281,223 283,227 285,230 286,233 288,237 290,240 291,242 293,245 295,247 296,249 298,251 300,253 302,254 303,255 305,256 307,256 308,256 310,256 312,255 313,254 315,253 317,251 318,249 320,247 322,244 324,241 325,238 327,234 329,231 330,227 332,222 334,218 335,214 337,209 339,204 341,199 342,194 344,189 346,184 347,179 349,173 351,168 352,163 354,158 356,153 357,149 359,144 361,140 363,135 364,131 366,127 368,123 369,120 371,116 373,113 374,110 376,107 378,105 379,103 381,100 383,99 385,97 386,96 388,94 390,94 391,93 393,92 395,92 396,92 398,92 400,92 401,93 403,94 405,95 407,96 408,97 410,99 412,100 413,102 415,105 417,107 418,110 420,112 422,115 423,119 425,122 427,126 429,129 430,133 432,137 434,141 435,145 437,150 439,154 440,159 442,163 444,168 445,173 447,178 449,182 451,187 452,192 454,197 456,202 457,207 459,211 461,216 462,221 464,225 466,230 468,234 469,239 471,243 473,247 474,251 476,255 478,258 479,262 481,265 483,268 484,271 486,274 488,276 490,279 491,281 493,283 495,285 496,286 498,287 500,288 501,289 503,290 505,290 506,291 508,291 510,291 512,291 513,290 515,290 517,289 518,288 520,288 522,287 523,286 525,285 527,284 528,283 530,282 532,281 534,279 535,278 537,277 539,276 540,276 542,275 544,274 545,273 547,272 549,272 550,271 552,271 554,270 556,270 557,270 559,269 561,269 562,269 564,269 566,269 567,269 569,269 571,270 573,270 574,270 576,271 578,271 579,272 581,273 583,274 584,275 586,276 588,277 589,278 591,279 593,281 595,282 596,284 598,285 600,287 601,289 603,291 605,293 606,295 608,297 610,299 611,301 613,304 615,306 617,308 618,311 620,313 622,316 623,318 625,321 627,323 628,326 630,329 632,331 633,334 635,336 637,339 639,341 640,344 642,347 644,349 645,352 647,354 649,356 650,359 652,361 654,363 655,366 657,368 659,370 661,372 662,374 664,376 666,377 667,379 669,381 671,382 672,384 674,385 676,387 677,388 679,389 681,390 683,391 684,392 686,392 688,393 689,394 691,394 693,395 694,395 696,396 698,396 700,396 701,397 703,397 705,397 706,397 708,397 710,397 711,397 713,397 715,397 716,397 718,397 720,397 722,397 723,397 725,398 727,398 728,398 730,398 732,398 733,398 735,398 737,398 738,398 740,399 742,399 744,399 745,399 747,399 749,400 750,400 752,400 754,401 755,401 757,402 759,402 760,403 762,403 764,404 766,404 767,405 769,405 771,406 772,407 774,407 776,408 777,409 779,410 781,410 782,411 784,412 786,413 788,414 789,415 791,415 793,416 794,417 796,418 798,419 799,420 801,421 803,422 804,423 806,424 808,425 810,427 811,428 813,429 815,430 816,431 818,432 820,433 821,434 823,435 825,436 827,436 828,437 830,438 832,439 833,440 835,441 837,441 838,442 840,443 842,443 843,444 845,445 847,445 849,446 850,446 852,447 854,447 855,448 857,448 859,448 860,449 862,449 864,450 865,450 867,450 869,451 871,451 872,451 874,452 876,452 877,452 879,452 881,453 882,453 884,453 886,453 887,454 889,454 891,454 893,454 894,454 896,455 898,455 899,455 901,455 903,455 904,455 906,455 908,455 909,455 911,455 913,456 915,456 916,456 918,456 920,456 921,456 923,456 925,456 926,456 928,456 930,456 932,456 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,285 166,277 168,270 169,262 171,254 173,247 175,239 176,232 178,225 180,218 181,211 183,204 185,198 186,192 188,186 190,180 191,174 193,169 195,164 197,159 198,155 200,150 202,146 203,143 205,139 207,136 208,133 210,131 212,128 214,126 215,125 217,123 219,122 220,121 222,121 224,120 225,120 227,121 229,121 230,122 232,123 234,125 236,126 237,128 239,130 241,133 242,135 244,138 246,141 247,144 249,147 251,151 252,154 254,158 256,162 258,166 259,170 261,174 263,178 264,182 266,186 268,191 269,195 271,199 273,203 274,207 276,211 278,215 280,219 281,223 283,227 285,230 286,233 288,237 290,240 291,242 293,245 295,247 296,249 298,251 300,253 302,254 303,255 305,256 307,256 308,256 310,256 312,255 313,254 315,253 317,251 318,249 320,247 322,244 324,241 325,238 327,234 329,231 330,227 332,222 334,218 335,214 337,209 339,204 341,199 342,194 344,189 346,184 347,179 349,173 351,168 352,163 354,158 356,153 357,149 359,144 361,140 363,135 364,131 366,127 368,123 369,120 371,116 373,113 374,110 376,107 378,105 379,103 381,100 383,99 385,97 386,96 388,94 390,94 391,93 393,92 395,92 396,92 398,92 400,92 401,93 403,94 405,95 407,96 408,97 410,99 412,100 413,102 415,105 417,107 418,110 420,112 422,115 423,119 425,122 427,126 429,129 430,133 432,137 434,141 435,145 437,150 439,154 440,159 442,163 444,168 445,173 447,178 449,182 451,187 452,192 454,197 456,202 457,207 459,211 461,216 462,221 464,225 466,230 468,234 469,239 471,243 473,247 474,251 476,255 478,258 479,262 481,265 483,268 484,271 486,274 488,276 490,279 491,281 493,283 495,285 496,286 498,287 500,288 501,289 503,290 505,290 506,291 508,291 510,291 512,291 513,290 515,290 517,289 518,288 520,288 522,287 523,286 525,285 527,284 528,283 530,282 532,281 534,279 535,278 537,277 539,276 540,276 542,275 544,274 545,273 547,272 549,272 550,271 552,271 554,270 556,270 557,270 559,269 561,269 562,269 564,269 566,269 567,269 569,269 571,270 573,270 574,270 576,271 578,271 579,272 581,273 583,274 584,275 586,276 588,277 589,278 591,279 593,281 595,282 596,284 598,285 600,287 601,289 603,291 605,293 606,295 608,297 610,299 611,301 613,304 615,306 617,308 618,311 620,313 622,316 623,318 625,321 627,323 628,326 630,329 632,331 633,334 635,336 637,339 639,341 640,344 642,347 644,349 645,352 647,354 649,356 650,359 652,361 654,363 655,366 657,368 659,370 661,372 662,374 664,376 666,377 667,379 669,381 671,382 672,384 674,385 676,387 677,388 679,389 681,390 683,391 684,392 686,392 688,393 689,394 691,394 693,395 694,395 696,396 698,396 700,396 701,397 703,397 705,397 706,397 708,397 710,397 711,397 713,397 715,397 716,397 718,397 720,397 722,397 723,397 725,398 727,398 728,398 730,398 732,398 733,398 735,398 737,398 738,398 740,399 742,399 744,399 745,399 747,399 749,400 750,400 752,400 754,401 755,401 757,402 759,402 760,403 762,403 764,404 766,404 767,405 769,405 771,406 772,407 774,407 776,408 777,409 779,410 781,410 782,411 784,412 786,413 788,414 789,415 791,415 793,416 794,417 796,418 798,419 799,420 801,421 803,422 804,423 806,424 808,425 810,427 811,428 813,429 815,430 816,431 818,432 820,433 821,434 823,435 825,436 827,436 828,437 830,438 832,439 833,440 835,441 837,441 838,442 840,443 842,443 843,444 845,445 847,445 849,446 850,446 852,447 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="421,473 421,115 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

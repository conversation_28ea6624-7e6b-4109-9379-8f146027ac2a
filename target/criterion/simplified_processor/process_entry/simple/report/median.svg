<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/simple:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="440" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,440 86,440 "/>
<text x="77" y="398" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,398 86,398 "/>
<text x="77" y="356" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,356 86,356 "/>
<text x="77" y="314" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,314 86,314 "/>
<text x="77" y="272" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,272 86,272 "/>
<text x="77" y="230" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,230 86,230 "/>
<text x="77" y="188" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,188 86,188 "/>
<text x="77" y="146" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,146 86,146 "/>
<text x="77" y="104" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,104 86,104 "/>
<text x="77" y="62" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,62 86,62 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="192" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
976
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="192,473 192,478 "/>
<text x="301" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
978
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="301,473 301,478 "/>
<text x="410" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
980
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="410,473 410,478 "/>
<text x="519" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
982
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="519,473 519,478 "/>
<text x="629" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
984
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="629,473 629,478 "/>
<text x="738" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
986
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="738,473 738,478 "/>
<text x="847" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
988
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="847,473 847,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,469 88,468 90,467 92,466 93,465 95,463 97,462 98,461 100,459 102,458 103,457 105,455 107,454 109,452 110,451 112,450 114,449 115,448 117,447 119,446 120,445 122,444 124,444 125,443 127,443 129,443 131,442 132,442 134,442 136,442 137,442 139,442 141,442 142,442 144,442 146,441 147,441 149,441 151,440 153,439 154,439 156,438 158,437 159,435 161,434 163,432 164,431 166,429 168,427 169,425 171,423 173,420 175,418 176,415 178,412 180,409 181,406 183,403 185,400 186,396 188,393 190,390 191,387 193,384 195,381 197,378 198,375 200,372 202,370 203,367 205,365 207,363 208,361 210,359 212,357 214,355 215,353 217,352 219,350 220,348 222,345 224,343 225,341 227,338 229,335 230,332 232,329 234,325 236,322 237,318 239,314 241,310 242,306 244,302 246,298 247,294 249,290 251,286 252,282 254,279 256,275 258,272 259,270 261,267 263,265 264,263 266,261 268,259 269,258 271,256 273,255 274,253 276,252 278,250 280,248 281,246 283,244 285,242 286,239 288,235 290,231 291,227 293,222 295,216 296,210 298,203 300,196 302,188 303,180 305,172 307,163 308,155 310,146 312,137 313,129 315,121 317,114 318,108 320,102 322,98 324,95 325,93 327,92 329,93 330,95 332,99 334,104 335,110 337,117 339,125 341,135 342,144 344,155 346,165 347,176 349,187 351,198 352,209 354,219 356,229 357,239 359,248 361,257 363,265 364,272 366,279 368,285 369,291 371,296 373,301 374,305 376,309 378,312 379,315 381,317 383,320 385,322 386,323 388,325 390,326 391,327 393,328 395,329 396,329 398,330 400,331 401,332 403,332 405,333 407,334 408,335 410,336 412,336 413,337 415,338 417,338 418,339 420,339 422,339 423,340 425,340 427,340 429,339 430,339 432,339 434,339 435,339 437,339 439,339 440,339 442,339 444,340 445,340 447,340 449,340 451,340 452,340 454,339 456,338 457,337 459,335 461,332 462,328 464,324 466,320 468,314 469,309 471,302 473,296 474,289 476,282 478,275 479,268 481,262 483,256 484,251 486,247 488,244 490,241 491,240 493,240 495,240 496,242 498,245 500,248 501,252 503,256 505,261 506,267 508,272 510,278 512,283 513,289 515,294 517,299 518,304 520,308 522,312 523,316 525,319 527,323 528,325 530,328 532,330 534,331 535,333 537,334 539,335 540,336 542,336 544,337 545,337 547,336 549,336 550,336 552,335 554,335 556,335 557,334 559,334 561,334 562,334 564,334 566,334 567,334 569,335 571,335 573,336 574,336 576,337 578,337 579,338 581,338 583,339 584,339 586,339 588,339 589,338 591,338 593,337 595,336 596,335 598,333 600,331 601,329 603,327 605,324 606,321 608,317 610,313 611,308 613,303 615,298 617,292 618,286 620,279 622,272 623,265 625,258 627,250 628,242 630,234 632,227 633,219 635,211 637,204 639,197 640,191 642,185 644,180 645,175 647,171 649,168 650,166 652,164 654,164 655,165 657,166 659,169 661,173 662,178 664,184 666,191 667,200 669,209 671,219 672,229 674,241 676,252 677,265 679,277 681,289 683,302 684,314 686,326 688,337 689,348 691,358 693,368 694,376 696,385 698,392 700,399 701,405 703,410 705,415 706,419 708,423 710,426 711,429 713,432 715,434 716,436 718,438 720,440 722,441 723,443 725,444 727,445 728,446 730,447 732,448 733,449 735,449 737,450 738,450 740,450 742,450 744,450 745,450 747,450 749,450 750,450 752,450 754,450 755,450 757,450 759,450 760,450 762,450 764,451 766,451 767,452 769,452 771,453 772,454 774,455 776,455 777,456 779,457 781,458 782,458 784,459 786,459 788,460 789,460 791,460 793,460 794,460 796,461 798,461 799,461 801,461 803,461 804,461 806,461 808,461 810,461 811,461 813,462 815,462 816,462 818,462 820,463 821,463 823,463 825,463 827,464 828,464 830,464 832,464 833,464 835,464 837,464 838,463 840,463 842,463 843,462 845,462 847,461 849,461 850,460 852,459 854,459 855,458 857,458 859,457 860,457 862,457 864,457 865,456 867,456 869,457 871,457 872,457 874,457 876,458 877,458 879,459 881,460 882,460 884,461 886,462 887,463 889,463 891,464 893,465 894,466 896,466 898,467 899,467 901,468 903,468 904,469 906,469 908,470 909,470 911,470 913,470 915,471 916,471 918,471 920,471 921,471 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,431 166,429 168,427 169,425 171,423 173,420 175,418 176,415 178,412 180,409 181,406 183,403 185,400 186,396 188,393 190,390 191,387 193,384 195,381 197,378 198,375 200,372 202,370 203,367 205,365 207,363 208,361 210,359 212,357 214,355 215,353 217,352 219,350 220,348 222,345 224,343 225,341 227,338 229,335 230,332 232,329 234,325 236,322 237,318 239,314 241,310 242,306 244,302 246,298 247,294 249,290 251,286 252,282 254,279 256,275 258,272 259,270 261,267 263,265 264,263 266,261 268,259 269,258 271,256 273,255 274,253 276,252 278,250 280,248 281,246 283,244 285,242 286,239 288,235 290,231 291,227 293,222 295,216 296,210 298,203 300,196 302,188 303,180 305,172 307,163 308,155 310,146 312,137 313,129 315,121 317,114 318,108 320,102 322,98 324,95 325,93 327,92 329,93 330,95 332,99 334,104 335,110 337,117 339,125 341,135 342,144 344,155 346,165 347,176 349,187 351,198 352,209 354,219 356,229 357,239 359,248 361,257 363,265 364,272 366,279 368,285 369,291 371,296 373,301 374,305 376,309 378,312 379,315 381,317 383,320 385,322 386,323 388,325 390,326 391,327 393,328 395,329 396,329 398,330 400,331 401,332 403,332 405,333 407,334 408,335 410,336 412,336 413,337 415,338 417,338 418,339 420,339 422,339 423,340 425,340 427,340 429,339 430,339 432,339 434,339 435,339 437,339 439,339 440,339 442,339 444,340 445,340 447,340 449,340 451,340 452,340 454,339 456,338 457,337 459,335 461,332 462,328 464,324 466,320 468,314 469,309 471,302 473,296 474,289 476,282 478,275 479,268 481,262 483,256 484,251 486,247 488,244 490,241 491,240 493,240 495,240 496,242 498,245 500,248 501,252 503,256 505,261 506,267 508,272 510,278 512,283 513,289 515,294 517,299 518,304 520,308 522,312 523,316 525,319 527,323 528,325 530,328 532,330 534,331 535,333 537,334 539,335 540,336 542,336 544,337 545,337 547,336 549,336 550,336 552,335 554,335 556,335 557,334 559,334 561,334 562,334 564,334 566,334 567,334 569,335 571,335 573,336 574,336 576,337 578,337 579,338 581,338 583,339 584,339 586,339 588,339 589,338 591,338 593,337 595,336 596,335 598,333 600,331 601,329 603,327 605,324 606,321 608,317 610,313 611,308 613,303 615,298 617,292 618,286 620,279 622,272 623,265 625,258 627,250 628,242 630,234 632,227 633,219 635,211 637,204 639,197 640,191 642,185 644,180 645,175 647,171 649,168 650,166 652,164 654,164 655,165 657,166 659,169 661,173 662,178 664,184 666,191 667,200 669,209 671,219 672,229 674,241 676,252 677,265 679,277 681,289 683,302 684,314 686,326 688,337 689,348 691,358 693,368 694,376 696,385 698,392 700,399 701,405 703,410 705,415 706,419 708,423 710,426 711,429 713,432 715,434 716,436 718,438 720,440 722,441 723,443 725,444 727,445 728,446 730,447 732,448 733,449 735,449 737,450 738,450 740,450 742,450 744,450 745,450 747,450 749,450 750,450 752,450 754,450 755,450 757,450 759,450 760,450 762,450 764,451 766,451 767,452 769,452 771,453 772,454 774,455 776,455 777,456 779,457 781,458 782,458 784,459 786,459 788,460 789,460 791,460 793,460 794,460 796,461 798,461 799,461 801,461 803,461 804,461 806,461 808,461 810,461 811,461 813,462 815,462 816,462 818,462 820,463 821,463 823,463 825,463 827,464 828,464 830,464 832,464 833,464 835,464 837,464 838,463 840,463 842,463 843,462 845,462 847,461 849,461 850,460 852,459 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="443,473 443,339 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/complex:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="434" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,434 86,434 "/>
<text x="77" y="393" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,393 86,393 "/>
<text x="77" y="351" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,351 86,351 "/>
<text x="77" y="310" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,310 86,310 "/>
<text x="77" y="269" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,269 86,269 "/>
<text x="77" y="228" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,228 86,228 "/>
<text x="77" y="187" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,187 86,187 "/>
<text x="77" y="145" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,145 86,145 "/>
<text x="77" y="104" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,104 86,104 "/>
<text x="77" y="63" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,63 86,63 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="156" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="156,473 156,478 "/>
<text x="251" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.65
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="251,473 251,478 "/>
<text x="347" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="347,473 347,478 "/>
<text x="442" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.75
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="442,473 442,478 "/>
<text x="537" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="537,473 537,478 "/>
<text x="633" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.85
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="633,473 633,478 "/>
<text x="728" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="728,473 728,478 "/>
<text x="823" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.95
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="823,473 823,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,470 95,470 97,469 98,468 100,467 102,466 103,465 105,463 107,462 109,460 110,458 112,456 114,453 115,451 117,448 119,445 120,442 122,438 124,435 125,431 127,426 129,422 131,417 132,412 134,407 136,401 137,395 139,389 141,383 142,376 144,370 146,363 147,356 149,349 151,342 153,334 154,327 156,320 158,312 159,305 161,298 163,291 164,284 166,277 168,270 169,264 171,258 173,252 175,246 176,241 178,236 180,231 181,226 183,222 185,218 186,214 188,211 190,208 191,205 193,203 195,201 197,199 198,197 200,196 202,194 203,193 205,192 207,191 208,190 210,189 212,189 214,188 215,187 217,187 219,186 220,186 222,185 224,184 225,184 227,183 229,183 230,182 232,182 234,181 236,181 237,180 239,180 241,180 242,180 244,180 246,180 247,180 249,180 251,181 252,181 254,182 256,183 258,184 259,185 261,186 263,187 264,188 266,190 268,191 269,193 271,194 273,196 274,197 276,198 278,200 280,201 281,202 283,203 285,204 286,204 288,205 290,205 291,205 293,204 295,204 296,203 298,201 300,200 302,198 303,196 305,193 307,190 308,187 310,184 312,181 313,177 315,173 317,169 318,165 320,160 322,156 324,152 325,147 327,143 329,138 330,134 332,130 334,126 335,122 337,118 339,115 341,111 342,108 344,105 346,103 347,101 349,99 351,97 352,95 354,94 356,93 357,92 359,92 361,92 363,92 364,92 366,92 368,93 369,94 371,95 373,96 374,97 376,98 378,99 379,100 381,102 383,103 385,104 386,105 388,107 390,108 391,109 393,110 395,111 396,112 398,113 400,114 401,115 403,116 405,117 407,118 408,119 410,120 412,122 413,123 415,125 417,126 418,128 420,130 422,132 423,134 425,136 427,139 429,141 430,144 432,147 434,150 435,153 437,156 439,160 440,163 442,167 444,170 445,174 447,177 449,180 451,184 452,187 454,190 456,193 457,196 459,199 461,202 462,204 464,206 466,208 468,210 469,211 471,213 473,214 474,215 476,215 478,216 479,216 481,216 483,216 484,216 486,216 488,215 490,215 491,215 493,214 495,214 496,213 498,213 500,212 501,212 503,212 505,212 506,212 508,212 510,212 512,213 513,214 515,214 517,215 518,217 520,218 522,219 523,221 525,222 527,224 528,226 530,228 532,230 534,232 535,234 537,236 539,238 540,240 542,242 544,244 545,246 547,248 549,250 550,251 552,253 554,255 556,256 557,258 559,259 561,260 562,262 564,263 566,264 567,266 569,267 571,268 573,269 574,270 576,272 578,273 579,274 581,275 583,277 584,278 586,280 588,281 589,283 591,284 593,286 595,288 596,290 598,292 600,294 601,296 603,298 605,300 606,302 608,305 610,307 611,309 613,312 615,314 617,316 618,318 620,321 622,323 623,325 625,327 627,329 628,331 630,333 632,335 633,337 635,339 637,340 639,342 640,343 642,344 644,346 645,347 647,348 649,349 650,350 652,351 654,352 655,352 657,353 659,354 661,354 662,355 664,355 666,356 667,356 669,357 671,357 672,358 674,358 676,358 677,359 679,359 681,360 683,361 684,361 686,362 688,362 689,363 691,364 693,365 694,366 696,367 698,368 700,369 701,370 703,371 705,372 706,373 708,374 710,375 711,377 713,378 715,379 716,380 718,381 720,383 722,384 723,385 725,386 727,387 728,388 730,389 732,390 733,391 735,392 737,393 738,394 740,395 742,396 744,396 745,397 747,398 749,399 750,400 752,401 754,401 755,402 757,403 759,404 760,405 762,406 764,407 766,407 767,408 769,409 771,410 772,411 774,412 776,413 777,414 779,415 781,416 782,417 784,418 786,419 788,420 789,421 791,422 793,423 794,424 796,425 798,425 799,426 801,427 803,428 804,429 806,429 808,430 810,431 811,431 813,432 815,432 816,433 818,433 820,434 821,434 823,435 825,435 827,436 828,436 830,436 832,437 833,437 835,437 837,438 838,438 840,438 842,439 843,439 845,439 847,440 849,440 850,440 852,441 854,441 855,442 857,442 859,442 860,443 862,443 864,444 865,444 867,444 869,445 871,445 872,445 874,446 876,446 877,446 879,447 881,447 882,447 884,448 886,448 887,448 889,448 891,449 893,449 894,449 896,449 898,450 899,450 901,450 903,450 904,451 906,451 908,451 909,452 911,452 913,452 915,452 916,453 918,453 920,454 921,454 923,454 925,455 926,455 928,455 930,456 932,456 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,284 166,277 168,270 169,264 171,258 173,252 175,246 176,241 178,236 180,231 181,226 183,222 185,218 186,214 188,211 190,208 191,205 193,203 195,201 197,199 198,197 200,196 202,194 203,193 205,192 207,191 208,190 210,189 212,189 214,188 215,187 217,187 219,186 220,186 222,185 224,184 225,184 227,183 229,183 230,182 232,182 234,181 236,181 237,180 239,180 241,180 242,180 244,180 246,180 247,180 249,180 251,181 252,181 254,182 256,183 258,184 259,185 261,186 263,187 264,188 266,190 268,191 269,193 271,194 273,196 274,197 276,198 278,200 280,201 281,202 283,203 285,204 286,204 288,205 290,205 291,205 293,204 295,204 296,203 298,201 300,200 302,198 303,196 305,193 307,190 308,187 310,184 312,181 313,177 315,173 317,169 318,165 320,160 322,156 324,152 325,147 327,143 329,138 330,134 332,130 334,126 335,122 337,118 339,115 341,111 342,108 344,105 346,103 347,101 349,99 351,97 352,95 354,94 356,93 357,92 359,92 361,92 363,92 364,92 366,92 368,93 369,94 371,95 373,96 374,97 376,98 378,99 379,100 381,102 383,103 385,104 386,105 388,107 390,108 391,109 393,110 395,111 396,112 398,113 400,114 401,115 403,116 405,117 407,118 408,119 410,120 412,122 413,123 415,125 417,126 418,128 420,130 422,132 423,134 425,136 427,139 429,141 430,144 432,147 434,150 435,153 437,156 439,160 440,163 442,167 444,170 445,174 447,177 449,180 451,184 452,187 454,190 456,193 457,196 459,199 461,202 462,204 464,206 466,208 468,210 469,211 471,213 473,214 474,215 476,215 478,216 479,216 481,216 483,216 484,216 486,216 488,215 490,215 491,215 493,214 495,214 496,213 498,213 500,212 501,212 503,212 505,212 506,212 508,212 510,212 512,213 513,214 515,214 517,215 518,217 520,218 522,219 523,221 525,222 527,224 528,226 530,228 532,230 534,232 535,234 537,236 539,238 540,240 542,242 544,244 545,246 547,248 549,250 550,251 552,253 554,255 556,256 557,258 559,259 561,260 562,262 564,263 566,264 567,266 569,267 571,268 573,269 574,270 576,272 578,273 579,274 581,275 583,277 584,278 586,280 588,281 589,283 591,284 593,286 595,288 596,290 598,292 600,294 601,296 603,298 605,300 606,302 608,305 610,307 611,309 613,312 615,314 617,316 618,318 620,321 622,323 623,325 625,327 627,329 628,331 630,333 632,335 633,337 635,339 637,340 639,342 640,343 642,344 644,346 645,347 647,348 649,349 650,350 652,351 654,352 655,352 657,353 659,354 661,354 662,355 664,355 666,356 667,356 669,357 671,357 672,358 674,358 676,358 677,359 679,359 681,360 683,361 684,361 686,362 688,362 689,363 691,364 693,365 694,366 696,367 698,368 700,369 701,370 703,371 705,372 706,373 708,374 710,375 711,377 713,378 715,379 716,380 718,381 720,383 722,384 723,385 725,386 727,387 728,388 730,389 732,390 733,391 735,392 737,393 738,394 740,395 742,396 744,396 745,397 747,398 749,399 750,400 752,401 754,401 755,402 757,403 759,404 760,405 762,406 764,407 766,407 767,408 769,409 771,410 772,411 774,412 776,413 777,414 779,415 781,416 782,417 784,418 786,419 788,420 789,421 791,422 793,423 794,424 796,425 798,425 799,426 801,427 803,428 804,429 806,429 808,430 810,431 811,431 813,432 815,432 816,433 818,433 820,434 821,434 823,435 825,435 827,436 828,436 830,436 832,437 833,437 835,437 837,438 838,438 840,438 842,439 843,439 845,439 847,440 849,440 850,440 852,441 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="429,473 429,141 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/complex:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="437" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,437 86,437 "/>
<text x="77" y="390" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,390 86,390 "/>
<text x="77" y="344" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,344 86,344 "/>
<text x="77" y="298" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,298 86,298 "/>
<text x="77" y="251" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,251 86,251 "/>
<text x="77" y="205" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,205 86,205 "/>
<text x="77" y="158" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,158 86,158 "/>
<text x="77" y="112" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,112 86,112 "/>
<text x="77" y="65" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,65 86,65 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="145" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.56
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="145,473 145,478 "/>
<text x="275" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.57
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="275,473 275,478 "/>
<text x="405" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.58
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="405,473 405,478 "/>
<text x="536" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.59
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="536,473 536,478 "/>
<text x="666" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="666,473 666,478 "/>
<text x="796" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.61
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="796,473 796,478 "/>
<text x="926" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.62
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="926,473 926,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,468 88,468 90,467 92,467 93,467 95,466 97,466 98,466 100,466 102,465 103,465 105,465 107,464 109,464 110,463 112,463 114,463 115,462 117,461 119,461 120,460 122,459 124,458 125,457 127,456 129,455 131,454 132,452 134,451 136,449 137,448 139,446 141,444 142,442 144,439 146,437 147,435 149,432 151,430 153,428 154,425 156,423 158,421 159,418 161,416 163,414 164,412 166,411 168,409 169,408 171,407 173,405 175,405 176,404 178,403 180,403 181,402 183,402 185,401 186,401 188,401 190,400 191,400 193,400 195,399 197,398 198,397 200,396 202,395 203,394 205,393 207,391 208,389 210,388 212,386 214,383 215,381 217,379 219,377 220,374 222,372 224,369 225,367 227,365 229,363 230,361 232,359 234,357 236,356 237,354 239,353 241,353 242,352 244,352 246,352 247,352 249,353 251,354 252,355 254,357 256,359 258,361 259,363 261,366 263,369 264,371 266,374 268,378 269,381 271,384 273,387 274,391 276,394 278,397 280,399 281,402 283,405 285,407 286,409 288,410 290,412 291,413 293,414 295,415 296,415 298,415 300,415 302,415 303,415 305,415 307,414 308,414 310,414 312,414 313,414 315,414 317,414 318,414 320,415 322,416 324,417 325,418 327,419 329,420 330,421 332,422 334,424 335,425 337,426 339,427 341,427 342,428 344,428 346,428 347,428 349,427 351,426 352,425 354,424 356,423 357,421 359,420 361,418 363,416 364,414 366,413 368,411 369,410 371,409 373,408 374,407 376,407 378,407 379,407 381,407 383,408 385,409 386,410 388,411 390,412 391,414 393,416 395,417 396,419 398,421 400,422 401,424 403,425 405,426 407,427 408,427 410,428 412,428 413,427 415,427 417,426 418,425 420,423 422,422 423,420 425,418 427,415 429,413 430,411 432,409 434,407 435,405 437,403 439,401 440,400 442,399 444,399 445,398 447,399 449,399 451,400 452,401 454,403 456,405 457,407 459,409 461,412 462,414 464,417 466,419 468,422 469,424 471,426 473,427 474,428 476,429 478,429 479,428 481,427 483,425 484,422 486,418 488,414 490,409 491,403 493,396 495,388 496,380 498,371 500,361 501,350 503,339 505,327 506,315 508,302 510,289 512,275 513,262 515,248 517,235 518,222 520,209 522,196 523,184 525,173 527,162 528,153 530,144 532,136 534,129 535,123 537,118 539,114 540,111 542,109 544,107 545,107 547,108 549,109 550,112 552,114 554,118 556,121 557,126 559,130 561,135 562,139 564,144 566,149 567,154 569,159 571,164 573,168 574,173 576,177 578,182 579,186 581,190 583,194 584,198 586,201 588,205 589,209 591,212 593,215 595,219 596,222 598,225 600,229 601,232 603,235 605,238 606,241 608,244 610,247 611,250 613,253 615,256 617,259 618,261 620,264 622,266 623,269 625,271 627,273 628,275 630,276 632,278 633,279 635,280 637,281 639,282 640,283 642,283 644,284 645,284 647,284 649,284 650,284 652,283 654,283 655,282 657,281 659,280 661,279 662,278 664,277 666,275 667,274 669,272 671,270 672,268 674,266 676,263 677,261 679,258 681,255 683,251 684,248 686,244 688,239 689,234 691,229 693,224 694,218 696,212 698,205 700,198 701,191 703,184 705,176 706,169 708,161 710,153 711,146 713,138 715,131 716,124 718,118 720,112 722,107 723,103 725,99 727,96 728,94 730,93 732,93 733,93 735,95 737,98 738,101 740,106 742,111 744,117 745,123 747,131 749,138 750,146 752,155 754,163 755,172 757,181 759,190 760,198 762,206 764,214 766,222 767,229 769,236 771,242 772,248 774,254 776,259 777,263 779,268 781,272 782,275 784,279 786,282 788,285 789,288 791,292 793,295 794,298 796,301 798,305 799,308 801,312 803,316 804,320 806,325 808,330 810,334 811,339 813,345 815,350 816,355 818,361 820,366 821,372 823,377 825,382 827,388 828,393 830,398 832,402 833,407 835,411 837,416 838,419 840,423 842,427 843,430 845,433 847,436 849,438 850,441 852,443 854,445 855,447 857,449 859,450 860,452 862,453 864,454 865,456 867,457 869,458 871,459 872,460 874,460 876,461 877,462 879,462 881,463 882,464 884,464 886,465 887,465 889,465 891,466 893,466 894,467 896,467 898,467 899,468 901,468 903,468 904,469 906,469 908,469 909,470 911,470 913,470 915,471 916,471 918,471 920,471 921,472 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,412 166,411 168,409 169,408 171,407 173,405 175,405 176,404 178,403 180,403 181,402 183,402 185,401 186,401 188,401 190,400 191,400 193,400 195,399 197,398 198,397 200,396 202,395 203,394 205,393 207,391 208,389 210,388 212,386 214,383 215,381 217,379 219,377 220,374 222,372 224,369 225,367 227,365 229,363 230,361 232,359 234,357 236,356 237,354 239,353 241,353 242,352 244,352 246,352 247,352 249,353 251,354 252,355 254,357 256,359 258,361 259,363 261,366 263,369 264,371 266,374 268,378 269,381 271,384 273,387 274,391 276,394 278,397 280,399 281,402 283,405 285,407 286,409 288,410 290,412 291,413 293,414 295,415 296,415 298,415 300,415 302,415 303,415 305,415 307,414 308,414 310,414 312,414 313,414 315,414 317,414 318,414 320,415 322,416 324,417 325,418 327,419 329,420 330,421 332,422 334,424 335,425 337,426 339,427 341,427 342,428 344,428 346,428 347,428 349,427 351,426 352,425 354,424 356,423 357,421 359,420 361,418 363,416 364,414 366,413 368,411 369,410 371,409 373,408 374,407 376,407 378,407 379,407 381,407 383,408 385,409 386,410 388,411 390,412 391,414 393,416 395,417 396,419 398,421 400,422 401,424 403,425 405,426 407,427 408,427 410,428 412,428 413,427 415,427 417,426 418,425 420,423 422,422 423,420 425,418 427,415 429,413 430,411 432,409 434,407 435,405 437,403 439,401 440,400 442,399 444,399 445,398 447,399 449,399 451,400 452,401 454,403 456,405 457,407 459,409 461,412 462,414 464,417 466,419 468,422 469,424 471,426 473,427 474,428 476,429 478,429 479,428 481,427 483,425 484,422 486,418 488,414 490,409 491,403 493,396 495,388 496,380 498,371 500,361 501,350 503,339 505,327 506,315 508,302 510,289 512,275 513,262 515,248 517,235 518,222 520,209 522,196 523,184 525,173 527,162 528,153 530,144 532,136 534,129 535,123 537,118 539,114 540,111 542,109 544,107 545,107 547,108 549,109 550,112 552,114 554,118 556,121 557,126 559,130 561,135 562,139 564,144 566,149 567,154 569,159 571,164 573,168 574,173 576,177 578,182 579,186 581,190 583,194 584,198 586,201 588,205 589,209 591,212 593,215 595,219 596,222 598,225 600,229 601,232 603,235 605,238 606,241 608,244 610,247 611,250 613,253 615,256 617,259 618,261 620,264 622,266 623,269 625,271 627,273 628,275 630,276 632,278 633,279 635,280 637,281 639,282 640,283 642,283 644,284 645,284 647,284 649,284 650,284 652,283 654,283 655,282 657,281 659,280 661,279 662,278 664,277 666,275 667,274 669,272 671,270 672,268 674,266 676,263 677,261 679,258 681,255 683,251 684,248 686,244 688,239 689,234 691,229 693,224 694,218 696,212 698,205 700,198 701,191 703,184 705,176 706,169 708,161 710,153 711,146 713,138 715,131 716,124 718,118 720,112 722,107 723,103 725,99 727,96 728,94 730,93 732,93 733,93 735,95 737,98 738,101 740,106 742,111 744,117 745,123 747,131 749,138 750,146 752,155 754,163 755,172 757,181 759,190 760,198 762,206 764,214 766,222 767,229 769,236 771,242 772,248 774,254 776,259 777,263 779,268 781,272 782,275 784,279 786,282 788,285 789,288 791,292 793,295 794,298 796,301 798,305 799,308 801,312 803,316 804,320 806,325 808,330 810,334 811,339 813,345 815,350 816,355 818,361 820,366 821,372 823,377 825,382 827,388 828,393 830,398 832,402 833,407 835,411 837,416 838,419 840,423 842,427 843,430 845,433 847,436 849,438 850,441 852,443 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="597,473 597,224 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

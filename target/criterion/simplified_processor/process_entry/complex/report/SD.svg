<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/complex:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="433" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,433 86,433 "/>
<text x="77" y="393" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,393 86,393 "/>
<text x="77" y="353" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,353 86,353 "/>
<text x="77" y="313" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,313 86,313 "/>
<text x="77" y="274" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,274 86,274 "/>
<text x="77" y="234" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,234 86,234 "/>
<text x="77" y="194" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,194 86,194 "/>
<text x="77" y="154" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,154 86,154 "/>
<text x="77" y="114" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,114 86,114 "/>
<text x="77" y="74" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,74 86,74 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="97" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="97,473 97,478 "/>
<text x="191" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="191,473 191,478 "/>
<text x="286" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="286,473 286,478 "/>
<text x="380" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="380,473 380,478 "/>
<text x="474" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="474,473 474,478 "/>
<text x="569" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="569,473 569,478 "/>
<text x="663" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="663,473 663,478 "/>
<text x="757" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="757,473 757,478 "/>
<text x="851" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="851,473 851,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,471 98,471 100,471 102,470 103,469 105,469 107,468 109,467 110,465 112,464 114,462 115,460 117,458 119,455 120,452 122,449 124,445 125,441 127,436 129,431 131,425 132,419 134,413 136,405 137,398 139,390 141,382 142,373 144,365 146,356 147,347 149,338 151,329 153,320 154,311 156,303 158,296 159,289 161,283 163,277 164,273 166,269 168,267 169,265 171,265 173,265 175,267 176,269 178,273 180,278 181,283 183,289 185,296 186,304 188,312 190,320 191,329 193,338 195,347 197,356 198,365 200,373 202,382 203,390 205,398 207,405 208,412 210,418 212,424 214,429 215,434 217,438 219,442 220,445 222,447 224,449 225,451 227,452 229,452 230,452 232,451 234,449 236,447 237,444 239,441 241,437 242,432 244,427 246,421 247,414 249,407 251,399 252,390 254,381 256,372 258,362 259,352 261,342 263,331 264,321 266,310 268,301 269,291 271,282 273,274 274,267 276,260 278,255 280,250 281,247 283,246 285,245 286,246 288,248 290,251 291,255 293,260 295,266 296,273 298,280 300,288 302,296 303,304 305,312 307,320 308,328 310,336 312,342 313,348 315,354 317,358 318,362 320,365 322,367 324,369 325,370 327,370 329,369 330,368 332,367 334,365 335,363 337,361 339,359 341,357 342,355 344,354 346,353 347,352 349,352 351,352 352,352 354,354 356,355 357,357 359,360 361,363 363,366 364,369 366,373 368,377 369,381 371,384 373,388 374,392 376,395 378,399 379,402 381,405 383,408 385,410 386,412 388,414 390,416 391,418 393,419 395,421 396,422 398,423 400,425 401,426 403,427 405,428 407,430 408,431 410,432 412,433 413,435 415,436 417,438 418,439 420,441 422,442 423,443 425,445 427,446 429,447 430,449 432,450 434,451 435,452 437,453 439,454 440,455 442,455 444,456 445,457 447,457 449,458 451,458 452,459 454,459 456,459 457,459 459,459 461,459 462,458 464,458 466,457 468,456 469,454 471,452 473,450 474,447 476,444 478,440 479,435 481,430 483,425 484,418 486,411 488,403 490,395 491,386 493,376 495,365 496,354 498,342 500,329 501,317 503,303 505,290 506,276 508,263 510,249 512,236 513,222 515,209 517,197 518,185 520,174 522,163 523,153 525,143 527,135 528,127 530,120 532,114 534,108 535,104 537,100 539,97 540,94 542,93 544,92 545,92 547,92 549,93 550,95 552,98 554,101 556,105 557,109 559,114 561,120 562,126 564,133 566,140 567,147 569,155 571,163 573,172 574,181 576,190 578,199 579,208 581,218 583,227 584,237 586,246 588,255 589,265 591,274 593,283 595,292 596,301 598,309 600,318 601,326 603,334 605,341 606,349 608,356 610,363 611,369 613,376 615,382 617,388 618,393 620,398 622,403 623,408 625,412 627,416 628,420 630,423 632,426 633,429 635,431 637,433 639,435 640,436 642,437 644,437 645,437 647,437 649,436 650,434 652,432 654,430 655,427 657,423 659,419 661,415 662,410 664,404 666,398 667,391 669,384 671,377 672,369 674,361 676,352 677,344 679,335 681,327 683,318 684,310 686,301 688,293 689,286 691,278 693,272 694,265 696,260 698,255 700,251 701,247 703,244 705,242 706,241 708,240 710,240 711,241 713,243 715,245 716,248 718,252 720,256 722,260 723,265 725,271 727,277 728,283 730,290 732,297 733,304 735,311 737,318 738,325 740,332 742,339 744,347 745,354 747,360 749,367 750,374 752,380 754,386 755,392 757,397 759,403 760,408 762,412 764,417 766,421 767,424 769,428 771,431 772,433 774,436 776,438 777,439 779,441 781,442 782,442 784,443 786,443 788,442 789,442 791,440 793,439 794,438 796,436 798,434 799,431 801,429 803,426 804,423 806,420 808,417 810,414 811,411 813,408 815,405 816,402 818,400 820,397 821,395 823,393 825,391 827,390 828,389 830,388 832,387 833,387 835,387 837,388 838,388 840,389 842,391 843,392 845,394 847,396 849,399 850,401 852,404 854,407 855,409 857,412 859,415 860,418 862,421 864,424 865,427 867,430 869,433 871,436 872,438 874,441 876,443 877,445 879,447 881,449 882,451 884,452 886,454 887,455 889,456 891,457 893,458 894,459 896,459 898,459 899,460 901,460 903,460 904,459 906,459 908,459 909,458 911,458 913,457 915,457 916,456 918,455 920,455 921,454 923,454 925,453 926,452 928,452 930,451 932,451 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,273 166,269 168,267 169,265 171,265 173,265 175,267 176,269 178,273 180,278 181,283 183,289 185,296 186,304 188,312 190,320 191,329 193,338 195,347 197,356 198,365 200,373 202,382 203,390 205,398 207,405 208,412 210,418 212,424 214,429 215,434 217,438 219,442 220,445 222,447 224,449 225,451 227,452 229,452 230,452 232,451 234,449 236,447 237,444 239,441 241,437 242,432 244,427 246,421 247,414 249,407 251,399 252,390 254,381 256,372 258,362 259,352 261,342 263,331 264,321 266,310 268,301 269,291 271,282 273,274 274,267 276,260 278,255 280,250 281,247 283,246 285,245 286,246 288,248 290,251 291,255 293,260 295,266 296,273 298,280 300,288 302,296 303,304 305,312 307,320 308,328 310,336 312,342 313,348 315,354 317,358 318,362 320,365 322,367 324,369 325,370 327,370 329,369 330,368 332,367 334,365 335,363 337,361 339,359 341,357 342,355 344,354 346,353 347,352 349,352 351,352 352,352 354,354 356,355 357,357 359,360 361,363 363,366 364,369 366,373 368,377 369,381 371,384 373,388 374,392 376,395 378,399 379,402 381,405 383,408 385,410 386,412 388,414 390,416 391,418 393,419 395,421 396,422 398,423 400,425 401,426 403,427 405,428 407,430 408,431 410,432 412,433 413,435 415,436 417,438 418,439 420,441 422,442 423,443 425,445 427,446 429,447 430,449 432,450 434,451 435,452 437,453 439,454 440,455 442,455 444,456 445,457 447,457 449,458 451,458 452,459 454,459 456,459 457,459 459,459 461,459 462,458 464,458 466,457 468,456 469,454 471,452 473,450 474,447 476,444 478,440 479,435 481,430 483,425 484,418 486,411 488,403 490,395 491,386 493,376 495,365 496,354 498,342 500,329 501,317 503,303 505,290 506,276 508,263 510,249 512,236 513,222 515,209 517,197 518,185 520,174 522,163 523,153 525,143 527,135 528,127 530,120 532,114 534,108 535,104 537,100 539,97 540,94 542,93 544,92 545,92 547,92 549,93 550,95 552,98 554,101 556,105 557,109 559,114 561,120 562,126 564,133 566,140 567,147 569,155 571,163 573,172 574,181 576,190 578,199 579,208 581,218 583,227 584,237 586,246 588,255 589,265 591,274 593,283 595,292 596,301 598,309 600,318 601,326 603,334 605,341 606,349 608,356 610,363 611,369 613,376 615,382 617,388 618,393 620,398 622,403 623,408 625,412 627,416 628,420 630,423 632,426 633,429 635,431 637,433 639,435 640,436 642,437 644,437 645,437 647,437 649,436 650,434 652,432 654,430 655,427 657,423 659,419 661,415 662,410 664,404 666,398 667,391 669,384 671,377 672,369 674,361 676,352 677,344 679,335 681,327 683,318 684,310 686,301 688,293 689,286 691,278 693,272 694,265 696,260 698,255 700,251 701,247 703,244 705,242 706,241 708,240 710,240 711,241 713,243 715,245 716,248 718,252 720,256 722,260 723,265 725,271 727,277 728,283 730,290 732,297 733,304 735,311 737,318 738,325 740,332 742,339 744,347 745,354 747,360 749,367 750,374 752,380 754,386 755,392 757,397 759,403 760,408 762,412 764,417 766,421 767,424 769,428 771,431 772,433 774,436 776,438 777,439 779,441 781,442 782,442 784,443 786,443 788,442 789,442 791,440 793,439 794,438 796,436 798,434 799,431 801,429 803,426 804,423 806,420 808,417 810,414 811,411 813,408 815,405 816,402 818,400 820,397 821,395 823,393 825,391 827,390 828,389 830,388 832,387 833,387 835,387 837,388 838,388 840,389 842,391 843,392 845,394 847,396 849,399 850,401 852,404 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="555,473 555,103 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

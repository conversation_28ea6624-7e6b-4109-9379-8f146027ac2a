<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/complex:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="410" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,410 86,410 "/>
<text x="77" y="330" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,330 86,330 "/>
<text x="77" y="249" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,249 86,249 "/>
<text x="77" y="168" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,168 86,168 "/>
<text x="77" y="88" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,88 86,88 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="149" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="149,473 149,478 "/>
<text x="253" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
110
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="253,473 253,478 "/>
<text x="356" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="356,473 356,478 "/>
<text x="460" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
130
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="460,473 460,478 "/>
<text x="564" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="564,473 564,478 "/>
<text x="668" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="668,473 668,478 "/>
<text x="771" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="771,473 771,478 "/>
<text x="875" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
170
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="875,473 875,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,470 88,470 90,469 92,468 93,468 95,467 97,466 98,465 100,464 102,464 103,463 105,462 107,461 109,460 110,459 112,458 114,457 115,456 117,456 119,455 120,454 122,453 124,452 125,451 127,450 129,450 131,449 132,448 134,447 136,446 137,446 139,445 141,444 142,443 144,442 146,441 147,440 149,439 151,438 153,437 154,436 156,434 158,433 159,431 161,430 163,428 164,426 166,425 168,423 169,421 171,419 173,417 175,415 176,413 178,412 180,410 181,408 183,406 185,404 186,402 188,401 190,399 191,398 193,396 195,395 197,393 198,392 200,391 202,390 203,389 205,388 207,387 208,386 210,385 212,384 214,383 215,382 217,381 219,380 220,379 222,378 224,377 225,376 227,374 229,373 230,372 232,370 234,369 236,367 237,366 239,364 241,363 242,361 244,359 246,357 247,355 249,353 251,351 252,349 254,347 256,345 258,344 259,342 261,340 263,338 264,336 266,334 268,332 269,331 271,329 273,327 274,326 276,324 278,323 280,322 281,320 283,319 285,318 286,317 288,316 290,314 291,313 293,312 295,311 296,309 298,308 300,306 302,305 303,303 305,301 307,299 308,297 310,295 312,292 313,290 315,287 317,285 318,282 320,279 322,276 324,273 325,270 327,267 329,264 330,261 332,258 334,255 335,253 337,250 339,248 341,246 342,244 344,242 346,241 347,240 349,239 351,238 352,238 354,238 356,238 357,239 359,240 361,241 363,242 364,243 366,245 368,246 369,248 371,250 373,251 374,253 376,254 378,256 379,257 381,258 383,258 385,259 386,259 388,259 390,258 391,258 393,257 395,255 396,254 398,252 400,250 401,247 403,245 405,242 407,239 408,236 410,233 412,230 413,227 415,225 417,222 418,219 420,217 422,215 423,213 425,212 427,211 429,210 430,209 432,209 434,209 435,209 437,210 439,210 440,211 442,212 444,213 445,214 447,216 449,217 451,218 452,219 454,221 456,222 457,223 459,224 461,225 462,225 464,226 466,226 468,227 469,227 471,227 473,227 474,226 476,226 478,225 479,224 481,223 483,221 484,220 486,218 488,215 490,213 491,211 493,208 495,205 496,202 498,199 500,196 501,192 503,189 505,186 506,183 508,180 510,177 512,174 513,172 515,169 517,167 518,165 520,163 522,162 523,160 525,159 527,158 528,157 530,156 532,155 534,154 535,153 537,152 539,151 540,150 542,148 544,147 545,145 547,143 549,141 550,138 552,135 554,133 556,129 557,126 559,123 561,120 562,116 564,113 566,110 567,107 569,104 571,101 573,99 574,97 576,95 578,94 579,94 581,93 583,93 584,94 586,95 588,96 589,98 591,100 593,102 595,104 596,107 598,110 600,113 601,116 603,119 605,122 606,125 608,129 610,132 611,135 613,138 615,142 617,145 618,148 620,151 622,154 623,158 625,161 627,164 628,167 630,171 632,174 633,177 635,180 637,183 639,186 640,188 642,191 644,193 645,195 647,197 649,199 650,201 652,202 654,204 655,205 657,206 659,207 661,208 662,209 664,209 666,210 667,211 669,212 671,214 672,215 674,217 676,218 677,220 679,223 681,225 683,228 684,230 686,233 688,236 689,239 691,242 693,245 694,248 696,251 698,254 700,257 701,259 703,262 705,264 706,266 708,267 710,269 711,270 713,271 715,272 716,273 718,274 720,275 722,276 723,276 725,277 727,278 728,279 730,280 732,281 733,282 735,283 737,284 738,286 740,287 742,289 744,291 745,293 747,295 749,297 750,299 752,301 754,303 755,305 757,307 759,309 760,312 762,314 764,316 766,318 767,320 769,322 771,324 772,326 774,328 776,330 777,331 779,333 781,335 782,336 784,338 786,339 788,341 789,342 791,344 793,345 794,346 796,348 798,349 799,350 801,351 803,352 804,354 806,355 808,356 810,357 811,358 813,359 815,360 816,361 818,363 820,364 821,365 823,366 825,367 827,368 828,369 830,370 832,372 833,373 835,374 837,375 838,377 840,378 842,380 843,382 845,383 847,385 849,387 850,389 852,391 854,393 855,396 857,398 859,400 860,403 862,405 864,408 865,410 867,413 869,415 871,418 872,420 874,423 876,425 877,428 879,430 881,432 882,434 884,436 886,439 887,441 889,442 891,444 893,446 894,448 896,449 898,451 899,453 901,454 903,455 904,457 906,458 908,459 909,461 911,462 913,463 915,464 916,465 918,466 920,467 921,468 923,469 925,469 926,470 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,426 166,425 168,423 169,421 171,419 173,417 175,415 176,413 178,412 180,410 181,408 183,406 185,404 186,402 188,401 190,399 191,398 193,396 195,395 197,393 198,392 200,391 202,390 203,389 205,388 207,387 208,386 210,385 212,384 214,383 215,382 217,381 219,380 220,379 222,378 224,377 225,376 227,374 229,373 230,372 232,370 234,369 236,367 237,366 239,364 241,363 242,361 244,359 246,357 247,355 249,353 251,351 252,349 254,347 256,345 258,344 259,342 261,340 263,338 264,336 266,334 268,332 269,331 271,329 273,327 274,326 276,324 278,323 280,322 281,320 283,319 285,318 286,317 288,316 290,314 291,313 293,312 295,311 296,309 298,308 300,306 302,305 303,303 305,301 307,299 308,297 310,295 312,292 313,290 315,287 317,285 318,282 320,279 322,276 324,273 325,270 327,267 329,264 330,261 332,258 334,255 335,253 337,250 339,248 341,246 342,244 344,242 346,241 347,240 349,239 351,238 352,238 354,238 356,238 357,239 359,240 361,241 363,242 364,243 366,245 368,246 369,248 371,250 373,251 374,253 376,254 378,256 379,257 381,258 383,258 385,259 386,259 388,259 390,258 391,258 393,257 395,255 396,254 398,252 400,250 401,247 403,245 405,242 407,239 408,236 410,233 412,230 413,227 415,225 417,222 418,219 420,217 422,215 423,213 425,212 427,211 429,210 430,209 432,209 434,209 435,209 437,210 439,210 440,211 442,212 444,213 445,214 447,216 449,217 451,218 452,219 454,221 456,222 457,223 459,224 461,225 462,225 464,226 466,226 468,227 469,227 471,227 473,227 474,226 476,226 478,225 479,224 481,223 483,221 484,220 486,218 488,215 490,213 491,211 493,208 495,205 496,202 498,199 500,196 501,192 503,189 505,186 506,183 508,180 510,177 512,174 513,172 515,169 517,167 518,165 520,163 522,162 523,160 525,159 527,158 528,157 530,156 532,155 534,154 535,153 537,152 539,151 540,150 542,148 544,147 545,145 547,143 549,141 550,138 552,135 554,133 556,129 557,126 559,123 561,120 562,116 564,113 566,110 567,107 569,104 571,101 573,99 574,97 576,95 578,94 579,94 581,93 583,93 584,94 586,95 588,96 589,98 591,100 593,102 595,104 596,107 598,110 600,113 601,116 603,119 605,122 606,125 608,129 610,132 611,135 613,138 615,142 617,145 618,148 620,151 622,154 623,158 625,161 627,164 628,167 630,171 632,174 633,177 635,180 637,183 639,186 640,188 642,191 644,193 645,195 647,197 649,199 650,201 652,202 654,204 655,205 657,206 659,207 661,208 662,209 664,209 666,210 667,211 669,212 671,214 672,215 674,217 676,218 677,220 679,223 681,225 683,228 684,230 686,233 688,236 689,239 691,242 693,245 694,248 696,251 698,254 700,257 701,259 703,262 705,264 706,266 708,267 710,269 711,270 713,271 715,272 716,273 718,274 720,275 722,276 723,276 725,277 727,278 728,279 730,280 732,281 733,282 735,283 737,284 738,286 740,287 742,289 744,291 745,293 747,295 749,297 750,299 752,301 754,303 755,305 757,307 759,309 760,312 762,314 764,316 766,318 767,320 769,322 771,324 772,326 774,328 776,330 777,331 779,333 781,335 782,336 784,338 786,339 788,341 789,342 791,344 793,345 794,346 796,348 798,349 799,350 801,351 803,352 804,354 806,355 808,356 810,357 811,358 813,359 815,360 816,361 818,363 820,364 821,365 823,366 825,367 827,368 828,369 830,370 832,372 833,373 835,374 837,375 838,377 840,378 842,380 843,382 845,383 847,385 849,387 850,389 852,391 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="601,473 601,115 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

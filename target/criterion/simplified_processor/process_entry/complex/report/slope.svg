<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
simplified_processor/process_entry/complex:slope
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="422" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,422 86,422 "/>
<text x="77" y="371" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,371 86,371 "/>
<text x="77" y="320" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,320 86,320 "/>
<text x="77" y="268" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,268 86,268 "/>
<text x="77" y="217" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,217 86,217 "/>
<text x="77" y="166" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,166 86,166 "/>
<text x="77" y="114" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,114 86,114 "/>
<text x="77" y="63" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,63 86,63 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="183" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="183,473 183,478 "/>
<text x="317" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="317,473 317,478 "/>
<text x="450" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="450,473 450,478 "/>
<text x="584" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="584,473 584,478 "/>
<text x="717" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="717,473 717,478 "/>
<text x="850" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="850,473 850,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,471 95,471 97,470 98,469 100,469 102,468 103,467 105,465 107,464 109,463 110,461 112,459 114,457 115,455 117,452 119,450 120,447 122,443 124,440 125,436 127,432 129,428 131,423 132,418 134,413 136,408 137,402 139,396 141,390 142,384 144,377 146,371 147,364 149,357 151,350 153,343 154,336 156,330 158,323 159,316 161,310 163,303 164,297 166,292 168,286 169,281 171,277 173,272 175,269 176,265 178,263 180,261 181,259 183,258 185,257 186,257 188,258 190,259 191,260 193,262 195,265 197,267 198,271 200,274 202,278 203,283 205,287 207,292 208,297 210,302 212,307 214,312 215,317 217,322 219,327 220,332 222,337 224,341 225,345 227,348 229,351 230,354 232,356 234,358 236,359 237,360 239,360 241,360 242,359 244,357 246,355 247,352 249,348 251,344 252,339 254,334 256,328 258,322 259,315 261,308 263,300 264,292 266,284 268,275 269,266 271,257 273,248 274,238 276,229 278,219 280,210 281,201 283,191 285,182 286,173 288,165 290,157 291,149 293,141 295,134 296,127 298,121 300,116 302,111 303,106 305,102 307,99 308,96 310,94 312,93 313,92 315,92 317,92 318,93 320,95 322,97 324,99 325,102 327,106 329,110 330,114 332,119 334,124 335,130 337,135 339,141 341,147 342,153 344,159 346,165 347,172 349,178 351,184 352,190 354,195 356,201 357,206 359,211 361,216 363,220 364,224 366,228 368,231 369,234 371,236 373,238 374,240 376,241 378,241 379,242 381,242 383,241 385,240 386,239 388,237 390,235 391,232 393,230 395,227 396,223 398,220 400,216 401,213 403,209 405,205 407,201 408,197 410,193 412,189 413,185 415,181 417,177 418,173 420,170 422,166 423,163 425,160 427,157 429,155 430,152 432,150 434,149 435,147 437,146 439,145 440,144 442,144 444,144 445,144 447,144 449,145 451,146 452,148 454,149 456,151 457,154 459,156 461,159 462,161 464,164 466,168 468,171 469,174 471,178 473,181 474,185 476,189 478,192 479,196 481,200 483,204 484,207 486,211 488,214 490,218 491,221 493,224 495,228 496,231 498,234 500,236 501,239 503,242 505,244 506,246 508,248 510,250 512,252 513,254 515,256 517,257 518,259 520,260 522,261 523,262 525,263 527,264 528,265 530,265 532,266 534,267 535,267 537,267 539,268 540,268 542,269 544,269 545,269 547,270 549,270 550,270 552,270 554,271 556,271 557,271 559,272 561,272 562,273 564,273 566,274 567,274 569,275 571,276 573,276 574,277 576,278 578,279 579,280 581,281 583,282 584,283 586,284 588,286 589,287 591,288 593,290 595,291 596,293 598,294 600,296 601,297 603,299 605,300 606,302 608,303 610,305 611,307 613,308 615,310 617,312 618,313 620,315 622,317 623,318 625,320 627,322 628,324 630,325 632,327 633,329 635,330 637,332 639,333 640,335 642,337 644,338 645,340 647,341 649,342 650,344 652,345 654,347 655,348 657,349 659,351 661,352 662,353 664,354 666,356 667,357 669,358 671,359 672,360 674,362 676,363 677,364 679,365 681,366 683,367 684,368 686,369 688,370 689,371 691,372 693,373 694,375 696,376 698,377 700,378 701,379 703,380 705,381 706,382 708,383 710,384 711,385 713,386 715,387 716,388 718,388 720,389 722,390 723,391 725,392 727,393 728,394 730,394 732,395 733,396 735,397 737,398 738,399 740,399 742,400 744,401 745,402 747,403 749,403 750,404 752,405 754,406 755,407 757,407 759,408 760,409 762,410 764,411 766,411 767,412 769,413 771,413 772,414 774,415 776,416 777,416 779,417 781,418 782,418 784,419 786,420 788,420 789,421 791,422 793,422 794,423 796,424 798,424 799,425 801,426 803,426 804,427 806,428 808,428 810,429 811,430 813,430 815,431 816,431 818,432 820,433 821,433 823,434 825,434 827,435 828,435 830,436 832,436 833,437 835,437 837,438 838,438 840,438 842,439 843,439 845,440 847,440 849,441 850,441 852,441 854,442 855,442 857,443 859,443 860,443 862,444 864,444 865,445 867,445 869,445 871,446 872,446 874,447 876,447 877,447 879,448 881,448 882,449 884,449 886,449 887,450 889,450 891,450 893,451 894,451 896,451 898,452 899,452 901,452 903,453 904,453 906,453 908,454 909,454 911,454 913,454 915,455 916,455 918,455 920,456 921,456 923,456 925,457 926,457 928,457 930,458 932,458 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,297 166,292 168,286 169,281 171,277 173,272 175,269 176,265 178,263 180,261 181,259 183,258 185,257 186,257 188,258 190,259 191,260 193,262 195,265 197,267 198,271 200,274 202,278 203,283 205,287 207,292 208,297 210,302 212,307 214,312 215,317 217,322 219,327 220,332 222,337 224,341 225,345 227,348 229,351 230,354 232,356 234,358 236,359 237,360 239,360 241,360 242,359 244,357 246,355 247,352 249,348 251,344 252,339 254,334 256,328 258,322 259,315 261,308 263,300 264,292 266,284 268,275 269,266 271,257 273,248 274,238 276,229 278,219 280,210 281,201 283,191 285,182 286,173 288,165 290,157 291,149 293,141 295,134 296,127 298,121 300,116 302,111 303,106 305,102 307,99 308,96 310,94 312,93 313,92 315,92 317,92 318,93 320,95 322,97 324,99 325,102 327,106 329,110 330,114 332,119 334,124 335,130 337,135 339,141 341,147 342,153 344,159 346,165 347,172 349,178 351,184 352,190 354,195 356,201 357,206 359,211 361,216 363,220 364,224 366,228 368,231 369,234 371,236 373,238 374,240 376,241 378,241 379,242 381,242 383,241 385,240 386,239 388,237 390,235 391,232 393,230 395,227 396,223 398,220 400,216 401,213 403,209 405,205 407,201 408,197 410,193 412,189 413,185 415,181 417,177 418,173 420,170 422,166 423,163 425,160 427,157 429,155 430,152 432,150 434,149 435,147 437,146 439,145 440,144 442,144 444,144 445,144 447,144 449,145 451,146 452,148 454,149 456,151 457,154 459,156 461,159 462,161 464,164 466,168 468,171 469,174 471,178 473,181 474,185 476,189 478,192 479,196 481,200 483,204 484,207 486,211 488,214 490,218 491,221 493,224 495,228 496,231 498,234 500,236 501,239 503,242 505,244 506,246 508,248 510,250 512,252 513,254 515,256 517,257 518,259 520,260 522,261 523,262 525,263 527,264 528,265 530,265 532,266 534,267 535,267 537,267 539,268 540,268 542,269 544,269 545,269 547,270 549,270 550,270 552,270 554,271 556,271 557,271 559,272 561,272 562,273 564,273 566,274 567,274 569,275 571,276 573,276 574,277 576,278 578,279 579,280 581,281 583,282 584,283 586,284 588,286 589,287 591,288 593,290 595,291 596,293 598,294 600,296 601,297 603,299 605,300 606,302 608,303 610,305 611,307 613,308 615,310 617,312 618,313 620,315 622,317 623,318 625,320 627,322 628,324 630,325 632,327 633,329 635,330 637,332 639,333 640,335 642,337 644,338 645,340 647,341 649,342 650,344 652,345 654,347 655,348 657,349 659,351 661,352 662,353 664,354 666,356 667,357 669,358 671,359 672,360 674,362 676,363 677,364 679,365 681,366 683,367 684,368 686,369 688,370 689,371 691,372 693,373 694,375 696,376 698,377 700,378 701,379 703,380 705,381 706,382 708,383 710,384 711,385 713,386 715,387 716,388 718,388 720,389 722,390 723,391 725,392 727,393 728,394 730,394 732,395 733,396 735,397 737,398 738,399 740,399 742,400 744,401 745,402 747,403 749,403 750,404 752,405 754,406 755,407 757,407 759,408 760,409 762,410 764,411 766,411 767,412 769,413 771,413 772,414 774,415 776,416 777,416 779,417 781,418 782,418 784,419 786,420 788,420 789,421 791,422 793,422 794,423 796,424 798,424 799,425 801,426 803,426 804,427 806,428 808,428 810,429 811,430 813,430 815,431 816,431 818,432 820,433 821,433 823,434 825,434 827,435 828,435 830,436 832,436 833,437 835,437 837,438 838,438 840,438 842,439 843,439 845,440 847,440 849,441 850,441 852,441 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="443,473 443,144 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

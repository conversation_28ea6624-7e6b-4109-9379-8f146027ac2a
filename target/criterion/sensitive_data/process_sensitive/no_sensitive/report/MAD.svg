<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/no_sensitive:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="430" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,430 86,430 "/>
<text x="77" y="373" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,373 86,373 "/>
<text x="77" y="315" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,315 86,315 "/>
<text x="77" y="258" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,258 86,258 "/>
<text x="77" y="200" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,200 86,200 "/>
<text x="77" y="142" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,142 86,142 "/>
<text x="77" y="85" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,85 86,85 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="193" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="193,473 193,478 "/>
<text x="357" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="357,473 357,478 "/>
<text x="520" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="520,473 520,478 "/>
<text x="683" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="683,473 683,478 "/>
<text x="847" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="847,473 847,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,471 95,471 97,471 98,471 100,470 102,470 103,470 105,470 107,470 109,469 110,469 112,469 114,468 115,468 117,467 119,467 120,466 122,466 124,465 125,465 127,464 129,464 131,463 132,462 134,462 136,461 137,461 139,460 141,459 142,459 144,458 146,457 147,457 149,456 151,455 153,455 154,454 156,453 158,452 159,452 161,451 163,450 164,449 166,448 168,447 169,446 171,445 173,444 175,443 176,442 178,441 180,440 181,439 183,438 185,437 186,436 188,435 190,434 191,434 193,433 195,432 197,431 198,431 200,430 202,429 203,428 205,428 207,427 208,426 210,426 212,425 214,425 215,424 217,423 219,423 220,422 222,422 224,421 225,421 227,420 229,419 230,419 232,418 234,417 236,416 237,415 239,414 241,412 242,411 244,409 246,407 247,405 249,403 251,400 252,397 254,394 256,391 258,387 259,383 261,379 263,375 264,370 266,366 268,361 269,356 271,351 273,346 274,342 276,337 278,332 280,327 281,322 283,317 285,313 286,308 288,303 290,299 291,294 293,289 295,284 296,279 298,274 300,269 302,263 303,257 305,251 307,244 308,237 310,230 312,222 313,215 315,207 317,198 318,190 320,182 322,173 324,165 325,157 327,150 329,143 330,136 332,130 334,125 335,120 337,116 339,112 341,109 342,107 344,106 346,104 347,104 349,103 351,103 352,103 354,103 356,103 357,103 359,103 361,102 363,102 364,101 366,100 368,99 369,98 371,97 373,96 374,95 376,94 378,94 379,93 381,93 383,93 385,94 386,94 388,95 390,97 391,98 393,100 395,102 396,104 398,106 400,108 401,111 403,113 405,115 407,117 408,120 410,122 412,124 413,126 415,128 417,130 418,132 420,135 422,137 423,140 425,143 427,147 429,151 430,156 432,161 434,167 435,173 437,180 439,188 440,196 442,205 444,215 445,224 447,234 449,244 451,255 452,265 454,276 456,286 457,296 459,305 461,315 462,323 464,332 466,339 468,346 469,353 471,359 473,364 474,369 476,373 478,377 479,380 481,383 483,386 484,388 486,390 488,391 490,393 491,394 493,395 495,396 496,397 498,398 500,399 501,399 503,400 505,400 506,401 508,401 510,401 512,401 513,401 515,401 517,401 518,401 520,401 522,400 523,400 525,399 527,399 528,398 530,398 532,398 534,397 535,397 537,397 539,397 540,397 542,397 544,398 545,398 547,399 549,400 550,401 552,402 554,404 556,405 557,407 559,408 561,410 562,412 564,414 566,415 567,417 569,419 571,421 573,422 574,424 576,425 578,427 579,428 581,429 583,430 584,431 586,432 588,432 589,433 591,433 593,434 595,434 596,434 598,434 600,435 601,435 603,435 605,435 606,435 608,435 610,435 611,435 613,435 615,436 617,436 618,436 620,436 622,437 623,437 625,437 627,437 628,438 630,438 632,438 633,438 635,438 637,439 639,439 640,439 642,439 644,439 645,439 647,439 649,440 650,440 652,440 654,440 655,440 657,441 659,441 661,441 662,442 664,442 666,443 667,443 669,444 671,444 672,445 674,446 676,446 677,447 679,448 681,448 683,449 684,450 686,451 688,451 689,452 691,453 693,453 694,454 696,454 698,454 700,455 701,455 703,455 705,455 706,455 708,454 710,454 711,454 713,454 715,453 716,453 718,453 720,452 722,452 723,452 725,451 727,451 728,451 730,451 732,451 733,451 735,452 737,452 738,452 740,453 742,453 744,453 745,454 747,454 749,454 750,455 752,455 754,455 755,455 757,455 759,455 760,455 762,455 764,455 766,454 767,454 769,454 771,453 772,452 774,452 776,451 777,451 779,450 781,450 782,449 784,449 786,449 788,448 789,448 791,448 793,448 794,448 796,448 798,448 799,448 801,448 803,448 804,449 806,449 808,449 810,449 811,449 813,450 815,450 816,450 818,450 820,450 821,450 823,451 825,451 827,451 828,451 830,451 832,451 833,451 835,451 837,452 838,452 840,452 842,452 843,453 845,453 847,454 849,454 850,455 852,455 854,456 855,456 857,457 859,457 860,458 862,458 864,459 865,459 867,460 869,460 871,461 872,461 874,462 876,462 877,463 879,463 881,464 882,464 884,465 886,465 887,465 889,466 891,466 893,466 894,467 896,467 898,467 899,468 901,468 903,468 904,468 906,469 908,469 909,469 911,470 913,470 915,470 916,470 918,471 920,471 921,471 923,471 925,471 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,449 166,448 168,447 169,446 171,445 173,444 175,443 176,442 178,441 180,440 181,439 183,438 185,437 186,436 188,435 190,434 191,434 193,433 195,432 197,431 198,431 200,430 202,429 203,428 205,428 207,427 208,426 210,426 212,425 214,425 215,424 217,423 219,423 220,422 222,422 224,421 225,421 227,420 229,419 230,419 232,418 234,417 236,416 237,415 239,414 241,412 242,411 244,409 246,407 247,405 249,403 251,400 252,397 254,394 256,391 258,387 259,383 261,379 263,375 264,370 266,366 268,361 269,356 271,351 273,346 274,342 276,337 278,332 280,327 281,322 283,317 285,313 286,308 288,303 290,299 291,294 293,289 295,284 296,279 298,274 300,269 302,263 303,257 305,251 307,244 308,237 310,230 312,222 313,215 315,207 317,198 318,190 320,182 322,173 324,165 325,157 327,150 329,143 330,136 332,130 334,125 335,120 337,116 339,112 341,109 342,107 344,106 346,104 347,104 349,103 351,103 352,103 354,103 356,103 357,103 359,103 361,102 363,102 364,101 366,100 368,99 369,98 371,97 373,96 374,95 376,94 378,94 379,93 381,93 383,93 385,94 386,94 388,95 390,97 391,98 393,100 395,102 396,104 398,106 400,108 401,111 403,113 405,115 407,117 408,120 410,122 412,124 413,126 415,128 417,130 418,132 420,135 422,137 423,140 425,143 427,147 429,151 430,156 432,161 434,167 435,173 437,180 439,188 440,196 442,205 444,215 445,224 447,234 449,244 451,255 452,265 454,276 456,286 457,296 459,305 461,315 462,323 464,332 466,339 468,346 469,353 471,359 473,364 474,369 476,373 478,377 479,380 481,383 483,386 484,388 486,390 488,391 490,393 491,394 493,395 495,396 496,397 498,398 500,399 501,399 503,400 505,400 506,401 508,401 510,401 512,401 513,401 515,401 517,401 518,401 520,401 522,400 523,400 525,399 527,399 528,398 530,398 532,398 534,397 535,397 537,397 539,397 540,397 542,397 544,398 545,398 547,399 549,400 550,401 552,402 554,404 556,405 557,407 559,408 561,410 562,412 564,414 566,415 567,417 569,419 571,421 573,422 574,424 576,425 578,427 579,428 581,429 583,430 584,431 586,432 588,432 589,433 591,433 593,434 595,434 596,434 598,434 600,435 601,435 603,435 605,435 606,435 608,435 610,435 611,435 613,435 615,436 617,436 618,436 620,436 622,437 623,437 625,437 627,437 628,438 630,438 632,438 633,438 635,438 637,439 639,439 640,439 642,439 644,439 645,439 647,439 649,440 650,440 652,440 654,440 655,440 657,441 659,441 661,441 662,442 664,442 666,443 667,443 669,444 671,444 672,445 674,446 676,446 677,447 679,448 681,448 683,449 684,450 686,451 688,451 689,452 691,453 693,453 694,454 696,454 698,454 700,455 701,455 703,455 705,455 706,455 708,454 710,454 711,454 713,454 715,453 716,453 718,453 720,452 722,452 723,452 725,451 727,451 728,451 730,451 732,451 733,451 735,452 737,452 738,452 740,453 742,453 744,453 745,454 747,454 749,454 750,455 752,455 754,455 755,455 757,455 759,455 760,455 762,455 764,455 766,454 767,454 769,454 771,453 772,452 774,452 776,451 777,451 779,450 781,450 782,449 784,449 786,449 788,448 789,448 791,448 793,448 794,448 796,448 798,448 799,448 801,448 803,448 804,449 806,449 808,449 810,449 811,449 813,450 815,450 816,450 818,450 820,450 821,450 823,451 825,451 827,451 828,451 830,451 832,451 833,451 835,451 837,452 838,452 840,452 842,452 843,453 845,453 847,454 849,454 850,455 852,455 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="405,473 405,116 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

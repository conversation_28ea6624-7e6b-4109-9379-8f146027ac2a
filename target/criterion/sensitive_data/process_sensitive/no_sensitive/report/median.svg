<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/no_sensitive:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="389" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,389 86,389 "/>
<text x="77" y="302" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,302 86,302 "/>
<text x="77" y="215" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,215 86,215 "/>
<text x="77" y="128" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,128 86,128 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="114" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="114,473 114,478 "/>
<text x="297" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.165
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="297,473 297,478 "/>
<text x="480" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.17
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="480,473 480,478 "/>
<text x="663" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.175
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="663,473 663,478 "/>
<text x="846" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="846,473 846,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,466 88,466 90,465 92,464 93,464 95,463 97,463 98,462 100,462 102,462 103,461 105,461 107,461 109,461 110,460 112,460 114,460 115,460 117,460 119,460 120,460 122,460 124,460 125,460 127,459 129,459 131,459 132,459 134,459 136,459 137,459 139,459 141,459 142,459 144,459 146,459 147,459 149,459 151,458 153,458 154,458 156,458 158,458 159,458 161,458 163,459 164,459 166,459 168,459 169,459 171,459 173,460 175,460 176,460 178,460 180,460 181,460 183,460 185,460 186,460 188,460 190,460 191,459 193,459 195,458 197,458 198,457 200,456 202,456 203,455 205,455 207,454 208,454 210,453 212,453 214,453 215,453 217,453 219,453 220,453 222,453 224,453 225,453 227,454 229,454 230,454 232,454 234,453 236,453 237,453 239,452 241,452 242,451 244,450 246,449 247,448 249,447 251,446 252,445 254,444 256,443 258,442 259,440 261,439 263,438 264,437 266,436 268,435 269,434 271,433 273,432 274,431 276,430 278,429 280,428 281,427 283,426 285,425 286,424 288,423 290,422 291,421 293,420 295,419 296,417 298,416 300,415 302,414 303,413 305,411 307,410 308,409 310,408 312,407 313,406 315,405 317,403 318,402 320,401 322,400 324,399 325,398 327,397 329,395 330,394 332,393 334,392 335,390 337,389 339,387 341,386 342,384 344,383 346,381 347,380 349,378 351,377 352,375 354,374 356,372 357,371 359,369 361,368 363,366 364,364 366,362 368,360 369,358 371,355 373,352 374,349 376,346 378,342 379,338 381,333 383,328 385,322 386,316 388,310 390,303 391,296 393,289 395,281 396,274 398,267 400,259 401,253 403,246 405,241 407,235 408,231 410,227 412,224 413,222 415,220 417,219 418,218 420,218 422,218 423,219 425,219 427,220 429,220 430,220 432,219 434,219 435,217 437,216 439,213 440,210 442,207 444,203 445,198 447,192 449,186 451,179 452,172 454,165 456,157 457,149 459,141 461,133 462,125 464,117 466,110 468,104 469,99 471,95 473,93 474,92 476,93 478,95 479,100 481,106 483,114 484,125 486,137 488,150 490,165 491,182 493,199 495,217 496,236 498,255 500,273 501,292 503,310 505,327 506,343 508,358 510,372 512,385 513,396 515,406 517,414 518,421 520,427 522,431 523,434 525,436 527,437 528,437 530,436 532,434 534,432 535,429 537,426 539,422 540,419 542,415 544,411 545,408 547,405 549,403 550,401 552,399 554,399 556,399 557,399 559,401 561,403 562,405 564,409 566,412 567,416 569,420 571,425 573,429 574,433 576,438 578,441 579,445 581,448 583,451 584,454 586,455 588,457 589,458 591,458 593,458 595,457 596,456 598,455 600,453 601,450 603,448 605,445 606,442 608,439 610,435 611,432 613,429 615,426 617,423 618,420 620,418 622,416 623,414 625,413 627,413 628,413 630,413 632,414 633,415 635,417 637,419 639,421 640,423 642,426 644,429 645,432 647,435 649,438 650,441 652,443 654,446 655,449 657,451 659,453 661,455 662,457 664,458 666,459 667,460 669,460 671,460 672,460 674,459 676,459 677,458 679,456 681,455 683,453 684,451 686,449 688,447 689,444 691,442 693,440 694,438 696,436 698,434 700,433 701,432 703,431 705,431 706,431 708,431 710,432 711,433 713,435 715,436 716,438 718,440 720,442 722,444 723,446 725,449 727,451 728,453 730,455 732,457 733,458 735,460 737,461 738,463 740,464 742,464 744,465 745,466 747,466 749,466 750,466 752,465 754,465 755,464 757,463 759,461 760,460 762,458 764,456 766,454 767,451 769,449 771,446 772,443 774,440 776,437 777,434 779,431 781,428 782,426 784,423 786,421 788,419 789,417 791,416 793,415 794,414 796,413 798,413 799,413 801,413 803,413 804,414 806,414 808,415 810,416 811,417 813,418 815,419 816,420 818,421 820,422 821,422 823,423 825,424 827,425 828,426 830,426 832,427 833,427 835,428 837,428 838,428 840,429 842,429 843,429 845,429 847,430 849,430 850,431 852,431 854,432 855,433 857,434 859,435 860,436 862,437 864,439 865,440 867,442 869,444 871,446 872,447 874,449 876,451 877,453 879,455 881,456 882,458 884,459 886,461 887,462 889,463 891,464 893,465 894,466 896,467 898,468 899,468 901,469 903,469 904,470 906,470 908,470 909,470 911,471 913,471 915,471 916,471 918,471 920,472 921,472 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,459 166,459 168,459 169,459 171,459 173,460 175,460 176,460 178,460 180,460 181,460 183,460 185,460 186,460 188,460 190,460 191,459 193,459 195,458 197,458 198,457 200,456 202,456 203,455 205,455 207,454 208,454 210,453 212,453 214,453 215,453 217,453 219,453 220,453 222,453 224,453 225,453 227,454 229,454 230,454 232,454 234,453 236,453 237,453 239,452 241,452 242,451 244,450 246,449 247,448 249,447 251,446 252,445 254,444 256,443 258,442 259,440 261,439 263,438 264,437 266,436 268,435 269,434 271,433 273,432 274,431 276,430 278,429 280,428 281,427 283,426 285,425 286,424 288,423 290,422 291,421 293,420 295,419 296,417 298,416 300,415 302,414 303,413 305,411 307,410 308,409 310,408 312,407 313,406 315,405 317,403 318,402 320,401 322,400 324,399 325,398 327,397 329,395 330,394 332,393 334,392 335,390 337,389 339,387 341,386 342,384 344,383 346,381 347,380 349,378 351,377 352,375 354,374 356,372 357,371 359,369 361,368 363,366 364,364 366,362 368,360 369,358 371,355 373,352 374,349 376,346 378,342 379,338 381,333 383,328 385,322 386,316 388,310 390,303 391,296 393,289 395,281 396,274 398,267 400,259 401,253 403,246 405,241 407,235 408,231 410,227 412,224 413,222 415,220 417,219 418,218 420,218 422,218 423,219 425,219 427,220 429,220 430,220 432,219 434,219 435,217 437,216 439,213 440,210 442,207 444,203 445,198 447,192 449,186 451,179 452,172 454,165 456,157 457,149 459,141 461,133 462,125 464,117 466,110 468,104 469,99 471,95 473,93 474,92 476,93 478,95 479,100 481,106 483,114 484,125 486,137 488,150 490,165 491,182 493,199 495,217 496,236 498,255 500,273 501,292 503,310 505,327 506,343 508,358 510,372 512,385 513,396 515,406 517,414 518,421 520,427 522,431 523,434 525,436 527,437 528,437 530,436 532,434 534,432 535,429 537,426 539,422 540,419 542,415 544,411 545,408 547,405 549,403 550,401 552,399 554,399 556,399 557,399 559,401 561,403 562,405 564,409 566,412 567,416 569,420 571,425 573,429 574,433 576,438 578,441 579,445 581,448 583,451 584,454 586,455 588,457 589,458 591,458 593,458 595,457 596,456 598,455 600,453 601,450 603,448 605,445 606,442 608,439 610,435 611,432 613,429 615,426 617,423 618,420 620,418 622,416 623,414 625,413 627,413 628,413 630,413 632,414 633,415 635,417 637,419 639,421 640,423 642,426 644,429 645,432 647,435 649,438 650,441 652,443 654,446 655,449 657,451 659,453 661,455 662,457 664,458 666,459 667,460 669,460 671,460 672,460 674,459 676,459 677,458 679,456 681,455 683,453 684,451 686,449 688,447 689,444 691,442 693,440 694,438 696,436 698,434 700,433 701,432 703,431 705,431 706,431 708,431 710,432 711,433 713,435 715,436 716,438 718,440 720,442 722,444 723,446 725,449 727,451 728,453 730,455 732,457 733,458 735,460 737,461 738,463 740,464 742,464 744,465 745,466 747,466 749,466 750,466 752,465 754,465 755,464 757,463 759,461 760,460 762,458 764,456 766,454 767,451 769,449 771,446 772,443 774,440 776,437 777,434 779,431 781,428 782,426 784,423 786,421 788,419 789,417 791,416 793,415 794,414 796,413 798,413 799,413 801,413 803,413 804,414 806,414 808,415 810,416 811,417 813,418 815,419 816,420 818,421 820,422 821,422 823,423 825,424 827,425 828,426 830,426 832,427 833,427 835,428 837,428 838,428 840,429 842,429 843,429 845,429 847,430 849,430 850,431 852,431 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="462,473 462,125 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/no_sensitive:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="437" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,437 86,437 "/>
<text x="77" y="378" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,378 86,378 "/>
<text x="77" y="319" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,319 86,319 "/>
<text x="77" y="259" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,259 86,259 "/>
<text x="77" y="200" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,200 86,200 "/>
<text x="77" y="140" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,140 86,140 "/>
<text x="77" y="81" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,81 86,81 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="133" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="133,473 133,478 "/>
<text x="255" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="255,473 255,478 "/>
<text x="376" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="376,473 376,478 "/>
<text x="497" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
26
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="497,473 497,478 "/>
<text x="619" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
28
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="619,473 619,478 "/>
<text x="740" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="740,473 740,478 "/>
<text x="862" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
32
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="862,473 862,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,470 97,469 98,468 100,468 102,467 103,466 105,466 107,465 109,464 110,463 112,463 114,462 115,461 117,460 119,460 120,459 122,458 124,457 125,456 127,455 129,455 131,454 132,453 134,452 136,451 137,450 139,449 141,448 142,447 144,446 146,445 147,444 149,443 151,442 153,441 154,440 156,439 158,438 159,437 161,435 163,434 164,433 166,432 168,430 169,429 171,428 173,426 175,425 176,423 178,422 180,420 181,419 183,417 185,416 186,414 188,413 190,411 191,409 193,408 195,406 197,404 198,403 200,401 202,399 203,398 205,396 207,394 208,392 210,390 212,389 214,387 215,385 217,383 219,381 220,379 222,377 224,375 225,374 227,372 229,370 230,368 232,366 234,364 236,362 237,360 239,358 241,357 242,355 244,353 246,351 247,349 249,347 251,345 252,343 254,341 256,339 258,337 259,335 261,333 263,330 264,328 266,326 268,324 269,322 271,320 273,317 274,315 276,313 278,310 280,308 281,306 283,303 285,301 286,299 288,296 290,294 291,291 293,289 295,286 296,284 298,281 300,279 302,276 303,274 305,272 307,269 308,267 310,265 312,262 313,260 315,258 317,256 318,253 320,251 322,249 324,247 325,245 327,243 329,241 330,239 332,237 334,234 335,232 337,230 339,228 341,226 342,224 344,222 346,220 347,217 349,215 351,213 352,211 354,209 356,207 357,205 359,203 361,201 363,199 364,198 366,196 368,194 369,192 371,190 373,189 374,187 376,185 378,183 379,181 381,180 383,178 385,176 386,174 388,172 390,170 391,168 393,166 395,165 396,163 398,161 400,159 401,157 403,155 405,153 407,151 408,149 410,147 412,146 413,144 415,142 417,140 418,139 420,137 422,135 423,134 425,132 427,131 429,129 430,127 432,126 434,124 435,123 437,121 439,119 440,118 442,116 444,115 445,113 447,112 449,110 451,109 452,108 454,106 456,105 457,104 459,103 461,102 462,101 464,100 466,99 468,98 469,97 471,97 473,96 474,96 476,95 478,95 479,95 481,94 483,94 484,94 486,94 488,94 490,94 491,94 493,94 495,94 496,94 498,94 500,94 501,94 503,94 505,94 506,94 508,95 510,95 512,95 513,95 515,95 517,96 518,96 520,96 522,97 523,97 525,98 527,98 528,99 530,100 532,100 534,101 535,102 537,103 539,104 540,105 542,106 544,107 545,108 547,109 549,110 550,111 552,112 554,114 556,115 557,116 559,117 561,118 562,120 564,121 566,122 567,123 569,125 571,126 573,127 574,129 576,130 578,131 579,133 581,134 583,136 584,137 586,138 588,140 589,142 591,143 593,145 595,146 596,148 598,150 600,152 601,153 603,155 605,157 606,159 608,161 610,163 611,165 613,167 615,169 617,171 618,173 620,175 622,177 623,180 625,182 627,184 628,186 630,188 632,190 633,192 635,194 637,196 639,198 640,200 642,202 644,204 645,206 647,208 649,210 650,213 652,215 654,217 655,219 657,221 659,224 661,226 662,229 664,231 666,233 667,236 669,238 671,241 672,243 674,246 676,248 677,251 679,253 681,255 683,258 684,260 686,262 688,264 689,266 691,268 693,270 694,272 696,274 698,276 700,278 701,280 703,282 705,284 706,285 708,287 710,289 711,291 713,293 715,295 716,297 718,299 720,301 722,303 723,305 725,307 727,309 728,312 730,314 732,316 733,318 735,321 737,323 738,325 740,327 742,330 744,332 745,334 747,337 749,339 750,341 752,344 754,346 755,348 757,350 759,353 760,355 762,357 764,359 766,361 767,363 769,365 771,367 772,369 774,371 776,372 777,374 779,376 781,378 782,379 784,381 786,383 788,384 789,386 791,387 793,389 794,390 796,392 798,394 799,395 801,397 803,398 804,399 806,401 808,402 810,404 811,405 813,406 815,408 816,409 818,410 820,412 821,413 823,414 825,416 827,417 828,418 830,419 832,421 833,422 835,423 837,424 838,425 840,427 842,428 843,429 845,430 847,431 849,432 850,433 852,435 854,436 855,437 857,438 859,439 860,440 862,441 864,442 865,443 867,444 869,445 871,446 872,447 874,448 876,449 877,450 879,451 881,451 882,452 884,453 886,454 887,455 889,456 891,456 893,457 894,458 896,459 898,459 899,460 901,461 903,461 904,462 906,463 908,463 909,464 911,465 913,465 915,466 916,467 918,467 920,468 921,469 923,469 925,470 926,470 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,433 166,432 168,430 169,429 171,428 173,426 175,425 176,423 178,422 180,420 181,419 183,417 185,416 186,414 188,413 190,411 191,409 193,408 195,406 197,404 198,403 200,401 202,399 203,398 205,396 207,394 208,392 210,390 212,389 214,387 215,385 217,383 219,381 220,379 222,377 224,375 225,374 227,372 229,370 230,368 232,366 234,364 236,362 237,360 239,358 241,357 242,355 244,353 246,351 247,349 249,347 251,345 252,343 254,341 256,339 258,337 259,335 261,333 263,330 264,328 266,326 268,324 269,322 271,320 273,317 274,315 276,313 278,310 280,308 281,306 283,303 285,301 286,299 288,296 290,294 291,291 293,289 295,286 296,284 298,281 300,279 302,276 303,274 305,272 307,269 308,267 310,265 312,262 313,260 315,258 317,256 318,253 320,251 322,249 324,247 325,245 327,243 329,241 330,239 332,237 334,234 335,232 337,230 339,228 341,226 342,224 344,222 346,220 347,217 349,215 351,213 352,211 354,209 356,207 357,205 359,203 361,201 363,199 364,198 366,196 368,194 369,192 371,190 373,189 374,187 376,185 378,183 379,181 381,180 383,178 385,176 386,174 388,172 390,170 391,168 393,166 395,165 396,163 398,161 400,159 401,157 403,155 405,153 407,151 408,149 410,147 412,146 413,144 415,142 417,140 418,139 420,137 422,135 423,134 425,132 427,131 429,129 430,127 432,126 434,124 435,123 437,121 439,119 440,118 442,116 444,115 445,113 447,112 449,110 451,109 452,108 454,106 456,105 457,104 459,103 461,102 462,101 464,100 466,99 468,98 469,97 471,97 473,96 474,96 476,95 478,95 479,95 481,94 483,94 484,94 486,94 488,94 490,94 491,94 493,94 495,94 496,94 498,94 500,94 501,94 503,94 505,94 506,94 508,95 510,95 512,95 513,95 515,95 517,96 518,96 520,96 522,97 523,97 525,98 527,98 528,99 530,100 532,100 534,101 535,102 537,103 539,104 540,105 542,106 544,107 545,108 547,109 549,110 550,111 552,112 554,114 556,115 557,116 559,117 561,118 562,120 564,121 566,122 567,123 569,125 571,126 573,127 574,129 576,130 578,131 579,133 581,134 583,136 584,137 586,138 588,140 589,142 591,143 593,145 595,146 596,148 598,150 600,152 601,153 603,155 605,157 606,159 608,161 610,163 611,165 613,167 615,169 617,171 618,173 620,175 622,177 623,180 625,182 627,184 628,186 630,188 632,190 633,192 635,194 637,196 639,198 640,200 642,202 644,204 645,206 647,208 649,210 650,213 652,215 654,217 655,219 657,221 659,224 661,226 662,229 664,231 666,233 667,236 669,238 671,241 672,243 674,246 676,248 677,251 679,253 681,255 683,258 684,260 686,262 688,264 689,266 691,268 693,270 694,272 696,274 698,276 700,278 701,280 703,282 705,284 706,285 708,287 710,289 711,291 713,293 715,295 716,297 718,299 720,301 722,303 723,305 725,307 727,309 728,312 730,314 732,316 733,318 735,321 737,323 738,325 740,327 742,330 744,332 745,334 747,337 749,339 750,341 752,344 754,346 755,348 757,350 759,353 760,355 762,357 764,359 766,361 767,363 769,365 771,367 772,369 774,371 776,372 777,374 779,376 781,378 782,379 784,381 786,383 788,384 789,386 791,387 793,389 794,390 796,392 798,394 799,395 801,397 803,398 804,399 806,401 808,402 810,404 811,405 813,406 815,408 816,409 818,410 820,412 821,413 823,414 825,416 827,417 828,418 830,419 832,421 833,422 835,423 837,424 838,425 840,427 842,428 843,429 845,430 847,431 849,432 850,433 852,435 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="530,473 530,100 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

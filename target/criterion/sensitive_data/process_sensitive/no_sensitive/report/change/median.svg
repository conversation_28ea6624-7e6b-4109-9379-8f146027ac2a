<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/no_sensitive:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="423" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,423 86,423 "/>
<text x="77" y="356" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,356 86,356 "/>
<text x="77" y="289" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,289 86,289 "/>
<text x="77" y="223" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,223 86,223 "/>
<text x="77" y="156" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,156 86,156 "/>
<text x="77" y="89" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,89 86,89 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="111" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="111,473 111,478 "/>
<text x="188" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="188,473 188,478 "/>
<text x="265" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="265,473 265,478 "/>
<text x="342" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="342,473 342,478 "/>
<text x="419" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="419,473 419,478 "/>
<text x="496" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="496,473 496,478 "/>
<text x="573" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="573,473 573,478 "/>
<text x="650" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="650,473 650,478 "/>
<text x="726" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="726,473 726,478 "/>
<text x="803" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.014
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="803,473 803,478 "/>
<text x="880" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.016
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="880,473 880,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,470 97,470 98,469 100,469 102,468 103,468 105,467 107,467 109,466 110,466 112,465 114,465 115,464 117,464 119,463 120,463 122,462 124,462 125,461 127,461 129,460 131,460 132,459 134,459 136,458 137,458 139,457 141,457 142,456 144,456 146,455 147,455 149,454 151,453 153,453 154,452 156,452 158,451 159,450 161,450 163,449 164,448 166,448 168,447 169,446 171,446 173,445 175,444 176,444 178,443 180,442 181,442 183,441 185,441 186,440 188,439 190,439 191,438 193,438 195,437 197,437 198,436 200,436 202,435 203,435 205,434 207,434 208,433 210,432 212,432 214,431 215,431 217,430 219,429 220,429 222,428 224,427 225,427 227,426 229,425 230,425 232,424 234,423 236,422 237,422 239,421 241,420 242,419 244,418 246,417 247,416 249,415 251,414 252,412 254,411 256,410 258,409 259,407 261,406 263,404 264,403 266,401 268,399 269,398 271,396 273,394 274,392 276,390 278,388 280,386 281,384 283,382 285,379 286,377 288,375 290,373 291,370 293,368 295,365 296,363 298,360 300,358 302,355 303,353 305,350 307,347 308,344 310,341 312,339 313,336 315,333 317,330 318,326 320,323 322,320 324,317 325,314 327,310 329,307 330,304 332,301 334,297 335,294 337,291 339,287 341,284 342,281 344,277 346,274 347,270 349,267 351,263 352,260 354,256 356,252 357,248 359,245 361,241 363,237 364,232 366,228 368,224 369,220 371,216 373,212 374,207 376,203 378,199 379,195 381,191 383,187 385,183 386,179 388,176 390,172 391,169 393,166 395,163 396,160 398,157 400,154 401,152 403,150 405,148 407,146 408,144 410,142 412,140 413,139 415,138 417,136 418,135 420,134 422,133 423,131 425,130 427,129 429,127 430,126 432,124 434,122 435,120 437,118 439,115 440,113 442,110 444,107 445,104 447,101 449,97 451,94 452,90 454,87 456,83 457,79 459,76 461,72 462,69 464,66 466,63 468,61 469,58 471,57 473,55 474,54 476,54 478,53 479,54 481,55 483,56 484,57 486,60 488,62 490,65 491,68 493,71 495,75 496,78 498,82 500,86 501,90 503,94 505,98 506,102 508,106 510,109 512,113 513,117 515,120 517,123 518,127 520,130 522,133 523,136 525,139 527,143 528,146 530,150 532,154 534,158 535,162 537,167 539,172 540,177 542,183 544,189 545,196 547,203 549,209 550,217 552,224 554,231 556,239 557,246 559,253 561,261 562,268 564,274 566,281 567,287 569,293 571,299 573,304 574,309 576,313 578,318 579,321 581,325 583,328 584,331 586,333 588,336 589,338 591,340 593,342 595,343 596,345 598,347 600,348 601,350 603,351 605,353 606,355 608,357 610,359 611,361 613,363 615,365 617,368 618,370 620,372 622,374 623,377 625,379 627,381 628,383 630,385 632,387 633,388 635,390 637,391 639,392 640,393 642,394 644,395 645,395 647,396 649,396 650,396 652,396 654,396 655,396 657,396 659,395 661,395 662,394 664,394 666,394 667,393 669,393 671,392 672,392 674,392 676,392 677,392 679,392 681,392 683,392 684,392 686,393 688,393 689,394 691,395 693,396 694,396 696,397 698,398 700,399 701,400 703,401 705,402 706,403 708,403 710,404 711,405 713,405 715,406 716,406 718,406 720,406 722,406 723,406 725,406 727,406 728,406 730,406 732,405 733,405 735,405 737,405 738,405 740,405 742,405 744,405 745,405 747,405 749,405 750,405 752,405 754,405 755,406 757,406 759,406 760,406 762,407 764,407 766,407 767,408 769,408 771,408 772,408 774,408 776,409 777,409 779,409 781,409 782,409 784,409 786,410 788,410 789,410 791,410 793,410 794,410 796,410 798,409 799,409 801,409 803,409 804,409 806,409 808,409 810,409 811,409 813,409 815,409 816,410 818,410 820,410 821,411 823,411 825,412 827,413 828,413 830,414 832,415 833,416 835,417 837,419 838,420 840,421 842,422 843,424 845,425 847,426 849,427 850,429 852,430 854,431 855,433 857,434 859,435 860,436 862,437 864,438 865,439 867,441 869,442 871,443 872,444 874,445 876,446 877,447 879,448 881,449 882,450 884,451 886,452 887,453 889,454 891,455 893,456 894,457 896,458 898,459 899,460 901,461 903,462 904,463 906,464 908,464 909,465 911,466 913,466 915,467 916,468 918,468 920,469 921,469 923,470 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,448 166,448 168,447 169,446 171,446 173,445 175,444 176,444 178,443 180,442 181,442 183,441 185,441 186,440 188,439 190,439 191,438 193,438 195,437 197,437 198,436 200,436 202,435 203,435 205,434 207,434 208,433 210,432 212,432 214,431 215,431 217,430 219,429 220,429 222,428 224,427 225,427 227,426 229,425 230,425 232,424 234,423 236,422 237,422 239,421 241,420 242,419 244,418 246,417 247,416 249,415 251,414 252,412 254,411 256,410 258,409 259,407 261,406 263,404 264,403 266,401 268,399 269,398 271,396 273,394 274,392 276,390 278,388 280,386 281,384 283,382 285,379 286,377 288,375 290,373 291,370 293,368 295,365 296,363 298,360 300,358 302,355 303,353 305,350 307,347 308,344 310,341 312,339 313,336 315,333 317,330 318,326 320,323 322,320 324,317 325,314 327,310 329,307 330,304 332,301 334,297 335,294 337,291 339,287 341,284 342,281 344,277 346,274 347,270 349,267 351,263 352,260 354,256 356,252 357,248 359,245 361,241 363,237 364,232 366,228 368,224 369,220 371,216 373,212 374,207 376,203 378,199 379,195 381,191 383,187 385,183 386,179 388,176 390,172 391,169 393,166 395,163 396,160 398,157 400,154 401,152 403,150 405,148 407,146 408,144 410,142 412,140 413,139 415,138 417,136 418,135 420,134 422,133 423,131 425,130 427,129 429,127 430,126 432,124 434,122 435,120 437,118 439,115 440,113 442,110 444,107 445,104 447,101 449,97 451,94 452,90 454,87 456,83 457,79 459,76 461,72 462,69 464,66 466,63 468,61 469,58 471,57 473,55 474,54 476,54 478,53 479,54 481,55 483,56 484,57 486,60 488,62 490,65 491,68 493,71 495,75 496,78 498,82 500,86 501,90 503,94 505,98 506,102 508,106 510,109 512,113 513,117 515,120 517,123 518,127 520,130 522,133 523,136 525,139 527,143 528,146 530,150 532,154 534,158 535,162 537,167 539,172 540,177 542,183 544,189 545,196 547,203 549,209 550,217 552,224 554,231 556,239 557,246 559,253 561,261 562,268 564,274 566,281 567,287 569,293 571,299 573,304 574,309 576,313 578,318 579,321 581,325 583,328 584,331 586,333 588,336 589,338 591,340 593,342 595,343 596,345 598,347 600,348 601,350 603,351 605,353 606,355 608,357 610,359 611,361 613,363 615,365 617,368 618,370 620,372 622,374 623,377 625,379 627,381 628,383 630,385 632,387 633,388 635,390 637,391 639,392 640,393 642,394 644,395 645,395 647,396 649,396 650,396 652,396 654,396 655,396 657,396 659,395 661,395 662,394 664,394 666,394 667,393 669,393 671,392 672,392 674,392 676,392 677,392 679,392 681,392 683,392 684,392 686,393 688,393 689,394 691,395 693,396 694,396 696,397 698,398 700,399 701,400 703,401 705,402 706,403 708,403 710,404 711,405 713,405 715,406 716,406 718,406 720,406 722,406 723,406 725,406 727,406 728,406 730,406 732,405 733,405 735,405 737,405 738,405 740,405 742,405 744,405 745,405 747,405 749,405 750,405 752,405 754,405 755,406 757,406 759,406 760,406 762,407 764,407 766,407 767,408 769,408 771,408 772,408 774,408 776,409 777,409 779,409 781,409 782,409 784,409 786,410 788,410 789,410 791,410 793,410 794,410 796,410 798,409 799,409 801,409 803,409 804,409 806,409 808,409 810,409 811,409 813,409 815,409 816,410 818,410 820,410 821,411 823,411 825,412 827,413 828,413 830,414 832,415 833,416 835,417 837,419 838,420 840,421 842,422 843,424 845,425 847,426 849,427 850,429 852,430 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="476,473 476,54 "/>
<rect x="87" y="53" width="563" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

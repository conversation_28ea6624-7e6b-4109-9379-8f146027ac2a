<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/no_sensitive:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="459" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,459 86,459 "/>
<text x="77" y="417" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,417 86,417 "/>
<text x="77" y="376" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,376 86,376 "/>
<text x="77" y="334" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,334 86,334 "/>
<text x="77" y="293" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,293 86,293 "/>
<text x="77" y="251" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,251 86,251 "/>
<text x="77" y="210" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,210 86,210 "/>
<text x="77" y="168" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,168 86,168 "/>
<text x="77" y="127" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
90
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,127 86,127 "/>
<text x="77" y="86" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,86 86,86 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="159" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="159,473 159,478 "/>
<text x="258" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="258,473 258,478 "/>
<text x="357" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="357,473 357,478 "/>
<text x="455" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="455,473 455,478 "/>
<text x="554" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="554,473 554,478 "/>
<text x="652" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="652,473 652,478 "/>
<text x="751" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="751,473 751,478 "/>
<text x="849" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="849,473 849,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,470 97,469 98,469 100,468 102,467 103,467 105,466 107,465 109,464 110,463 112,463 114,462 115,461 117,460 119,459 120,458 122,457 124,456 125,455 127,454 129,453 131,452 132,451 134,450 136,448 137,447 139,446 141,445 142,444 144,442 146,441 147,440 149,439 151,437 153,436 154,435 156,433 158,432 159,431 161,429 163,428 164,426 166,425 168,423 169,422 171,420 173,419 175,417 176,416 178,414 180,412 181,411 183,409 185,407 186,406 188,404 190,403 191,401 193,399 195,398 197,396 198,394 200,393 202,391 203,389 205,388 207,386 208,385 210,383 212,381 214,380 215,378 217,376 219,374 220,373 222,371 224,369 225,367 227,365 229,364 230,362 232,360 234,358 236,356 237,354 239,352 241,349 242,347 244,345 246,343 247,341 249,339 251,336 252,334 254,332 256,329 258,327 259,325 261,322 263,320 264,317 266,315 268,312 269,310 271,307 273,304 274,302 276,299 278,297 280,294 281,291 283,289 285,286 286,284 288,281 290,279 291,276 293,274 295,271 296,269 298,266 300,264 302,262 303,260 305,257 307,255 308,253 310,251 312,248 313,246 315,244 317,242 318,240 320,237 322,235 324,233 325,231 327,228 329,226 330,224 332,221 334,219 335,216 337,214 339,211 341,209 342,206 344,203 346,201 347,198 349,195 351,192 352,190 354,187 356,184 357,181 359,179 361,176 363,173 364,170 366,168 368,165 369,163 371,160 373,158 374,155 376,153 378,151 379,148 381,146 383,144 385,142 386,140 388,138 390,137 391,135 393,133 395,132 396,130 398,129 400,127 401,126 403,124 405,123 407,121 408,120 410,118 412,117 413,116 415,114 417,113 418,111 420,110 422,108 423,106 425,105 427,103 429,102 430,100 432,99 434,97 435,95 437,94 439,92 440,91 442,89 444,88 445,86 447,84 449,83 451,81 452,80 454,78 456,77 457,76 459,74 461,73 462,71 464,70 466,69 468,68 469,66 471,65 473,64 474,63 476,62 478,61 479,60 481,59 483,58 484,58 486,57 488,56 490,56 491,55 493,55 495,54 496,54 498,54 500,54 501,54 503,54 505,53 506,54 508,54 510,54 512,54 513,54 515,54 517,54 518,55 520,55 522,55 523,56 525,56 527,56 528,57 530,57 532,58 534,58 535,59 537,59 539,60 540,60 542,61 544,61 545,62 547,62 549,63 550,63 552,64 554,65 556,66 557,66 559,67 561,68 562,69 564,70 566,71 567,72 569,73 571,74 573,75 574,77 576,78 578,79 579,80 581,82 583,83 584,85 586,86 588,88 589,89 591,91 593,92 595,94 596,95 598,97 600,99 601,100 603,102 605,104 606,106 608,108 610,109 611,111 613,113 615,115 617,117 618,119 620,121 622,123 623,126 625,128 627,130 628,132 630,135 632,137 633,140 635,142 637,145 639,147 640,150 642,153 644,156 645,158 647,161 649,164 650,166 652,169 654,172 655,174 657,177 659,179 661,182 662,184 664,187 666,189 667,191 669,193 671,196 672,198 674,200 676,202 677,204 679,206 681,208 683,210 684,213 686,215 688,217 689,219 691,222 693,224 694,226 696,229 698,231 700,234 701,236 703,239 705,242 706,244 708,247 710,249 711,252 713,255 715,257 716,260 718,263 720,265 722,268 723,270 725,273 727,276 728,278 730,281 732,283 733,286 735,288 737,290 738,293 740,295 742,297 744,300 745,302 747,304 749,306 750,309 752,311 754,313 755,315 757,317 759,320 760,322 762,324 764,326 766,328 767,330 769,333 771,335 772,337 774,339 776,341 777,343 779,346 781,348 782,350 784,352 786,354 788,356 789,358 791,360 793,362 794,364 796,366 798,368 799,370 801,371 803,373 804,375 806,377 808,379 810,381 811,382 813,384 815,386 816,388 818,390 820,392 821,393 823,395 825,397 827,399 828,401 830,402 832,404 833,406 835,408 837,410 838,411 840,413 842,415 843,417 845,418 847,420 849,422 850,423 852,425 854,427 855,428 857,430 859,431 860,433 862,434 864,436 865,437 867,438 869,440 871,441 872,442 874,444 876,445 877,446 879,447 881,448 882,449 884,450 886,451 887,452 889,453 891,453 893,454 894,455 896,456 898,456 899,457 901,458 903,458 904,459 906,460 908,460 909,461 911,462 913,463 915,463 916,464 918,465 920,465 921,466 923,467 925,467 926,468 928,469 930,470 932,470 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,426 166,425 168,423 169,422 171,420 173,419 175,417 176,416 178,414 180,412 181,411 183,409 185,407 186,406 188,404 190,403 191,401 193,399 195,398 197,396 198,394 200,393 202,391 203,389 205,388 207,386 208,385 210,383 212,381 214,380 215,378 217,376 219,374 220,373 222,371 224,369 225,367 227,365 229,364 230,362 232,360 234,358 236,356 237,354 239,352 241,349 242,347 244,345 246,343 247,341 249,339 251,336 252,334 254,332 256,329 258,327 259,325 261,322 263,320 264,317 266,315 268,312 269,310 271,307 273,304 274,302 276,299 278,297 280,294 281,291 283,289 285,286 286,284 288,281 290,279 291,276 293,274 295,271 296,269 298,266 300,264 302,262 303,260 305,257 307,255 308,253 310,251 312,248 313,246 315,244 317,242 318,240 320,237 322,235 324,233 325,231 327,228 329,226 330,224 332,221 334,219 335,216 337,214 339,211 341,209 342,206 344,203 346,201 347,198 349,195 351,192 352,190 354,187 356,184 357,181 359,179 361,176 363,173 364,170 366,168 368,165 369,163 371,160 373,158 374,155 376,153 378,151 379,148 381,146 383,144 385,142 386,140 388,138 390,137 391,135 393,133 395,132 396,130 398,129 400,127 401,126 403,124 405,123 407,121 408,120 410,118 412,117 413,116 415,114 417,113 418,111 420,110 422,108 423,106 425,105 427,103 429,102 430,100 432,99 434,97 435,95 437,94 439,92 440,91 442,89 444,88 445,86 447,84 449,83 451,81 452,80 454,78 456,77 457,76 459,74 461,73 462,71 464,70 466,69 468,68 469,66 471,65 473,64 474,63 476,62 478,61 479,60 481,59 483,58 484,58 486,57 488,56 490,56 491,55 493,55 495,54 496,54 498,54 500,54 501,54 503,54 505,53 506,54 508,54 510,54 512,54 513,54 515,54 517,54 518,55 520,55 522,55 523,56 525,56 527,56 528,57 530,57 532,58 534,58 535,59 537,59 539,60 540,60 542,61 544,61 545,62 547,62 549,63 550,63 552,64 554,65 556,66 557,66 559,67 561,68 562,69 564,70 566,71 567,72 569,73 571,74 573,75 574,77 576,78 578,79 579,80 581,82 583,83 584,85 586,86 588,88 589,89 591,91 593,92 595,94 596,95 598,97 600,99 601,100 603,102 605,104 606,106 608,108 610,109 611,111 613,113 615,115 617,117 618,119 620,121 622,123 623,126 625,128 627,130 628,132 630,135 632,137 633,140 635,142 637,145 639,147 640,150 642,153 644,156 645,158 647,161 649,164 650,166 652,169 654,172 655,174 657,177 659,179 661,182 662,184 664,187 666,189 667,191 669,193 671,196 672,198 674,200 676,202 677,204 679,206 681,208 683,210 684,213 686,215 688,217 689,219 691,222 693,224 694,226 696,229 698,231 700,234 701,236 703,239 705,242 706,244 708,247 710,249 711,252 713,255 715,257 716,260 718,263 720,265 722,268 723,270 725,273 727,276 728,278 730,281 732,283 733,286 735,288 737,290 738,293 740,295 742,297 744,300 745,302 747,304 749,306 750,309 752,311 754,313 755,315 757,317 759,320 760,322 762,324 764,326 766,328 767,330 769,333 771,335 772,337 774,339 776,341 777,343 779,346 781,348 782,350 784,352 786,354 788,356 789,358 791,360 793,362 794,364 796,366 798,368 799,370 801,371 803,373 804,375 806,377 808,379 810,381 811,382 813,384 815,386 816,388 818,390 820,392 821,393 823,395 825,397 827,399 828,401 830,402 832,404 833,406 835,408 837,410 838,411 840,413 842,415 843,417 845,418 847,420 849,422 850,423 852,425 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="504,473 504,54 "/>
<rect x="87" y="53" width="664" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

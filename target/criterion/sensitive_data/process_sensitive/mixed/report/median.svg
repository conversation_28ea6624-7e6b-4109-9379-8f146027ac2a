<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/mixed:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="432" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,432 86,432 "/>
<text x="77" y="391" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,391 86,391 "/>
<text x="77" y="349" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,349 86,349 "/>
<text x="77" y="307" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,307 86,307 "/>
<text x="77" y="266" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,266 86,266 "/>
<text x="77" y="224" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,224 86,224 "/>
<text x="77" y="182" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,182 86,182 "/>
<text x="77" y="140" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,140 86,140 "/>
<text x="77" y="99" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
180
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,99 86,99 "/>
<text x="77" y="57" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,57 86,57 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="101" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.938
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="101,473 101,478 "/>
<text x="224" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.94
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="224,473 224,478 "/>
<text x="347" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.942
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="347,473 347,478 "/>
<text x="471" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.944
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="471,473 471,478 "/>
<text x="594" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.946
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="594,473 594,478 "/>
<text x="717" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.948
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="717,473 717,478 "/>
<text x="841" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.95
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="841,473 841,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,467 88,467 90,466 92,466 93,465 95,465 97,464 98,463 100,462 102,461 103,460 105,459 107,458 109,456 110,455 112,453 114,451 115,449 117,447 119,445 120,443 122,440 124,437 125,435 127,432 129,429 131,426 132,422 134,419 136,416 137,412 139,408 141,405 142,401 144,397 146,393 147,389 149,385 151,380 153,376 154,372 156,367 158,363 159,358 161,353 163,349 164,344 166,339 168,334 169,330 171,325 173,321 175,317 176,313 178,309 180,305 181,302 183,300 185,297 186,296 188,294 190,293 191,293 193,293 195,293 197,294 198,295 200,297 202,298 203,300 205,303 207,305 208,307 210,309 212,312 214,314 215,316 217,317 219,319 220,320 222,321 224,322 225,322 227,323 229,323 230,322 232,322 234,321 236,320 237,319 239,318 241,317 242,315 244,314 246,312 247,310 249,308 251,306 252,303 254,301 256,299 258,296 259,293 261,291 263,288 264,284 266,281 268,277 269,273 271,269 273,265 274,260 276,255 278,249 280,244 281,237 283,231 285,224 286,216 288,209 290,200 291,192 293,184 295,175 296,166 298,158 300,149 302,141 303,133 305,125 307,118 308,112 310,106 312,101 313,97 315,95 317,93 318,92 320,92 322,93 324,95 325,98 327,102 329,107 330,113 332,119 334,125 335,132 337,140 339,147 341,155 342,163 344,171 346,179 347,187 349,194 351,202 352,209 354,216 356,223 357,230 359,236 361,243 363,249 364,255 366,261 368,266 369,272 371,277 373,283 374,288 376,293 378,299 379,304 381,309 383,314 385,319 386,324 388,329 390,334 391,339 393,344 395,348 396,352 398,357 400,360 401,364 403,367 405,370 407,372 408,374 410,376 412,377 413,377 415,378 417,377 418,376 420,375 422,373 423,371 425,368 427,365 429,362 430,359 432,355 434,351 435,347 437,343 439,338 440,334 442,330 444,326 445,322 447,318 449,314 451,311 452,308 454,306 456,304 457,302 459,301 461,301 462,301 464,301 466,303 468,304 469,307 471,310 473,313 474,317 476,321 478,325 479,330 481,335 483,340 484,345 486,350 488,355 490,359 491,364 493,368 495,372 496,375 498,378 500,381 501,383 503,384 505,385 506,385 508,385 510,384 512,382 513,380 515,377 517,373 518,369 520,365 522,360 523,354 525,348 527,343 528,336 530,330 532,324 534,318 535,311 537,305 539,299 540,294 542,288 544,283 545,278 547,274 549,270 550,266 552,263 554,260 556,257 557,255 559,253 561,251 562,249 564,248 566,247 567,247 569,246 571,246 573,246 574,246 576,247 578,247 579,248 581,248 583,249 584,250 586,251 588,252 589,253 591,254 593,256 595,257 596,258 598,259 600,260 601,262 603,263 605,264 606,264 608,265 610,266 611,267 613,267 615,268 617,268 618,268 620,269 622,269 623,269 625,269 627,270 628,270 630,270 632,270 633,270 635,270 637,270 639,270 640,269 642,269 644,268 645,267 647,266 649,265 650,264 652,262 654,261 655,259 657,256 659,254 661,252 662,249 664,246 666,243 667,241 669,238 671,235 672,232 674,229 676,226 677,223 679,221 681,218 683,216 684,214 686,212 688,210 689,209 691,208 693,208 694,207 696,208 698,208 700,209 701,211 703,213 705,216 706,218 708,222 710,226 711,230 713,235 715,240 716,245 718,251 720,256 722,262 723,269 725,275 727,281 728,288 730,294 732,301 733,307 735,313 737,319 738,325 740,331 742,336 744,342 745,347 747,352 749,356 750,361 752,365 754,369 755,372 757,376 759,379 760,382 762,385 764,388 766,391 767,393 769,396 771,398 772,400 774,402 776,404 777,405 779,407 781,408 782,409 784,411 786,412 788,413 789,414 791,415 793,416 794,416 796,417 798,418 799,419 801,419 803,420 804,421 806,422 808,422 810,423 811,424 813,425 815,426 816,426 818,427 820,428 821,428 823,429 825,429 827,430 828,430 830,430 832,429 833,429 835,428 837,428 838,427 840,426 842,424 843,423 845,422 847,420 849,419 850,418 852,416 854,415 855,414 857,413 859,413 860,412 862,412 864,412 865,412 867,413 869,414 871,415 872,416 874,417 876,419 877,421 879,423 881,426 882,428 884,430 886,433 887,435 889,438 891,440 893,443 894,445 896,448 898,450 899,452 901,454 903,456 904,458 906,460 908,461 909,463 911,464 913,465 915,466 916,467 918,468 920,469 921,470 923,470 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,344 166,339 168,334 169,330 171,325 173,321 175,317 176,313 178,309 180,305 181,302 183,300 185,297 186,296 188,294 190,293 191,293 193,293 195,293 197,294 198,295 200,297 202,298 203,300 205,303 207,305 208,307 210,309 212,312 214,314 215,316 217,317 219,319 220,320 222,321 224,322 225,322 227,323 229,323 230,322 232,322 234,321 236,320 237,319 239,318 241,317 242,315 244,314 246,312 247,310 249,308 251,306 252,303 254,301 256,299 258,296 259,293 261,291 263,288 264,284 266,281 268,277 269,273 271,269 273,265 274,260 276,255 278,249 280,244 281,237 283,231 285,224 286,216 288,209 290,200 291,192 293,184 295,175 296,166 298,158 300,149 302,141 303,133 305,125 307,118 308,112 310,106 312,101 313,97 315,95 317,93 318,92 320,92 322,93 324,95 325,98 327,102 329,107 330,113 332,119 334,125 335,132 337,140 339,147 341,155 342,163 344,171 346,179 347,187 349,194 351,202 352,209 354,216 356,223 357,230 359,236 361,243 363,249 364,255 366,261 368,266 369,272 371,277 373,283 374,288 376,293 378,299 379,304 381,309 383,314 385,319 386,324 388,329 390,334 391,339 393,344 395,348 396,352 398,357 400,360 401,364 403,367 405,370 407,372 408,374 410,376 412,377 413,377 415,378 417,377 418,376 420,375 422,373 423,371 425,368 427,365 429,362 430,359 432,355 434,351 435,347 437,343 439,338 440,334 442,330 444,326 445,322 447,318 449,314 451,311 452,308 454,306 456,304 457,302 459,301 461,301 462,301 464,301 466,303 468,304 469,307 471,310 473,313 474,317 476,321 478,325 479,330 481,335 483,340 484,345 486,350 488,355 490,359 491,364 493,368 495,372 496,375 498,378 500,381 501,383 503,384 505,385 506,385 508,385 510,384 512,382 513,380 515,377 517,373 518,369 520,365 522,360 523,354 525,348 527,343 528,336 530,330 532,324 534,318 535,311 537,305 539,299 540,294 542,288 544,283 545,278 547,274 549,270 550,266 552,263 554,260 556,257 557,255 559,253 561,251 562,249 564,248 566,247 567,247 569,246 571,246 573,246 574,246 576,247 578,247 579,248 581,248 583,249 584,250 586,251 588,252 589,253 591,254 593,256 595,257 596,258 598,259 600,260 601,262 603,263 605,264 606,264 608,265 610,266 611,267 613,267 615,268 617,268 618,268 620,269 622,269 623,269 625,269 627,270 628,270 630,270 632,270 633,270 635,270 637,270 639,270 640,269 642,269 644,268 645,267 647,266 649,265 650,264 652,262 654,261 655,259 657,256 659,254 661,252 662,249 664,246 666,243 667,241 669,238 671,235 672,232 674,229 676,226 677,223 679,221 681,218 683,216 684,214 686,212 688,210 689,209 691,208 693,208 694,207 696,208 698,208 700,209 701,211 703,213 705,216 706,218 708,222 710,226 711,230 713,235 715,240 716,245 718,251 720,256 722,262 723,269 725,275 727,281 728,288 730,294 732,301 733,307 735,313 737,319 738,325 740,331 742,336 744,342 745,347 747,352 749,356 750,361 752,365 754,369 755,372 757,376 759,379 760,382 762,385 764,388 766,391 767,393 769,396 771,398 772,400 774,402 776,404 777,405 779,407 781,408 782,409 784,411 786,412 788,413 789,414 791,415 793,416 794,416 796,417 798,418 799,419 801,419 803,420 804,421 806,422 808,422 810,423 811,424 813,425 815,426 816,426 818,427 820,428 821,428 823,429 825,429 827,430 828,430 830,430 832,429 833,429 835,428 837,428 838,427 840,426 842,424 843,423 845,422 847,420 849,419 850,418 852,416 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="463,473 463,301 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

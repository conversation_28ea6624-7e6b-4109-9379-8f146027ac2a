<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/mixed:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="432" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,432 86,432 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="303" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,303 86,303 "/>
<text x="77" y="239" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,239 86,239 "/>
<text x="77" y="175" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,175 86,175 "/>
<text x="77" y="111" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,111 86,111 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="141" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="141,473 141,478 "/>
<text x="252" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="252,473 252,478 "/>
<text x="362" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="362,473 362,478 "/>
<text x="472" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="472,473 472,478 "/>
<text x="583" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="583,473 583,478 "/>
<text x="693" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="693,473 693,478 "/>
<text x="804" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="804,473 804,478 "/>
<text x="914" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="914,473 914,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,470 95,470 97,469 98,469 100,468 102,467 103,466 105,466 107,465 109,464 110,463 112,462 114,461 115,460 117,459 119,458 120,456 122,455 124,454 125,453 127,451 129,450 131,448 132,447 134,445 136,443 137,442 139,440 141,438 142,436 144,434 146,431 147,429 149,427 151,424 153,422 154,419 156,416 158,414 159,411 161,408 163,405 164,402 166,399 168,396 169,392 171,389 173,386 175,382 176,379 178,375 180,371 181,367 183,363 185,359 186,354 188,350 190,345 191,340 193,335 195,329 197,324 198,318 200,312 202,306 203,299 205,292 207,285 208,278 210,271 212,264 214,257 215,249 217,242 219,235 220,228 222,221 224,214 225,207 227,200 229,194 230,188 232,182 234,177 236,171 237,166 239,162 241,157 242,153 244,149 246,145 247,142 249,138 251,135 252,132 254,129 256,126 258,123 259,120 261,117 263,114 264,112 266,109 268,107 269,105 271,103 273,101 274,99 276,98 278,97 280,96 281,95 283,94 285,94 286,94 288,94 290,94 291,94 293,95 295,95 296,96 298,97 300,97 302,98 303,98 305,99 307,99 308,100 310,100 312,100 313,100 315,101 317,101 318,101 320,101 322,102 324,102 325,103 327,103 329,104 330,105 332,106 334,107 335,108 337,109 339,110 341,111 342,112 344,113 346,114 347,114 349,115 351,115 352,116 354,116 356,116 357,116 359,116 361,115 363,115 364,114 366,113 368,112 369,111 371,111 373,110 374,109 376,108 378,107 379,106 381,106 383,105 385,105 386,104 388,104 390,104 391,104 393,104 395,104 396,104 398,104 400,105 401,105 403,106 405,107 407,108 408,109 410,110 412,112 413,113 415,115 417,117 418,119 420,121 422,124 423,126 425,129 427,132 429,135 430,139 432,142 434,146 435,149 437,153 439,157 440,162 442,166 444,170 445,174 447,179 449,183 451,187 452,191 454,195 456,199 457,203 459,206 461,210 462,213 464,216 466,218 468,220 469,222 471,224 473,226 474,227 476,228 478,229 479,230 481,231 483,231 484,232 486,232 488,232 490,233 491,233 493,233 495,234 496,234 498,234 500,235 501,235 503,236 505,236 506,237 508,237 510,238 512,238 513,239 515,239 517,239 518,240 520,240 522,240 523,241 525,241 527,241 528,242 530,242 532,242 534,243 535,243 537,244 539,244 540,245 542,245 544,246 545,247 547,247 549,248 550,249 552,250 554,251 556,252 557,253 559,254 561,256 562,257 564,259 566,260 567,261 569,263 571,265 573,266 574,268 576,269 578,271 579,273 581,274 583,276 584,277 586,279 588,280 589,282 591,283 593,285 595,286 596,287 598,289 600,290 601,291 603,292 605,293 606,294 608,295 610,295 611,296 613,297 615,297 617,297 618,298 620,298 622,298 623,298 625,298 627,298 628,298 630,298 632,298 633,298 635,298 637,298 639,299 640,299 642,299 644,300 645,300 647,301 649,302 650,303 652,303 654,304 655,305 657,306 659,307 661,308 662,308 664,309 666,310 667,310 669,311 671,311 672,311 674,312 676,312 677,312 679,312 681,312 683,312 684,312 686,313 688,313 689,313 691,313 693,314 694,314 696,315 698,316 700,316 701,317 703,318 705,319 706,320 708,321 710,322 711,323 713,324 715,325 716,327 718,328 720,329 722,331 723,333 725,334 727,336 728,338 730,340 732,342 733,344 735,346 737,348 738,350 740,352 742,355 744,357 745,360 747,362 749,365 750,367 752,370 754,373 755,375 757,378 759,381 760,384 762,386 764,389 766,392 767,394 769,397 771,399 772,402 774,404 776,406 777,409 779,411 781,413 782,415 784,416 786,418 788,420 789,422 791,423 793,425 794,426 796,427 798,429 799,430 801,431 803,432 804,434 806,435 808,436 810,437 811,438 813,439 815,440 816,441 818,442 820,443 821,444 823,445 825,446 827,447 828,448 830,448 832,449 833,450 835,450 837,451 838,451 840,452 842,452 843,453 845,453 847,454 849,454 850,455 852,455 854,455 855,456 857,456 859,457 860,457 862,457 864,458 865,458 867,459 869,459 871,459 872,460 874,460 876,460 877,461 879,461 881,462 882,462 884,462 886,463 887,463 889,463 891,463 893,464 894,464 896,464 898,464 899,465 901,465 903,465 904,466 906,466 908,466 909,466 911,467 913,467 915,468 916,468 918,468 920,469 921,469 923,470 925,470 926,470 928,471 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,402 166,399 168,396 169,392 171,389 173,386 175,382 176,379 178,375 180,371 181,367 183,363 185,359 186,354 188,350 190,345 191,340 193,335 195,329 197,324 198,318 200,312 202,306 203,299 205,292 207,285 208,278 210,271 212,264 214,257 215,249 217,242 219,235 220,228 222,221 224,214 225,207 227,200 229,194 230,188 232,182 234,177 236,171 237,166 239,162 241,157 242,153 244,149 246,145 247,142 249,138 251,135 252,132 254,129 256,126 258,123 259,120 261,117 263,114 264,112 266,109 268,107 269,105 271,103 273,101 274,99 276,98 278,97 280,96 281,95 283,94 285,94 286,94 288,94 290,94 291,94 293,95 295,95 296,96 298,97 300,97 302,98 303,98 305,99 307,99 308,100 310,100 312,100 313,100 315,101 317,101 318,101 320,101 322,102 324,102 325,103 327,103 329,104 330,105 332,106 334,107 335,108 337,109 339,110 341,111 342,112 344,113 346,114 347,114 349,115 351,115 352,116 354,116 356,116 357,116 359,116 361,115 363,115 364,114 366,113 368,112 369,111 371,111 373,110 374,109 376,108 378,107 379,106 381,106 383,105 385,105 386,104 388,104 390,104 391,104 393,104 395,104 396,104 398,104 400,105 401,105 403,106 405,107 407,108 408,109 410,110 412,112 413,113 415,115 417,117 418,119 420,121 422,124 423,126 425,129 427,132 429,135 430,139 432,142 434,146 435,149 437,153 439,157 440,162 442,166 444,170 445,174 447,179 449,183 451,187 452,191 454,195 456,199 457,203 459,206 461,210 462,213 464,216 466,218 468,220 469,222 471,224 473,226 474,227 476,228 478,229 479,230 481,231 483,231 484,232 486,232 488,232 490,233 491,233 493,233 495,234 496,234 498,234 500,235 501,235 503,236 505,236 506,237 508,237 510,238 512,238 513,239 515,239 517,239 518,240 520,240 522,240 523,241 525,241 527,241 528,242 530,242 532,242 534,243 535,243 537,244 539,244 540,245 542,245 544,246 545,247 547,247 549,248 550,249 552,250 554,251 556,252 557,253 559,254 561,256 562,257 564,259 566,260 567,261 569,263 571,265 573,266 574,268 576,269 578,271 579,273 581,274 583,276 584,277 586,279 588,280 589,282 591,283 593,285 595,286 596,287 598,289 600,290 601,291 603,292 605,293 606,294 608,295 610,295 611,296 613,297 615,297 617,297 618,298 620,298 622,298 623,298 625,298 627,298 628,298 630,298 632,298 633,298 635,298 637,298 639,299 640,299 642,299 644,300 645,300 647,301 649,302 650,303 652,303 654,304 655,305 657,306 659,307 661,308 662,308 664,309 666,310 667,310 669,311 671,311 672,311 674,312 676,312 677,312 679,312 681,312 683,312 684,312 686,313 688,313 689,313 691,313 693,314 694,314 696,315 698,316 700,316 701,317 703,318 705,319 706,320 708,321 710,322 711,323 713,324 715,325 716,327 718,328 720,329 722,331 723,333 725,334 727,336 728,338 730,340 732,342 733,344 735,346 737,348 738,350 740,352 742,355 744,357 745,360 747,362 749,365 750,367 752,370 754,373 755,375 757,378 759,381 760,384 762,386 764,389 766,392 767,394 769,397 771,399 772,402 774,404 776,406 777,409 779,411 781,413 782,415 784,416 786,418 788,420 789,422 791,423 793,425 794,426 796,427 798,429 799,430 801,431 803,432 804,434 806,435 808,436 810,437 811,438 813,439 815,440 816,441 818,442 820,443 821,444 823,445 825,446 827,447 828,448 830,448 832,449 833,450 835,450 837,451 838,451 840,452 842,452 843,453 845,453 847,454 849,454 850,455 852,455 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="414,473 414,114 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

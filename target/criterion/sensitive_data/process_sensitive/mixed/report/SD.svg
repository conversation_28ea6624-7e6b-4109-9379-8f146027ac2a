<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/mixed:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="433" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,433 86,433 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="304" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,304 86,304 "/>
<text x="77" y="240" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,240 86,240 "/>
<text x="77" y="176" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,176 86,176 "/>
<text x="77" y="112" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,112 86,112 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="166" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="166,473 166,478 "/>
<text x="279" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="279,473 279,478 "/>
<text x="392" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="392,473 392,478 "/>
<text x="505" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="505,473 505,478 "/>
<text x="619" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="619,473 619,478 "/>
<text x="732" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
26
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="732,473 732,478 "/>
<text x="845" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
28
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="845,473 845,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,471 92,471 93,470 95,470 97,469 98,469 100,468 102,468 103,467 105,466 107,466 109,465 110,465 112,464 114,463 115,463 117,462 119,461 120,460 122,459 124,459 125,458 127,457 129,456 131,455 132,454 134,453 136,452 137,451 139,450 141,449 142,448 144,447 146,446 147,445 149,443 151,442 153,441 154,440 156,439 158,438 159,437 161,436 163,435 164,433 166,432 168,431 169,430 171,429 173,428 175,426 176,425 178,424 180,423 181,421 183,420 185,419 186,417 188,416 190,414 191,413 193,412 195,410 197,409 198,407 200,406 202,404 203,403 205,401 207,400 208,398 210,397 212,395 214,393 215,392 217,390 219,389 220,387 222,386 224,384 225,382 227,381 229,379 230,378 232,376 234,374 236,373 237,371 239,369 241,367 242,365 244,363 246,361 247,360 249,358 251,356 252,354 254,351 256,349 258,347 259,345 261,343 263,341 264,339 266,337 268,334 269,332 271,330 273,328 274,326 276,324 278,322 280,320 281,318 283,316 285,314 286,312 288,310 290,308 291,306 293,304 295,302 296,301 298,299 300,297 302,295 303,293 305,291 307,289 308,287 310,286 312,284 313,282 315,280 317,278 318,276 320,274 322,271 324,269 325,267 327,265 329,263 330,261 332,259 334,256 335,254 337,252 339,250 341,247 342,245 344,243 346,240 347,238 349,236 351,233 352,231 354,228 356,226 357,224 359,221 361,219 363,216 364,214 366,211 368,209 369,206 371,204 373,201 374,199 376,196 378,194 379,191 381,189 383,186 385,184 386,182 388,179 390,177 391,175 393,173 395,170 396,168 398,166 400,164 401,162 403,160 405,158 407,156 408,154 410,152 412,150 413,149 415,147 417,145 418,143 420,142 422,140 423,139 425,137 427,135 429,134 430,132 432,131 434,130 435,128 437,127 439,125 440,124 442,123 444,121 445,120 447,118 449,117 451,116 452,115 454,113 456,112 457,111 459,110 461,109 462,108 464,107 466,106 468,105 469,104 471,103 473,102 474,101 476,100 478,100 479,99 481,98 483,98 484,97 486,97 488,96 490,96 491,96 493,95 495,95 496,95 498,94 500,94 501,94 503,94 505,94 506,94 508,94 510,94 512,94 513,94 515,94 517,94 518,94 520,94 522,94 523,94 525,94 527,95 528,95 530,95 532,95 534,95 535,96 537,96 539,96 540,97 542,97 544,98 545,98 547,99 549,99 550,100 552,101 554,102 556,103 557,104 559,105 561,106 562,107 564,108 566,110 567,111 569,112 571,114 573,115 574,117 576,118 578,120 579,121 581,123 583,125 584,126 586,128 588,130 589,131 591,133 593,134 595,136 596,137 598,139 600,140 601,142 603,143 605,145 606,146 608,148 610,149 611,150 613,152 615,153 617,155 618,156 620,158 622,159 623,161 625,162 627,164 628,166 630,167 632,169 633,171 635,173 637,175 639,177 640,179 642,181 644,183 645,185 647,187 649,189 650,191 652,193 654,195 655,198 657,200 659,202 661,204 662,206 664,208 666,210 667,212 669,214 671,216 672,219 674,221 676,223 677,225 679,227 681,230 683,232 684,234 686,237 688,239 689,241 691,244 693,246 694,249 696,251 698,253 700,256 701,258 703,261 705,263 706,266 708,268 710,271 711,273 713,276 715,278 716,281 718,283 720,286 722,288 723,290 725,293 727,295 728,297 730,300 732,302 733,304 735,307 737,309 738,311 740,314 742,316 744,318 745,320 747,323 749,325 750,327 752,329 754,332 755,334 757,336 759,338 760,340 762,342 764,345 766,347 767,349 769,351 771,353 772,355 774,357 776,359 777,361 779,363 781,365 782,367 784,368 786,370 788,372 789,374 791,376 793,378 794,379 796,381 798,383 799,385 801,386 803,388 804,390 806,391 808,393 810,395 811,396 813,398 815,400 816,401 818,403 820,404 821,406 823,407 825,409 827,410 828,412 830,413 832,415 833,416 835,418 837,419 838,420 840,422 842,423 843,424 845,426 847,427 849,428 850,429 852,430 854,432 855,433 857,434 859,435 860,436 862,437 864,439 865,440 867,441 869,442 871,443 872,444 874,445 876,446 877,447 879,448 881,449 882,450 884,451 886,452 887,453 889,454 891,455 893,456 894,456 896,457 898,458 899,459 901,460 903,461 904,461 906,462 908,463 909,464 911,464 913,465 915,466 916,467 918,467 920,468 921,469 923,469 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,433 166,432 168,431 169,430 171,429 173,428 175,426 176,425 178,424 180,423 181,421 183,420 185,419 186,417 188,416 190,414 191,413 193,412 195,410 197,409 198,407 200,406 202,404 203,403 205,401 207,400 208,398 210,397 212,395 214,393 215,392 217,390 219,389 220,387 222,386 224,384 225,382 227,381 229,379 230,378 232,376 234,374 236,373 237,371 239,369 241,367 242,365 244,363 246,361 247,360 249,358 251,356 252,354 254,351 256,349 258,347 259,345 261,343 263,341 264,339 266,337 268,334 269,332 271,330 273,328 274,326 276,324 278,322 280,320 281,318 283,316 285,314 286,312 288,310 290,308 291,306 293,304 295,302 296,301 298,299 300,297 302,295 303,293 305,291 307,289 308,287 310,286 312,284 313,282 315,280 317,278 318,276 320,274 322,271 324,269 325,267 327,265 329,263 330,261 332,259 334,256 335,254 337,252 339,250 341,247 342,245 344,243 346,240 347,238 349,236 351,233 352,231 354,228 356,226 357,224 359,221 361,219 363,216 364,214 366,211 368,209 369,206 371,204 373,201 374,199 376,196 378,194 379,191 381,189 383,186 385,184 386,182 388,179 390,177 391,175 393,173 395,170 396,168 398,166 400,164 401,162 403,160 405,158 407,156 408,154 410,152 412,150 413,149 415,147 417,145 418,143 420,142 422,140 423,139 425,137 427,135 429,134 430,132 432,131 434,130 435,128 437,127 439,125 440,124 442,123 444,121 445,120 447,118 449,117 451,116 452,115 454,113 456,112 457,111 459,110 461,109 462,108 464,107 466,106 468,105 469,104 471,103 473,102 474,101 476,100 478,100 479,99 481,98 483,98 484,97 486,97 488,96 490,96 491,96 493,95 495,95 496,95 498,94 500,94 501,94 503,94 505,94 506,94 508,94 510,94 512,94 513,94 515,94 517,94 518,94 520,94 522,94 523,94 525,94 527,95 528,95 530,95 532,95 534,95 535,96 537,96 539,96 540,97 542,97 544,98 545,98 547,99 549,99 550,100 552,101 554,102 556,103 557,104 559,105 561,106 562,107 564,108 566,110 567,111 569,112 571,114 573,115 574,117 576,118 578,120 579,121 581,123 583,125 584,126 586,128 588,130 589,131 591,133 593,134 595,136 596,137 598,139 600,140 601,142 603,143 605,145 606,146 608,148 610,149 611,150 613,152 615,153 617,155 618,156 620,158 622,159 623,161 625,162 627,164 628,166 630,167 632,169 633,171 635,173 637,175 639,177 640,179 642,181 644,183 645,185 647,187 649,189 650,191 652,193 654,195 655,198 657,200 659,202 661,204 662,206 664,208 666,210 667,212 669,214 671,216 672,219 674,221 676,223 677,225 679,227 681,230 683,232 684,234 686,237 688,239 689,241 691,244 693,246 694,249 696,251 698,253 700,256 701,258 703,261 705,263 706,266 708,268 710,271 711,273 713,276 715,278 716,281 718,283 720,286 722,288 723,290 725,293 727,295 728,297 730,300 732,302 733,304 735,307 737,309 738,311 740,314 742,316 744,318 745,320 747,323 749,325 750,327 752,329 754,332 755,334 757,336 759,338 760,340 762,342 764,345 766,347 767,349 769,351 771,353 772,355 774,357 776,359 777,361 779,363 781,365 782,367 784,368 786,370 788,372 789,374 791,376 793,378 794,379 796,381 798,383 799,385 801,386 803,388 804,390 806,391 808,393 810,395 811,396 813,398 815,400 816,401 818,403 820,404 821,406 823,407 825,409 827,410 828,412 830,413 832,415 833,416 835,418 837,419 838,420 840,422 842,423 843,424 845,426 847,427 849,428 850,429 852,430 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="537,473 537,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

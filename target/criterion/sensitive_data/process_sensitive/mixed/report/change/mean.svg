<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/mixed:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="418" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,418 86,418 "/>
<text x="77" y="340" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,340 86,340 "/>
<text x="77" y="261" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,261 86,261 "/>
<text x="77" y="183" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,183 86,183 "/>
<text x="77" y="105" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,105 86,105 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="147" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.28
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="147,473 147,478 "/>
<text x="272" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.27
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="272,473 272,478 "/>
<text x="398" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.26
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="398,473 398,478 "/>
<text x="523" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="523,473 523,478 "/>
<text x="649" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="649,473 649,478 "/>
<text x="774" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.23
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="774,473 774,478 "/>
<text x="900" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="900,473 900,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,471 95,470 97,470 98,469 100,469 102,468 103,468 105,467 107,466 109,466 110,465 112,465 114,464 115,463 117,463 119,462 120,461 122,461 124,460 125,459 127,458 129,458 131,457 132,456 134,455 136,455 137,454 139,453 141,452 142,451 144,451 146,450 147,449 149,448 151,447 153,446 154,446 156,445 158,444 159,443 161,442 163,441 164,440 166,439 168,439 169,438 171,437 173,436 175,435 176,434 178,433 180,432 181,432 183,431 185,430 186,429 188,428 190,427 191,426 193,425 195,425 197,424 198,423 200,422 202,421 203,420 205,419 207,418 208,417 210,416 212,415 214,414 215,413 217,411 219,410 220,409 222,408 224,407 225,405 227,404 229,403 230,402 232,400 234,399 236,397 237,396 239,395 241,393 242,392 244,390 246,389 247,387 249,385 251,384 252,382 254,381 256,379 258,377 259,376 261,374 263,372 264,371 266,369 268,367 269,365 271,364 273,362 274,360 276,358 278,356 280,355 281,353 283,351 285,349 286,347 288,346 290,344 291,342 293,340 295,339 296,337 298,335 300,333 302,332 303,330 305,328 307,326 308,325 310,323 312,321 313,320 315,318 317,316 318,314 320,313 322,311 324,309 325,307 327,305 329,303 330,301 332,299 334,297 335,295 337,293 339,291 341,289 342,287 344,285 346,283 347,281 349,278 351,276 352,274 354,272 356,270 357,268 359,265 361,263 363,261 364,259 366,257 368,255 369,252 371,250 373,248 374,246 376,244 378,241 379,239 381,237 383,235 385,233 386,231 388,228 390,226 391,224 393,222 395,220 396,217 398,215 400,213 401,211 403,208 405,206 407,204 408,202 410,200 412,197 413,195 415,193 417,191 418,189 420,187 422,185 423,183 425,180 427,178 429,176 430,174 432,172 434,170 435,168 437,166 439,164 440,162 442,161 444,159 445,157 447,155 449,153 451,151 452,149 454,147 456,145 457,143 459,141 461,139 462,137 464,135 466,133 468,132 469,130 471,128 473,126 474,124 476,122 478,120 479,118 481,117 483,115 484,113 486,111 488,109 490,107 491,106 493,104 495,102 496,100 498,99 500,97 501,96 503,94 505,93 506,91 508,90 510,88 512,87 513,86 515,85 517,84 518,83 520,81 522,80 523,79 525,78 527,78 528,77 530,76 532,75 534,74 535,73 537,72 539,71 540,70 542,69 544,69 545,68 547,67 549,66 550,65 552,65 554,64 556,63 557,62 559,62 561,61 562,61 564,60 566,59 567,59 569,58 571,58 573,57 574,57 576,56 578,56 579,55 581,55 583,54 584,54 586,54 588,54 589,54 591,53 593,54 595,54 596,54 598,54 600,54 601,55 603,55 605,56 606,57 608,57 610,58 611,59 613,60 615,62 617,63 618,64 620,65 622,67 623,68 625,70 627,72 628,73 630,75 632,77 633,79 635,81 637,83 639,85 640,87 642,89 644,91 645,93 647,95 649,97 650,99 652,101 654,103 655,105 657,106 659,108 661,110 662,112 664,114 666,116 667,117 669,119 671,121 672,122 674,124 676,126 677,127 679,129 681,131 683,132 684,134 686,136 688,138 689,140 691,141 693,143 694,145 696,147 698,150 700,152 701,154 703,156 705,159 706,161 708,164 710,167 711,169 713,172 715,175 716,177 718,180 720,183 722,186 723,189 725,192 727,195 728,198 730,200 732,203 733,206 735,209 737,212 738,215 740,218 742,220 744,223 745,226 747,229 749,232 750,235 752,238 754,241 755,244 757,247 759,249 760,252 762,255 764,258 766,261 767,264 769,267 771,270 772,273 774,276 776,279 777,282 779,285 781,288 782,291 784,294 786,297 788,299 789,302 791,305 793,308 794,311 796,313 798,316 799,319 801,321 803,324 804,326 806,329 808,331 810,334 811,337 813,339 815,342 816,344 818,347 820,349 821,352 823,354 825,357 827,359 828,362 830,364 832,367 833,369 835,372 837,374 838,376 840,379 842,381 843,384 845,386 847,388 849,390 850,393 852,395 854,397 855,399 857,401 859,404 860,406 862,408 864,410 865,412 867,414 869,416 871,418 872,420 874,422 876,424 877,425 879,427 881,429 882,431 884,432 886,434 887,436 889,437 891,439 893,441 894,442 896,444 898,445 899,447 901,448 903,450 904,451 906,453 908,454 909,455 911,457 913,458 915,460 916,461 918,462 920,464 921,465 923,466 925,467 926,469 928,470 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,440 166,439 168,439 169,438 171,437 173,436 175,435 176,434 178,433 180,432 181,432 183,431 185,430 186,429 188,428 190,427 191,426 193,425 195,425 197,424 198,423 200,422 202,421 203,420 205,419 207,418 208,417 210,416 212,415 214,414 215,413 217,411 219,410 220,409 222,408 224,407 225,405 227,404 229,403 230,402 232,400 234,399 236,397 237,396 239,395 241,393 242,392 244,390 246,389 247,387 249,385 251,384 252,382 254,381 256,379 258,377 259,376 261,374 263,372 264,371 266,369 268,367 269,365 271,364 273,362 274,360 276,358 278,356 280,355 281,353 283,351 285,349 286,347 288,346 290,344 291,342 293,340 295,339 296,337 298,335 300,333 302,332 303,330 305,328 307,326 308,325 310,323 312,321 313,320 315,318 317,316 318,314 320,313 322,311 324,309 325,307 327,305 329,303 330,301 332,299 334,297 335,295 337,293 339,291 341,289 342,287 344,285 346,283 347,281 349,278 351,276 352,274 354,272 356,270 357,268 359,265 361,263 363,261 364,259 366,257 368,255 369,252 371,250 373,248 374,246 376,244 378,241 379,239 381,237 383,235 385,233 386,231 388,228 390,226 391,224 393,222 395,220 396,217 398,215 400,213 401,211 403,208 405,206 407,204 408,202 410,200 412,197 413,195 415,193 417,191 418,189 420,187 422,185 423,183 425,180 427,178 429,176 430,174 432,172 434,170 435,168 437,166 439,164 440,162 442,161 444,159 445,157 447,155 449,153 451,151 452,149 454,147 456,145 457,143 459,141 461,139 462,137 464,135 466,133 468,132 469,130 471,128 473,126 474,124 476,122 478,120 479,118 481,117 483,115 484,113 486,111 488,109 490,107 491,106 493,104 495,102 496,100 498,99 500,97 501,96 503,94 505,93 506,91 508,90 510,88 512,87 513,86 515,85 517,84 518,83 520,81 522,80 523,79 525,78 527,78 528,77 530,76 532,75 534,74 535,73 537,72 539,71 540,70 542,69 544,69 545,68 547,67 549,66 550,65 552,65 554,64 556,63 557,62 559,62 561,61 562,61 564,60 566,59 567,59 569,58 571,58 573,57 574,57 576,56 578,56 579,55 581,55 583,54 584,54 586,54 588,54 589,54 591,53 593,54 595,54 596,54 598,54 600,54 601,55 603,55 605,56 606,57 608,57 610,58 611,59 613,60 615,62 617,63 618,64 620,65 622,67 623,68 625,70 627,72 628,73 630,75 632,77 633,79 635,81 637,83 639,85 640,87 642,89 644,91 645,93 647,95 649,97 650,99 652,101 654,103 655,105 657,106 659,108 661,110 662,112 664,114 666,116 667,117 669,119 671,121 672,122 674,124 676,126 677,127 679,129 681,131 683,132 684,134 686,136 688,138 689,140 691,141 693,143 694,145 696,147 698,150 700,152 701,154 703,156 705,159 706,161 708,164 710,167 711,169 713,172 715,175 716,177 718,180 720,183 722,186 723,189 725,192 727,195 728,198 730,200 732,203 733,206 735,209 737,212 738,215 740,218 742,220 744,223 745,226 747,229 749,232 750,235 752,238 754,241 755,244 757,247 759,249 760,252 762,255 764,258 766,261 767,264 769,267 771,270 772,273 774,276 776,279 777,282 779,285 781,288 782,291 784,294 786,297 788,299 789,302 791,305 793,308 794,311 796,313 798,316 799,319 801,321 803,324 804,326 806,329 808,331 810,334 811,337 813,339 815,342 816,344 818,347 820,349 821,352 823,354 825,357 827,359 828,362 830,364 832,367 833,369 835,372 837,374 838,376 840,379 842,381 843,384 845,386 847,388 849,390 850,393 852,395 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="540,473 540,71 "/>
<rect x="509" y="53" width="0" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/mixed:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="431" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,431 86,431 "/>
<text x="77" y="367" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,367 86,367 "/>
<text x="77" y="303" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,303 86,303 "/>
<text x="77" y="239" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,239 86,239 "/>
<text x="77" y="175" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,175 86,175 "/>
<text x="77" y="111" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,111 86,111 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="148" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.944
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="148,473 148,478 "/>
<text x="260" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.946
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="260,473 260,478 "/>
<text x="371" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.948
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="371,473 371,478 "/>
<text x="483" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.95
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="483,473 483,478 "/>
<text x="595" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.952
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="595,473 595,478 "/>
<text x="706" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.954
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="706,473 706,478 "/>
<text x="818" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.956
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="818,473 818,478 "/>
<text x="929" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.958
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="929,473 929,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,469 97,468 98,468 100,467 102,466 103,465 105,465 107,464 109,463 110,462 112,461 114,461 115,460 117,459 119,458 120,457 122,456 124,455 125,454 127,453 129,452 131,451 132,450 134,449 136,448 137,447 139,446 141,445 142,444 144,443 146,442 147,441 149,439 151,438 153,437 154,436 156,434 158,433 159,432 161,431 163,429 164,428 166,427 168,425 169,424 171,422 173,421 175,419 176,418 178,417 180,415 181,413 183,412 185,410 186,409 188,407 190,406 191,404 193,402 195,401 197,399 198,397 200,395 202,394 203,392 205,390 207,388 208,386 210,384 212,382 214,380 215,378 217,376 219,374 220,372 222,370 224,368 225,366 227,364 229,362 230,360 232,358 234,356 236,354 237,352 239,349 241,347 242,345 244,343 246,341 247,339 249,337 251,335 252,333 254,330 256,328 258,326 259,324 261,322 263,319 264,317 266,315 268,313 269,310 271,308 273,306 274,303 276,301 278,299 280,297 281,294 283,292 285,290 286,287 288,285 290,282 291,280 293,278 295,275 296,273 298,270 300,267 302,265 303,262 305,260 307,257 308,254 310,252 312,249 313,246 315,244 317,241 318,238 320,236 322,233 324,231 325,228 327,226 329,223 330,221 332,219 334,217 335,214 337,212 339,210 341,208 342,206 344,204 346,202 347,200 349,198 351,196 352,194 354,192 356,191 357,189 359,187 361,185 363,183 364,181 366,179 368,177 369,175 371,172 373,170 374,168 376,166 378,164 379,162 381,160 383,157 385,155 386,153 388,151 390,149 391,147 393,145 395,143 396,141 398,139 400,137 401,136 403,134 405,132 407,130 408,129 410,127 412,126 413,124 415,123 417,121 418,120 420,118 422,117 423,116 425,114 427,113 429,112 430,111 432,109 434,108 435,107 437,106 439,105 440,104 442,103 444,102 445,101 447,101 449,100 451,99 452,98 454,98 456,97 457,97 459,96 461,96 462,95 464,95 466,94 468,94 469,94 471,94 473,94 474,94 476,94 478,94 479,94 481,94 483,94 484,95 486,95 488,95 490,96 491,96 493,97 495,97 496,97 498,98 500,98 501,99 503,99 505,99 506,100 508,100 510,100 512,101 513,101 515,101 517,102 518,102 520,102 522,103 523,103 525,104 527,104 528,105 530,105 532,106 534,107 535,108 537,109 539,110 540,111 542,112 544,113 545,114 547,116 549,117 550,118 552,120 554,121 556,123 557,124 559,126 561,127 562,129 564,131 566,132 567,134 569,136 571,138 573,139 574,141 576,143 578,145 579,146 581,148 583,150 584,152 586,153 588,155 589,157 591,159 593,160 595,162 596,164 598,166 600,167 601,169 603,171 605,173 606,174 608,176 610,178 611,180 613,182 615,184 617,186 618,188 620,190 622,192 623,194 625,196 627,198 628,200 630,203 632,205 633,207 635,209 637,211 639,214 640,216 642,218 644,220 645,223 647,225 649,227 650,229 652,231 654,233 655,235 657,237 659,239 661,241 662,243 664,245 666,247 667,249 669,251 671,253 672,255 674,257 676,259 677,261 679,263 681,265 683,267 684,269 686,271 688,274 689,276 691,278 693,280 694,283 696,285 698,288 700,290 701,293 703,295 705,298 706,300 708,303 710,305 711,308 713,310 715,313 716,315 718,318 720,320 722,322 723,325 725,327 727,329 728,332 730,334 732,336 733,338 735,340 737,342 738,344 740,346 742,348 744,350 745,352 747,354 749,356 750,358 752,360 754,362 755,363 757,365 759,367 760,369 762,370 764,372 766,374 767,376 769,377 771,379 772,381 774,382 776,384 777,386 779,387 781,389 782,390 784,392 786,393 788,395 789,396 791,398 793,399 794,400 796,402 798,403 799,404 801,406 803,407 804,409 806,410 808,411 810,412 811,414 813,415 815,416 816,418 818,419 820,420 821,421 823,423 825,424 827,425 828,426 830,427 832,428 833,429 835,430 837,431 838,432 840,433 842,434 843,435 845,436 847,437 849,438 850,439 852,439 854,440 855,441 857,442 859,443 860,444 862,445 864,446 865,446 867,447 869,448 871,449 872,450 874,451 876,452 877,452 879,453 881,454 882,455 884,455 886,456 887,457 889,458 891,458 893,459 894,460 896,460 898,461 899,462 901,462 903,463 904,464 906,464 908,465 909,465 911,466 913,467 915,467 916,468 918,468 920,469 921,469 923,470 925,470 926,471 928,471 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,428 166,427 168,425 169,424 171,422 173,421 175,419 176,418 178,417 180,415 181,413 183,412 185,410 186,409 188,407 190,406 191,404 193,402 195,401 197,399 198,397 200,395 202,394 203,392 205,390 207,388 208,386 210,384 212,382 214,380 215,378 217,376 219,374 220,372 222,370 224,368 225,366 227,364 229,362 230,360 232,358 234,356 236,354 237,352 239,349 241,347 242,345 244,343 246,341 247,339 249,337 251,335 252,333 254,330 256,328 258,326 259,324 261,322 263,319 264,317 266,315 268,313 269,310 271,308 273,306 274,303 276,301 278,299 280,297 281,294 283,292 285,290 286,287 288,285 290,282 291,280 293,278 295,275 296,273 298,270 300,267 302,265 303,262 305,260 307,257 308,254 310,252 312,249 313,246 315,244 317,241 318,238 320,236 322,233 324,231 325,228 327,226 329,223 330,221 332,219 334,217 335,214 337,212 339,210 341,208 342,206 344,204 346,202 347,200 349,198 351,196 352,194 354,192 356,191 357,189 359,187 361,185 363,183 364,181 366,179 368,177 369,175 371,172 373,170 374,168 376,166 378,164 379,162 381,160 383,157 385,155 386,153 388,151 390,149 391,147 393,145 395,143 396,141 398,139 400,137 401,136 403,134 405,132 407,130 408,129 410,127 412,126 413,124 415,123 417,121 418,120 420,118 422,117 423,116 425,114 427,113 429,112 430,111 432,109 434,108 435,107 437,106 439,105 440,104 442,103 444,102 445,101 447,101 449,100 451,99 452,98 454,98 456,97 457,97 459,96 461,96 462,95 464,95 466,94 468,94 469,94 471,94 473,94 474,94 476,94 478,94 479,94 481,94 483,94 484,95 486,95 488,95 490,96 491,96 493,97 495,97 496,97 498,98 500,98 501,99 503,99 505,99 506,100 508,100 510,100 512,101 513,101 515,101 517,102 518,102 520,102 522,103 523,103 525,104 527,104 528,105 530,105 532,106 534,107 535,108 537,109 539,110 540,111 542,112 544,113 545,114 547,116 549,117 550,118 552,120 554,121 556,123 557,124 559,126 561,127 562,129 564,131 566,132 567,134 569,136 571,138 573,139 574,141 576,143 578,145 579,146 581,148 583,150 584,152 586,153 588,155 589,157 591,159 593,160 595,162 596,164 598,166 600,167 601,169 603,171 605,173 606,174 608,176 610,178 611,180 613,182 615,184 617,186 618,188 620,190 622,192 623,194 625,196 627,198 628,200 630,203 632,205 633,207 635,209 637,211 639,214 640,216 642,218 644,220 645,223 647,225 649,227 650,229 652,231 654,233 655,235 657,237 659,239 661,241 662,243 664,245 666,247 667,249 669,251 671,253 672,255 674,257 676,259 677,261 679,263 681,265 683,267 684,269 686,271 688,274 689,276 691,278 693,280 694,283 696,285 698,288 700,290 701,293 703,295 705,298 706,300 708,303 710,305 711,308 713,310 715,313 716,315 718,318 720,320 722,322 723,325 725,327 727,329 728,332 730,334 732,336 733,338 735,340 737,342 738,344 740,346 742,348 744,350 745,352 747,354 749,356 750,358 752,360 754,362 755,363 757,365 759,367 760,369 762,370 764,372 766,374 767,376 769,377 771,379 772,381 774,382 776,384 777,386 779,387 781,389 782,390 784,392 786,393 788,395 789,396 791,398 793,399 794,400 796,402 798,403 799,404 801,406 803,407 804,409 806,410 808,411 810,412 811,414 813,415 815,416 816,418 818,419 820,420 821,421 823,423 825,424 827,425 828,426 830,427 832,428 833,429 835,430 837,431 838,432 840,433 842,434 843,435 845,436 847,437 849,438 850,439 852,439 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="497,473 497,97 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

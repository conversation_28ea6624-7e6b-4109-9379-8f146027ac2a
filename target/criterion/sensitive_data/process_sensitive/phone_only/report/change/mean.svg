<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="405" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,405 86,405 "/>
<text x="77" y="318" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,318 86,318 "/>
<text x="77" y="232" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,232 86,232 "/>
<text x="77" y="145" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,145 86,145 "/>
<text x="77" y="58" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,58 86,58 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="176" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="176,473 176,478 "/>
<text x="288" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="288,473 288,478 "/>
<text x="400" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="400,473 400,478 "/>
<text x="512" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="512,473 512,478 "/>
<text x="624" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="624,473 624,478 "/>
<text x="736" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.9389e-18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="736,473 736,478 "/>
<text x="848" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="848,473 848,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,468 88,468 90,467 92,467 93,466 95,466 97,465 98,465 100,464 102,464 103,463 105,463 107,462 109,462 110,461 112,461 114,460 115,460 117,459 119,459 120,458 122,458 124,457 125,457 127,456 129,456 131,455 132,455 134,455 136,454 137,454 139,453 141,453 142,452 144,452 146,451 147,451 149,450 151,450 153,449 154,449 156,448 158,447 159,447 161,446 163,446 164,445 166,445 168,444 169,443 171,443 173,442 175,441 176,441 178,440 180,439 181,439 183,438 185,437 186,436 188,436 190,435 191,434 193,433 195,432 197,431 198,430 200,429 202,428 203,427 205,426 207,425 208,424 210,423 212,422 214,421 215,419 217,418 219,417 220,416 222,414 224,413 225,412 227,411 229,409 230,408 232,407 234,406 236,404 237,403 239,402 241,401 242,400 244,398 246,397 247,396 249,395 251,394 252,392 254,391 256,390 258,389 259,387 261,386 263,385 264,384 266,382 268,381 269,379 271,378 273,376 274,375 276,374 278,372 280,371 281,369 283,368 285,366 286,365 288,363 290,362 291,360 293,359 295,357 296,356 298,354 300,353 302,351 303,350 305,349 307,347 308,346 310,344 312,343 313,342 315,340 317,339 318,337 320,336 322,334 324,333 325,331 327,330 329,328 330,327 332,325 334,323 335,322 337,320 339,318 341,317 342,315 344,313 346,312 347,310 349,308 351,306 352,304 354,303 356,301 357,299 359,297 361,295 363,293 364,291 366,289 368,287 369,285 371,283 373,281 374,278 376,276 378,274 379,272 381,270 383,268 385,266 386,264 388,261 390,259 391,257 393,255 395,253 396,251 398,249 400,247 401,245 403,243 405,241 407,239 408,237 410,235 412,233 413,231 415,229 417,227 418,225 420,223 422,222 423,220 425,218 427,216 429,214 430,212 432,211 434,209 435,207 437,205 439,203 440,202 442,200 444,198 445,196 447,194 449,192 451,190 452,188 454,186 456,184 457,182 459,180 461,178 462,176 464,174 466,172 468,170 469,168 471,166 473,164 474,162 476,160 478,158 479,156 481,154 483,152 484,151 486,149 488,147 490,146 491,144 493,142 495,141 496,139 498,137 500,136 501,134 503,132 505,131 506,129 508,127 510,126 512,124 513,122 515,120 517,118 518,117 520,115 522,113 523,111 525,110 527,108 528,106 530,104 532,103 534,101 535,99 537,98 539,96 540,95 542,93 544,92 545,90 547,89 549,87 550,86 552,84 554,83 556,82 557,80 559,79 561,77 562,76 564,74 566,73 567,72 569,70 571,69 573,68 574,67 576,65 578,64 579,63 581,62 583,61 584,60 586,59 588,58 589,57 591,57 593,56 595,55 596,55 598,54 600,54 601,54 603,54 605,53 606,54 608,54 610,54 611,54 613,54 615,55 617,55 618,56 620,57 622,57 623,58 625,59 627,60 628,61 630,62 632,63 633,64 635,65 637,66 639,67 640,68 642,69 644,70 645,71 647,72 649,73 650,74 652,75 654,76 655,77 657,78 659,79 661,80 662,81 664,82 666,83 667,84 669,85 671,86 672,87 674,88 676,89 677,90 679,91 681,92 683,93 684,94 686,95 688,96 689,98 691,99 693,100 694,102 696,103 698,105 700,106 701,108 703,110 705,111 706,113 708,115 710,117 711,119 713,121 715,123 716,125 718,127 720,130 722,132 723,134 725,136 727,138 728,140 730,143 732,145 733,147 735,149 737,151 738,153 740,156 742,158 744,160 745,162 747,164 749,167 750,169 752,171 754,173 755,176 757,178 759,181 760,183 762,186 764,188 766,191 767,193 769,196 771,199 772,202 774,204 776,207 777,210 779,213 781,216 782,220 784,223 786,226 788,229 789,233 791,236 793,240 794,243 796,247 798,250 799,254 801,258 803,262 804,265 806,269 808,273 810,277 811,281 813,284 815,288 816,292 818,296 820,300 821,303 823,307 825,311 827,315 828,318 830,322 832,326 833,329 835,333 837,336 838,340 840,343 842,347 843,350 845,353 847,357 849,360 850,363 852,367 854,370 855,373 857,376 859,379 860,383 862,386 864,389 865,392 867,395 869,398 871,401 872,403 874,406 876,409 877,412 879,414 881,417 882,420 884,422 886,425 887,427 889,430 891,432 893,434 894,436 896,439 898,441 899,443 901,445 903,447 904,449 906,451 908,452 909,454 911,456 913,457 915,459 916,461 918,462 920,464 921,465 923,466 925,468 926,469 928,470 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,445 166,445 168,444 169,443 171,443 173,442 175,441 176,441 178,440 180,439 181,439 183,438 185,437 186,436 188,436 190,435 191,434 193,433 195,432 197,431 198,430 200,429 202,428 203,427 205,426 207,425 208,424 210,423 212,422 214,421 215,419 217,418 219,417 220,416 222,414 224,413 225,412 227,411 229,409 230,408 232,407 234,406 236,404 237,403 239,402 241,401 242,400 244,398 246,397 247,396 249,395 251,394 252,392 254,391 256,390 258,389 259,387 261,386 263,385 264,384 266,382 268,381 269,379 271,378 273,376 274,375 276,374 278,372 280,371 281,369 283,368 285,366 286,365 288,363 290,362 291,360 293,359 295,357 296,356 298,354 300,353 302,351 303,350 305,349 307,347 308,346 310,344 312,343 313,342 315,340 317,339 318,337 320,336 322,334 324,333 325,331 327,330 329,328 330,327 332,325 334,323 335,322 337,320 339,318 341,317 342,315 344,313 346,312 347,310 349,308 351,306 352,304 354,303 356,301 357,299 359,297 361,295 363,293 364,291 366,289 368,287 369,285 371,283 373,281 374,278 376,276 378,274 379,272 381,270 383,268 385,266 386,264 388,261 390,259 391,257 393,255 395,253 396,251 398,249 400,247 401,245 403,243 405,241 407,239 408,237 410,235 412,233 413,231 415,229 417,227 418,225 420,223 422,222 423,220 425,218 427,216 429,214 430,212 432,211 434,209 435,207 437,205 439,203 440,202 442,200 444,198 445,196 447,194 449,192 451,190 452,188 454,186 456,184 457,182 459,180 461,178 462,176 464,174 466,172 468,170 469,168 471,166 473,164 474,162 476,160 478,158 479,156 481,154 483,152 484,151 486,149 488,147 490,146 491,144 493,142 495,141 496,139 498,137 500,136 501,134 503,132 505,131 506,129 508,127 510,126 512,124 513,122 515,120 517,118 518,117 520,115 522,113 523,111 525,110 527,108 528,106 530,104 532,103 534,101 535,99 537,98 539,96 540,95 542,93 544,92 545,90 547,89 549,87 550,86 552,84 554,83 556,82 557,80 559,79 561,77 562,76 564,74 566,73 567,72 569,70 571,69 573,68 574,67 576,65 578,64 579,63 581,62 583,61 584,60 586,59 588,58 589,57 591,57 593,56 595,55 596,55 598,54 600,54 601,54 603,54 605,53 606,54 608,54 610,54 611,54 613,54 615,55 617,55 618,56 620,57 622,57 623,58 625,59 627,60 628,61 630,62 632,63 633,64 635,65 637,66 639,67 640,68 642,69 644,70 645,71 647,72 649,73 650,74 652,75 654,76 655,77 657,78 659,79 661,80 662,81 664,82 666,83 667,84 669,85 671,86 672,87 674,88 676,89 677,90 679,91 681,92 683,93 684,94 686,95 688,96 689,98 691,99 693,100 694,102 696,103 698,105 700,106 701,108 703,110 705,111 706,113 708,115 710,117 711,119 713,121 715,123 716,125 718,127 720,130 722,132 723,134 725,136 727,138 728,140 730,143 732,145 733,147 735,149 737,151 738,153 740,156 742,158 744,160 745,162 747,164 749,167 750,169 752,171 754,173 755,176 757,178 759,181 760,183 762,186 764,188 766,191 767,193 769,196 771,199 772,202 774,204 776,207 777,210 779,213 781,216 782,220 784,223 786,226 788,229 789,233 791,236 793,240 794,243 796,247 798,250 799,254 801,258 803,262 804,265 806,269 808,273 810,277 811,281 813,284 815,288 816,292 818,296 820,300 821,303 823,307 825,311 827,315 828,318 830,322 832,326 833,329 835,333 837,336 838,340 840,343 842,347 843,350 845,353 847,357 849,360 850,363 852,367 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="556,473 556,81 "/>
<rect x="624" y="53" width="224" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="415" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,415 86,415 "/>
<text x="77" y="347" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,347 86,347 "/>
<text x="77" y="280" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,280 86,280 "/>
<text x="77" y="213" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,213 86,213 "/>
<text x="77" y="145" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,145 86,145 "/>
<text x="77" y="78" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,78 86,78 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="263" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="263,473 263,478 "/>
<text x="443" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="443,473 443,478 "/>
<text x="623" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="623,473 623,478 "/>
<text x="803" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="803,473 803,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,471 95,470 97,470 98,469 100,469 102,468 103,467 105,467 107,466 109,465 110,464 112,463 114,462 115,461 117,460 119,458 120,457 122,456 124,454 125,452 127,451 129,449 131,447 132,445 134,443 136,441 137,439 139,436 141,434 142,432 144,430 146,428 147,426 149,424 151,422 153,420 154,418 156,417 158,415 159,414 161,412 163,411 164,410 166,409 168,408 169,407 171,406 173,405 175,404 176,403 178,402 180,401 181,400 183,399 185,398 186,397 188,396 190,395 191,393 193,392 195,390 197,388 198,386 200,384 202,381 203,379 205,376 207,373 208,369 210,366 212,362 214,358 215,353 217,349 219,344 220,339 222,333 224,328 225,322 227,316 229,310 230,304 232,298 234,292 236,286 237,280 239,274 241,268 242,263 244,258 246,253 247,248 249,244 251,240 252,237 254,234 256,231 258,229 259,227 261,225 263,224 264,223 266,222 268,221 269,221 271,221 273,221 274,221 276,221 278,221 280,221 281,222 283,222 285,223 286,224 288,224 290,225 291,226 293,227 295,227 296,228 298,229 300,230 302,230 303,230 305,231 307,231 308,231 310,231 312,231 313,230 315,229 317,228 318,227 320,226 322,225 324,223 325,221 327,219 329,216 330,214 332,211 334,207 335,204 337,200 339,195 341,191 342,186 344,180 346,175 347,168 349,162 351,155 352,148 354,140 356,133 357,125 359,117 361,109 363,101 364,94 366,87 368,80 369,74 371,68 373,64 374,60 376,57 378,55 379,54 381,53 383,54 385,56 386,59 388,63 390,67 391,73 393,79 395,85 396,92 398,100 400,108 401,116 403,124 405,132 407,141 408,149 410,157 412,165 413,173 415,181 417,188 418,195 420,202 422,209 423,215 425,221 427,226 429,231 430,236 432,240 434,244 435,248 437,252 439,255 440,258 442,260 444,263 445,265 447,267 449,269 451,271 452,272 454,273 456,274 457,275 459,276 461,277 462,277 464,277 466,277 468,277 469,276 471,275 473,274 474,273 476,271 478,269 479,267 481,264 483,261 484,258 486,255 488,251 490,248 491,244 493,241 495,238 496,235 498,232 500,229 501,227 503,226 505,225 506,225 508,225 510,226 512,228 513,230 515,233 517,237 518,241 520,246 522,251 523,257 525,263 527,269 528,275 530,282 532,289 534,295 535,302 537,309 539,315 540,322 542,328 544,334 545,340 547,346 549,352 550,357 552,363 554,368 556,373 557,378 559,382 561,386 562,391 564,394 566,398 567,402 569,405 571,408 573,411 574,414 576,416 578,418 579,420 581,422 583,424 584,426 586,427 588,428 589,429 591,430 593,431 595,431 596,432 598,432 600,432 601,432 603,431 605,431 606,430 608,429 610,428 611,426 613,425 615,423 617,421 618,419 620,417 622,415 623,413 625,411 627,409 628,406 630,404 632,402 633,400 635,397 637,395 639,394 640,392 642,390 644,389 645,387 647,386 649,385 650,384 652,383 654,382 655,381 657,381 659,380 661,380 662,380 664,380 666,380 667,380 669,380 671,380 672,381 674,381 676,382 677,383 679,384 681,385 683,386 684,387 686,388 688,389 689,391 691,392 693,394 694,395 696,396 698,398 700,399 701,401 703,402 705,403 706,404 708,406 710,407 711,408 713,409 715,409 716,410 718,411 720,411 722,412 723,413 725,413 727,413 728,414 730,414 732,414 733,414 735,415 737,415 738,415 740,415 742,415 744,415 745,415 747,415 749,414 750,414 752,414 754,413 755,413 757,413 759,412 760,412 762,411 764,411 766,410 767,410 769,409 771,409 772,409 774,408 776,408 777,407 779,407 781,407 782,406 784,406 786,406 788,406 789,406 791,406 793,406 794,406 796,406 798,406 799,406 801,407 803,407 804,408 806,409 808,409 810,410 811,411 813,412 815,413 816,414 818,415 820,416 821,417 823,418 825,419 827,421 828,422 830,423 832,424 833,425 835,426 837,427 838,428 840,429 842,430 843,431 845,432 847,432 849,433 850,434 852,434 854,435 855,436 857,436 859,437 860,437 862,438 864,439 865,439 867,440 869,440 871,441 872,441 874,442 876,443 877,443 879,444 881,445 882,445 884,446 886,447 887,447 889,448 891,448 893,449 894,450 896,450 898,451 899,452 901,452 903,453 904,453 906,454 908,455 909,455 911,456 913,457 915,458 916,458 918,459 920,460 921,460 923,461 925,462 926,463 928,464 930,464 932,465 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,410 166,409 168,408 169,407 171,406 173,405 175,404 176,403 178,402 180,401 181,400 183,399 185,398 186,397 188,396 190,395 191,393 193,392 195,390 197,388 198,386 200,384 202,381 203,379 205,376 207,373 208,369 210,366 212,362 214,358 215,353 217,349 219,344 220,339 222,333 224,328 225,322 227,316 229,310 230,304 232,298 234,292 236,286 237,280 239,274 241,268 242,263 244,258 246,253 247,248 249,244 251,240 252,237 254,234 256,231 258,229 259,227 261,225 263,224 264,223 266,222 268,221 269,221 271,221 273,221 274,221 276,221 278,221 280,221 281,222 283,222 285,223 286,224 288,224 290,225 291,226 293,227 295,227 296,228 298,229 300,230 302,230 303,230 305,231 307,231 308,231 310,231 312,231 313,230 315,229 317,228 318,227 320,226 322,225 324,223 325,221 327,219 329,216 330,214 332,211 334,207 335,204 337,200 339,195 341,191 342,186 344,180 346,175 347,168 349,162 351,155 352,148 354,140 356,133 357,125 359,117 361,109 363,101 364,94 366,87 368,80 369,74 371,68 373,64 374,60 376,57 378,55 379,54 381,53 383,54 385,56 386,59 388,63 390,67 391,73 393,79 395,85 396,92 398,100 400,108 401,116 403,124 405,132 407,141 408,149 410,157 412,165 413,173 415,181 417,188 418,195 420,202 422,209 423,215 425,221 427,226 429,231 430,236 432,240 434,244 435,248 437,252 439,255 440,258 442,260 444,263 445,265 447,267 449,269 451,271 452,272 454,273 456,274 457,275 459,276 461,277 462,277 464,277 466,277 468,277 469,276 471,275 473,274 474,273 476,271 478,269 479,267 481,264 483,261 484,258 486,255 488,251 490,248 491,244 493,241 495,238 496,235 498,232 500,229 501,227 503,226 505,225 506,225 508,225 510,226 512,228 513,230 515,233 517,237 518,241 520,246 522,251 523,257 525,263 527,269 528,275 530,282 532,289 534,295 535,302 537,309 539,315 540,322 542,328 544,334 545,340 547,346 549,352 550,357 552,363 554,368 556,373 557,378 559,382 561,386 562,391 564,394 566,398 567,402 569,405 571,408 573,411 574,414 576,416 578,418 579,420 581,422 583,424 584,426 586,427 588,428 589,429 591,430 593,431 595,431 596,432 598,432 600,432 601,432 603,431 605,431 606,430 608,429 610,428 611,426 613,425 615,423 617,421 618,419 620,417 622,415 623,413 625,411 627,409 628,406 630,404 632,402 633,400 635,397 637,395 639,394 640,392 642,390 644,389 645,387 647,386 649,385 650,384 652,383 654,382 655,381 657,381 659,380 661,380 662,380 664,380 666,380 667,380 669,380 671,380 672,381 674,381 676,382 677,383 679,384 681,385 683,386 684,387 686,388 688,389 689,391 691,392 693,394 694,395 696,396 698,398 700,399 701,401 703,402 705,403 706,404 708,406 710,407 711,408 713,409 715,409 716,410 718,411 720,411 722,412 723,413 725,413 727,413 728,414 730,414 732,414 733,414 735,415 737,415 738,415 740,415 742,415 744,415 745,415 747,415 749,414 750,414 752,414 754,413 755,413 757,413 759,412 760,412 762,411 764,411 766,410 767,410 769,409 771,409 772,409 774,408 776,408 777,407 779,407 781,407 782,406 784,406 786,406 788,406 789,406 791,406 793,406 794,406 796,406 798,406 799,406 801,407 803,407 804,408 806,409 808,409 810,410 811,411 813,412 815,413 816,414 818,415 820,416 821,417 823,418 825,419 827,421 828,422 830,423 832,424 833,425 835,426 837,427 838,428 840,429 842,430 843,431 845,432 847,432 849,433 850,434 852,434 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="398,473 398,98 "/>
<rect x="87" y="53" width="536" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

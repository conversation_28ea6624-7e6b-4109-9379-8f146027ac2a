<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="437" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,437 86,437 "/>
<text x="77" y="379" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,379 86,379 "/>
<text x="77" y="322" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,322 86,322 "/>
<text x="77" y="265" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,265 86,265 "/>
<text x="77" y="207" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,207 86,207 "/>
<text x="77" y="150" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,150 86,150 "/>
<text x="77" y="92" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,92 86,92 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="156" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.385
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="156,473 156,478 "/>
<text x="311" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.39
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="311,473 311,478 "/>
<text x="465" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.395
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="465,473 465,478 "/>
<text x="620" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="620,473 620,478 "/>
<text x="775" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.405
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="775,473 775,478 "/>
<text x="929" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.41
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="929,473 929,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,469 88,468 90,468 92,467 93,466 95,466 97,465 98,464 100,464 102,463 103,462 105,462 107,461 109,460 110,460 112,459 114,458 115,457 117,457 119,456 120,455 122,454 124,453 125,452 127,451 129,450 131,449 132,448 134,447 136,446 137,445 139,444 141,443 142,442 144,440 146,439 147,438 149,437 151,435 153,434 154,433 156,431 158,430 159,429 161,427 163,426 164,425 166,423 168,422 169,421 171,419 173,418 175,416 176,415 178,413 180,412 181,410 183,409 185,407 186,406 188,404 190,403 191,401 193,400 195,398 197,396 198,395 200,393 202,391 203,390 205,388 207,386 208,384 210,382 212,381 214,379 215,377 217,375 219,373 220,371 222,369 224,367 225,365 227,363 229,362 230,360 232,358 234,356 236,354 237,352 239,350 241,348 242,346 244,344 246,342 247,340 249,338 251,336 252,334 254,332 256,330 258,328 259,326 261,324 263,321 264,319 266,317 268,314 269,312 271,309 273,306 274,304 276,301 278,298 280,296 281,293 283,290 285,287 286,284 288,281 290,278 291,275 293,272 295,270 296,267 298,264 300,261 302,258 303,256 305,253 307,250 308,248 310,246 312,243 313,241 315,239 317,236 318,234 320,232 322,230 324,228 325,226 327,224 329,222 330,220 332,218 334,215 335,213 337,211 339,209 341,207 342,205 344,203 346,200 347,198 349,196 351,194 352,191 354,189 356,187 357,185 359,183 361,181 363,179 364,177 366,174 368,172 369,171 371,169 373,167 374,165 376,163 378,161 379,159 381,157 383,155 385,153 386,152 388,150 390,148 391,146 393,144 395,142 396,140 398,139 400,137 401,135 403,133 405,131 407,130 408,128 410,126 412,125 413,123 415,122 417,120 418,119 420,117 422,116 423,115 425,113 427,112 429,111 430,110 432,109 434,108 435,107 437,106 439,105 440,104 442,103 444,102 445,101 447,101 449,100 451,99 452,99 454,98 456,98 457,97 459,97 461,96 462,96 464,95 466,95 468,95 469,94 471,94 473,94 474,94 476,94 478,94 479,94 481,94 483,94 484,94 486,94 488,94 490,94 491,94 493,95 495,95 496,95 498,96 500,96 501,97 503,97 505,98 506,99 508,99 510,100 512,101 513,102 515,102 517,103 518,104 520,105 522,106 523,106 525,107 527,108 528,109 530,110 532,111 534,112 535,113 537,114 539,115 540,116 542,117 544,118 545,119 547,120 549,121 550,123 552,124 554,125 556,127 557,128 559,129 561,131 562,133 564,134 566,136 567,137 569,139 571,141 573,143 574,144 576,146 578,148 579,150 581,152 583,154 584,156 586,158 588,159 589,161 591,163 593,165 595,167 596,168 598,170 600,172 601,173 603,175 605,176 606,178 608,179 610,181 611,182 613,184 615,185 617,187 618,188 620,190 622,192 623,193 625,195 627,197 628,199 630,200 632,202 633,204 635,207 637,209 639,211 640,213 642,216 644,218 645,221 647,223 649,226 650,229 652,231 654,234 655,237 657,240 659,242 661,245 662,248 664,251 666,253 667,256 669,259 671,261 672,264 674,266 676,269 677,271 679,273 681,276 683,278 684,280 686,283 688,285 689,287 691,289 693,291 694,293 696,295 698,297 700,300 701,302 703,304 705,306 706,308 708,310 710,312 711,314 713,316 715,318 716,320 718,322 720,324 722,326 723,328 725,330 727,332 728,334 730,336 732,338 733,340 735,342 737,344 738,346 740,348 742,350 744,352 745,354 747,356 749,357 750,359 752,361 754,363 755,365 757,367 759,369 760,370 762,372 764,374 766,376 767,378 769,379 771,381 772,383 774,384 776,386 777,387 779,389 781,390 782,392 784,393 786,395 788,396 789,398 791,399 793,400 794,401 796,403 798,404 799,405 801,406 803,408 804,409 806,410 808,411 810,413 811,414 813,415 815,416 816,417 818,419 820,420 821,421 823,422 825,423 827,425 828,426 830,427 832,428 833,429 835,430 837,431 838,432 840,433 842,434 843,435 845,436 847,437 849,438 850,439 852,440 854,440 855,441 857,442 859,443 860,444 862,445 864,445 865,446 867,447 869,448 871,449 872,449 874,450 876,451 877,452 879,453 881,453 882,454 884,455 886,456 887,456 889,457 891,458 893,458 894,459 896,460 898,460 899,461 901,462 903,462 904,463 906,464 908,464 909,465 911,465 913,466 915,467 916,467 918,468 920,469 921,469 923,470 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,425 166,423 168,422 169,421 171,419 173,418 175,416 176,415 178,413 180,412 181,410 183,409 185,407 186,406 188,404 190,403 191,401 193,400 195,398 197,396 198,395 200,393 202,391 203,390 205,388 207,386 208,384 210,382 212,381 214,379 215,377 217,375 219,373 220,371 222,369 224,367 225,365 227,363 229,362 230,360 232,358 234,356 236,354 237,352 239,350 241,348 242,346 244,344 246,342 247,340 249,338 251,336 252,334 254,332 256,330 258,328 259,326 261,324 263,321 264,319 266,317 268,314 269,312 271,309 273,306 274,304 276,301 278,298 280,296 281,293 283,290 285,287 286,284 288,281 290,278 291,275 293,272 295,270 296,267 298,264 300,261 302,258 303,256 305,253 307,250 308,248 310,246 312,243 313,241 315,239 317,236 318,234 320,232 322,230 324,228 325,226 327,224 329,222 330,220 332,218 334,215 335,213 337,211 339,209 341,207 342,205 344,203 346,200 347,198 349,196 351,194 352,191 354,189 356,187 357,185 359,183 361,181 363,179 364,177 366,174 368,172 369,171 371,169 373,167 374,165 376,163 378,161 379,159 381,157 383,155 385,153 386,152 388,150 390,148 391,146 393,144 395,142 396,140 398,139 400,137 401,135 403,133 405,131 407,130 408,128 410,126 412,125 413,123 415,122 417,120 418,119 420,117 422,116 423,115 425,113 427,112 429,111 430,110 432,109 434,108 435,107 437,106 439,105 440,104 442,103 444,102 445,101 447,101 449,100 451,99 452,99 454,98 456,98 457,97 459,97 461,96 462,96 464,95 466,95 468,95 469,94 471,94 473,94 474,94 476,94 478,94 479,94 481,94 483,94 484,94 486,94 488,94 490,94 491,94 493,95 495,95 496,95 498,96 500,96 501,97 503,97 505,98 506,99 508,99 510,100 512,101 513,102 515,102 517,103 518,104 520,105 522,106 523,106 525,107 527,108 528,109 530,110 532,111 534,112 535,113 537,114 539,115 540,116 542,117 544,118 545,119 547,120 549,121 550,123 552,124 554,125 556,127 557,128 559,129 561,131 562,133 564,134 566,136 567,137 569,139 571,141 573,143 574,144 576,146 578,148 579,150 581,152 583,154 584,156 586,158 588,159 589,161 591,163 593,165 595,167 596,168 598,170 600,172 601,173 603,175 605,176 606,178 608,179 610,181 611,182 613,184 615,185 617,187 618,188 620,190 622,192 623,193 625,195 627,197 628,199 630,200 632,202 633,204 635,207 637,209 639,211 640,213 642,216 644,218 645,221 647,223 649,226 650,229 652,231 654,234 655,237 657,240 659,242 661,245 662,248 664,251 666,253 667,256 669,259 671,261 672,264 674,266 676,269 677,271 679,273 681,276 683,278 684,280 686,283 688,285 689,287 691,289 693,291 694,293 696,295 698,297 700,300 701,302 703,304 705,306 706,308 708,310 710,312 711,314 713,316 715,318 716,320 718,322 720,324 722,326 723,328 725,330 727,332 728,334 730,336 732,338 733,340 735,342 737,344 738,346 740,348 742,350 744,352 745,354 747,356 749,357 750,359 752,361 754,363 755,365 757,367 759,369 760,370 762,372 764,374 766,376 767,378 769,379 771,381 772,383 774,384 776,386 777,387 779,389 781,390 782,392 784,393 786,395 788,396 789,398 791,399 793,400 794,401 796,403 798,404 799,405 801,406 803,408 804,409 806,410 808,411 810,413 811,414 813,415 815,416 816,417 818,419 820,420 821,421 823,422 825,423 827,425 828,426 830,427 832,428 833,429 835,430 837,431 838,432 840,433 842,434 843,435 845,436 847,437 849,438 850,439 852,440 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="494,473 494,95 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

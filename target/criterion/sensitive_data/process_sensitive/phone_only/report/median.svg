<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="417" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,417 86,417 "/>
<text x="77" y="359" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,359 86,359 "/>
<text x="77" y="301" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,301 86,301 "/>
<text x="77" y="243" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,243 86,243 "/>
<text x="77" y="185" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,185 86,185 "/>
<text x="77" y="127" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,127 86,127 "/>
<text x="77" y="69" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,69 86,69 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="167" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.375
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="167,473 167,478 "/>
<text x="323" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.38
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="323,473 323,478 "/>
<text x="480" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.385
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="480,473 480,478 "/>
<text x="636" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.39
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="636,473 636,478 "/>
<text x="792" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.395
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="792,473 792,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,471 97,471 98,471 100,470 102,470 103,469 105,468 107,468 109,467 110,466 112,465 114,464 115,463 117,461 119,460 120,458 122,456 124,455 125,453 127,451 129,449 131,447 132,445 134,442 136,440 137,438 139,436 141,434 142,432 144,430 146,428 147,426 149,424 151,423 153,421 154,420 156,418 158,417 159,416 161,415 163,414 164,413 166,411 168,410 169,409 171,408 173,406 175,405 176,403 178,401 180,399 181,397 183,394 185,392 186,389 188,385 190,382 191,378 193,375 195,370 197,366 198,362 200,357 202,352 203,347 205,342 207,336 208,331 210,325 212,320 214,314 215,308 217,302 219,296 220,289 222,283 224,276 225,269 227,262 229,254 230,246 232,238 234,230 236,221 237,213 239,204 241,194 242,185 244,176 246,167 247,158 249,150 251,142 252,134 254,127 256,121 258,116 259,112 261,109 263,107 264,106 266,106 268,107 269,109 271,113 273,117 274,123 276,129 278,136 280,143 281,151 283,159 285,168 286,177 288,185 290,194 291,202 293,210 295,218 296,225 298,232 300,238 302,244 303,249 305,254 307,258 308,262 310,265 312,268 313,271 315,273 317,274 318,275 320,276 322,277 324,278 325,278 327,278 329,279 330,279 332,279 334,279 335,279 337,279 339,279 341,279 342,279 344,279 346,279 347,280 349,280 351,280 352,280 354,279 356,279 357,278 359,277 361,276 363,275 364,273 366,271 368,268 369,265 371,262 373,257 374,253 376,247 378,241 379,234 381,227 383,219 385,210 386,200 388,191 390,180 391,170 393,159 395,149 396,139 398,129 400,120 401,112 403,105 405,99 407,95 408,93 410,92 412,93 413,96 415,101 417,108 418,117 420,128 422,140 423,154 425,169 427,186 429,202 430,220 432,238 434,256 435,274 437,291 439,308 440,325 442,341 444,355 445,369 447,382 449,394 451,405 452,414 454,423 456,431 457,437 459,443 461,448 462,453 464,457 466,460 468,462 469,465 471,466 473,468 474,469 476,470 478,471 479,471 481,471 483,472 484,472 486,472 488,472 490,472 491,472 493,471 495,471 496,471 498,471 500,470 501,470 503,470 505,469 506,469 508,468 510,467 512,467 513,466 515,465 517,464 518,462 520,461 522,459 523,458 525,455 527,453 528,451 530,448 532,445 534,442 535,438 537,434 539,430 540,426 542,422 544,418 545,413 547,409 549,404 550,400 552,396 554,392 556,388 557,385 559,382 561,379 562,377 564,375 566,374 567,373 569,373 571,373 573,374 574,375 576,377 578,378 579,381 581,383 583,386 584,389 586,392 588,395 589,399 591,402 593,405 595,409 596,412 598,415 600,418 601,421 603,424 605,427 606,430 608,432 610,435 611,437 613,439 615,441 617,443 618,445 620,447 622,449 623,450 625,452 627,453 628,455 630,456 632,457 633,458 635,460 637,461 639,462 640,463 642,464 644,465 645,466 647,466 649,467 650,468 652,468 654,469 655,469 657,470 659,470 661,470 662,470 664,470 666,470 667,470 669,469 671,469 672,468 674,467 676,466 677,465 679,464 681,462 683,460 684,459 686,457 688,454 689,452 691,450 693,447 694,444 696,442 698,439 700,436 701,433 703,430 705,428 706,425 708,422 710,420 711,418 713,415 715,413 716,411 718,410 720,408 722,407 723,405 725,404 727,403 728,402 730,401 732,401 733,400 735,400 737,399 738,399 740,398 742,398 744,398 745,397 747,397 749,397 750,397 752,396 754,396 755,396 757,396 759,396 760,396 762,396 764,395 766,395 767,395 769,396 771,396 772,396 774,396 776,396 777,396 779,397 781,397 782,398 784,398 786,398 788,399 789,399 791,400 793,400 794,400 796,400 798,401 799,401 801,401 803,401 804,401 806,401 808,401 810,401 811,401 813,400 815,400 816,400 818,399 820,399 821,398 823,398 825,398 827,398 828,398 830,398 832,398 833,398 835,398 837,399 838,400 840,400 842,401 843,403 845,404 847,405 849,407 850,409 852,410 854,412 855,414 857,416 859,418 860,420 862,422 864,424 865,426 867,428 869,430 871,432 872,433 874,435 876,437 877,438 879,440 881,441 882,442 884,444 886,445 887,446 889,447 891,448 893,449 894,450 896,451 898,451 899,452 901,453 903,454 904,454 906,455 908,456 909,457 911,457 913,458 915,459 916,459 918,460 920,461 921,461 923,462 925,463 926,463 928,464 930,465 932,465 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,413 166,411 168,410 169,409 171,408 173,406 175,405 176,403 178,401 180,399 181,397 183,394 185,392 186,389 188,385 190,382 191,378 193,375 195,370 197,366 198,362 200,357 202,352 203,347 205,342 207,336 208,331 210,325 212,320 214,314 215,308 217,302 219,296 220,289 222,283 224,276 225,269 227,262 229,254 230,246 232,238 234,230 236,221 237,213 239,204 241,194 242,185 244,176 246,167 247,158 249,150 251,142 252,134 254,127 256,121 258,116 259,112 261,109 263,107 264,106 266,106 268,107 269,109 271,113 273,117 274,123 276,129 278,136 280,143 281,151 283,159 285,168 286,177 288,185 290,194 291,202 293,210 295,218 296,225 298,232 300,238 302,244 303,249 305,254 307,258 308,262 310,265 312,268 313,271 315,273 317,274 318,275 320,276 322,277 324,278 325,278 327,278 329,279 330,279 332,279 334,279 335,279 337,279 339,279 341,279 342,279 344,279 346,279 347,280 349,280 351,280 352,280 354,279 356,279 357,278 359,277 361,276 363,275 364,273 366,271 368,268 369,265 371,262 373,257 374,253 376,247 378,241 379,234 381,227 383,219 385,210 386,200 388,191 390,180 391,170 393,159 395,149 396,139 398,129 400,120 401,112 403,105 405,99 407,95 408,93 410,92 412,93 413,96 415,101 417,108 418,117 420,128 422,140 423,154 425,169 427,186 429,202 430,220 432,238 434,256 435,274 437,291 439,308 440,325 442,341 444,355 445,369 447,382 449,394 451,405 452,414 454,423 456,431 457,437 459,443 461,448 462,453 464,457 466,460 468,462 469,465 471,466 473,468 474,469 476,470 478,471 479,471 481,471 483,472 484,472 486,472 488,472 490,472 491,472 493,471 495,471 496,471 498,471 500,470 501,470 503,470 505,469 506,469 508,468 510,467 512,467 513,466 515,465 517,464 518,462 520,461 522,459 523,458 525,455 527,453 528,451 530,448 532,445 534,442 535,438 537,434 539,430 540,426 542,422 544,418 545,413 547,409 549,404 550,400 552,396 554,392 556,388 557,385 559,382 561,379 562,377 564,375 566,374 567,373 569,373 571,373 573,374 574,375 576,377 578,378 579,381 581,383 583,386 584,389 586,392 588,395 589,399 591,402 593,405 595,409 596,412 598,415 600,418 601,421 603,424 605,427 606,430 608,432 610,435 611,437 613,439 615,441 617,443 618,445 620,447 622,449 623,450 625,452 627,453 628,455 630,456 632,457 633,458 635,460 637,461 639,462 640,463 642,464 644,465 645,466 647,466 649,467 650,468 652,468 654,469 655,469 657,470 659,470 661,470 662,470 664,470 666,470 667,470 669,469 671,469 672,468 674,467 676,466 677,465 679,464 681,462 683,460 684,459 686,457 688,454 689,452 691,450 693,447 694,444 696,442 698,439 700,436 701,433 703,430 705,428 706,425 708,422 710,420 711,418 713,415 715,413 716,411 718,410 720,408 722,407 723,405 725,404 727,403 728,402 730,401 732,401 733,400 735,400 737,399 738,399 740,398 742,398 744,398 745,397 747,397 749,397 750,397 752,396 754,396 755,396 757,396 759,396 760,396 762,396 764,395 766,395 767,395 769,396 771,396 772,396 774,396 776,396 777,396 779,397 781,397 782,398 784,398 786,398 788,399 789,399 791,400 793,400 794,400 796,400 798,401 799,401 801,401 803,401 804,401 806,401 808,401 810,401 811,401 813,400 815,400 816,400 818,399 820,399 821,398 823,398 825,398 827,398 828,398 830,398 832,398 833,398 835,398 837,399 838,400 840,400 842,401 843,403 845,404 847,405 849,407 850,409 852,410 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="372,473 372,259 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

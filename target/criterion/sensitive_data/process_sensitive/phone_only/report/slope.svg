<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:slope
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="425" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,425 86,425 "/>
<text x="77" y="362" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,362 86,362 "/>
<text x="77" y="299" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,299 86,299 "/>
<text x="77" y="236" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,236 86,236 "/>
<text x="77" y="172" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,172 86,172 "/>
<text x="77" y="109" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,109 86,109 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="212" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.38
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="212,473 212,478 "/>
<text x="357" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.385
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="357,473 357,478 "/>
<text x="502" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.39
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="502,473 502,478 "/>
<text x="647" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.395
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="647,473 647,478 "/>
<text x="791" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="791,473 791,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,469 93,468 95,467 97,465 98,464 100,462 102,461 103,459 105,458 107,456 109,454 110,453 112,451 114,449 115,447 117,445 119,443 120,441 122,438 124,436 125,434 127,431 129,429 131,426 132,423 134,420 136,418 137,415 139,411 141,408 142,405 144,402 146,398 147,395 149,391 151,387 153,383 154,379 156,375 158,371 159,367 161,363 163,359 164,354 166,350 168,345 169,341 171,336 173,332 175,327 176,322 178,317 180,312 181,308 183,303 185,298 186,293 188,288 190,283 191,278 193,273 195,268 197,263 198,258 200,253 202,248 203,244 205,239 207,234 208,229 210,225 212,220 214,216 215,211 217,207 219,203 220,198 222,194 224,190 225,186 227,182 229,178 230,174 232,171 234,167 236,164 237,160 239,157 241,154 242,151 244,148 246,145 247,142 249,140 251,137 252,135 254,133 256,130 258,128 259,126 261,125 263,123 264,121 266,119 268,118 269,116 271,115 273,114 274,112 276,111 278,110 280,109 281,108 283,107 285,106 286,106 288,105 290,105 291,104 293,104 295,103 296,103 298,103 300,103 302,103 303,103 305,103 307,104 308,104 310,104 312,105 313,105 315,106 317,106 318,107 320,107 322,108 324,108 325,109 327,110 329,110 330,111 332,111 334,112 335,112 337,113 339,113 341,113 342,113 344,113 346,113 347,113 349,113 351,113 352,113 354,112 356,112 357,111 359,111 361,110 363,109 364,108 366,108 368,107 369,106 371,105 373,104 374,103 376,103 378,102 379,101 381,100 383,99 385,99 386,98 388,98 390,97 391,96 393,96 395,96 396,95 398,95 400,95 401,94 403,94 405,94 407,94 408,93 410,93 412,93 413,93 415,93 417,93 418,93 420,93 422,93 423,93 425,93 427,94 429,94 430,94 432,94 434,95 435,95 437,96 439,96 440,97 442,97 444,98 445,99 447,100 449,101 451,102 452,103 454,104 456,105 457,106 459,108 461,109 462,111 464,112 466,114 468,115 469,117 471,118 473,120 474,122 476,123 478,125 479,127 481,129 483,130 484,132 486,134 488,136 490,138 491,140 493,142 495,144 496,146 498,148 500,150 501,152 503,155 505,157 506,159 508,162 510,164 512,167 513,169 515,172 517,174 518,177 520,179 522,182 523,184 525,187 527,189 528,192 530,194 532,196 534,199 535,201 537,203 539,205 540,208 542,210 544,212 545,214 547,216 549,218 550,220 552,222 554,224 556,226 557,228 559,230 561,232 562,234 564,235 566,237 567,239 569,240 571,242 573,244 574,245 576,247 578,248 579,250 581,251 583,252 584,254 586,255 588,257 589,258 591,260 593,261 595,263 596,264 598,266 600,267 601,269 603,270 605,272 606,274 608,275 610,277 611,279 613,280 615,282 617,284 618,285 620,287 622,289 623,291 625,292 627,294 628,296 630,298 632,299 633,301 635,303 637,304 639,306 640,308 642,310 644,311 645,313 647,315 649,316 650,318 652,319 654,321 655,323 657,324 659,326 661,327 662,329 664,330 666,332 667,333 669,335 671,336 672,337 674,339 676,340 677,342 679,343 681,344 683,346 684,347 686,349 688,350 689,351 691,353 693,354 694,355 696,357 698,358 700,359 701,361 703,362 705,363 706,364 708,366 710,367 711,368 713,369 715,371 716,372 718,373 720,374 722,376 723,377 725,378 727,379 728,381 730,382 732,383 733,385 735,386 737,387 738,388 740,390 742,391 744,392 745,393 747,394 749,396 750,397 752,398 754,399 755,400 757,401 759,402 760,403 762,404 764,405 766,406 767,407 769,408 771,409 772,410 774,411 776,412 777,412 779,413 781,414 782,415 784,416 786,417 788,418 789,418 791,419 793,420 794,421 796,421 798,422 799,423 801,424 803,424 804,425 806,426 808,427 810,427 811,428 813,429 815,429 816,430 818,431 820,431 821,432 823,433 825,433 827,434 828,435 830,435 832,436 833,437 835,437 837,438 838,439 840,440 842,440 843,441 845,442 847,442 849,443 850,444 852,444 854,445 855,446 857,447 859,447 860,448 862,448 864,449 865,450 867,450 869,451 871,451 872,452 874,452 876,453 877,453 879,454 881,454 882,455 884,455 886,455 887,456 889,456 891,457 893,457 894,457 896,458 898,458 899,459 901,459 903,460 904,460 906,460 908,461 909,461 911,462 913,462 915,463 916,463 918,464 920,464 921,464 923,465 925,465 926,466 928,466 930,466 932,467 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,354 166,350 168,345 169,341 171,336 173,332 175,327 176,322 178,317 180,312 181,308 183,303 185,298 186,293 188,288 190,283 191,278 193,273 195,268 197,263 198,258 200,253 202,248 203,244 205,239 207,234 208,229 210,225 212,220 214,216 215,211 217,207 219,203 220,198 222,194 224,190 225,186 227,182 229,178 230,174 232,171 234,167 236,164 237,160 239,157 241,154 242,151 244,148 246,145 247,142 249,140 251,137 252,135 254,133 256,130 258,128 259,126 261,125 263,123 264,121 266,119 268,118 269,116 271,115 273,114 274,112 276,111 278,110 280,109 281,108 283,107 285,106 286,106 288,105 290,105 291,104 293,104 295,103 296,103 298,103 300,103 302,103 303,103 305,103 307,104 308,104 310,104 312,105 313,105 315,106 317,106 318,107 320,107 322,108 324,108 325,109 327,110 329,110 330,111 332,111 334,112 335,112 337,113 339,113 341,113 342,113 344,113 346,113 347,113 349,113 351,113 352,113 354,112 356,112 357,111 359,111 361,110 363,109 364,108 366,108 368,107 369,106 371,105 373,104 374,103 376,103 378,102 379,101 381,100 383,99 385,99 386,98 388,98 390,97 391,96 393,96 395,96 396,95 398,95 400,95 401,94 403,94 405,94 407,94 408,93 410,93 412,93 413,93 415,93 417,93 418,93 420,93 422,93 423,93 425,93 427,94 429,94 430,94 432,94 434,95 435,95 437,96 439,96 440,97 442,97 444,98 445,99 447,100 449,101 451,102 452,103 454,104 456,105 457,106 459,108 461,109 462,111 464,112 466,114 468,115 469,117 471,118 473,120 474,122 476,123 478,125 479,127 481,129 483,130 484,132 486,134 488,136 490,138 491,140 493,142 495,144 496,146 498,148 500,150 501,152 503,155 505,157 506,159 508,162 510,164 512,167 513,169 515,172 517,174 518,177 520,179 522,182 523,184 525,187 527,189 528,192 530,194 532,196 534,199 535,201 537,203 539,205 540,208 542,210 544,212 545,214 547,216 549,218 550,220 552,222 554,224 556,226 557,228 559,230 561,232 562,234 564,235 566,237 567,239 569,240 571,242 573,244 574,245 576,247 578,248 579,250 581,251 583,252 584,254 586,255 588,257 589,258 591,260 593,261 595,263 596,264 598,266 600,267 601,269 603,270 605,272 606,274 608,275 610,277 611,279 613,280 615,282 617,284 618,285 620,287 622,289 623,291 625,292 627,294 628,296 630,298 632,299 633,301 635,303 637,304 639,306 640,308 642,310 644,311 645,313 647,315 649,316 650,318 652,319 654,321 655,323 657,324 659,326 661,327 662,329 664,330 666,332 667,333 669,335 671,336 672,337 674,339 676,340 677,342 679,343 681,344 683,346 684,347 686,349 688,350 689,351 691,353 693,354 694,355 696,357 698,358 700,359 701,361 703,362 705,363 706,364 708,366 710,367 711,368 713,369 715,371 716,372 718,373 720,374 722,376 723,377 725,378 727,379 728,381 730,382 732,383 733,385 735,386 737,387 738,388 740,390 742,391 744,392 745,393 747,394 749,396 750,397 752,398 754,399 755,400 757,401 759,402 760,403 762,404 764,405 766,406 767,407 769,408 771,409 772,410 774,411 776,412 777,412 779,413 781,414 782,415 784,416 786,417 788,418 789,418 791,419 793,420 794,421 796,421 798,422 799,423 801,424 803,424 804,425 806,426 808,427 810,427 811,428 813,429 815,429 816,430 818,431 820,431 821,432 823,433 825,433 827,434 828,435 830,435 832,436 833,437 835,437 837,438 838,439 840,440 842,440 843,441 845,442 847,442 849,443 850,444 852,444 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="441,473 441,97 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

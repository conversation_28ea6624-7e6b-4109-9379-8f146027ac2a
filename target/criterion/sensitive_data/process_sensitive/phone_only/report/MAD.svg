<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="446" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,446 86,446 "/>
<text x="77" y="406" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,406 86,406 "/>
<text x="77" y="365" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,365 86,365 "/>
<text x="77" y="325" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,325 86,325 "/>
<text x="77" y="284" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,284 86,284 "/>
<text x="77" y="243" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,243 86,243 "/>
<text x="77" y="203" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.07
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,203 86,203 "/>
<text x="77" y="162" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,162 86,162 "/>
<text x="77" y="122" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.09
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,122 86,122 "/>
<text x="77" y="81" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,81 86,81 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="203" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="203,473 203,478 "/>
<text x="341" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="341,473 341,478 "/>
<text x="479" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="479,473 479,478 "/>
<text x="618" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="618,473 618,478 "/>
<text x="756" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="756,473 756,478 "/>
<text x="894" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="894,473 894,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,469 97,469 98,468 100,467 102,466 103,465 105,464 107,463 109,462 110,461 112,460 114,459 115,458 117,457 119,457 120,456 122,455 124,454 125,453 127,453 129,452 131,451 132,451 134,450 136,450 137,450 139,449 141,449 142,449 144,449 146,449 147,449 149,448 151,448 153,448 154,448 156,448 158,447 159,447 161,447 163,446 164,446 166,445 168,445 169,444 171,443 173,442 175,442 176,441 178,440 180,439 181,438 183,437 185,436 186,435 188,434 190,433 191,431 193,430 195,429 197,428 198,427 200,426 202,425 203,424 205,423 207,422 208,421 210,421 212,420 214,419 215,419 217,418 219,418 220,417 222,416 224,416 225,415 227,414 229,414 230,413 232,412 234,411 236,410 237,408 239,407 241,406 242,404 244,403 246,401 247,400 249,398 251,396 252,394 254,392 256,390 258,389 259,387 261,385 263,383 264,381 266,379 268,377 269,375 271,374 273,372 274,371 276,369 278,368 280,366 281,365 283,364 285,363 286,361 288,360 290,359 291,357 293,356 295,355 296,353 298,351 300,350 302,348 303,346 305,344 307,342 308,339 310,337 312,334 313,331 315,328 317,325 318,322 320,318 322,314 324,310 325,306 327,302 329,297 330,292 332,288 334,282 335,277 337,272 339,267 341,261 342,256 344,250 346,245 347,240 349,235 351,230 352,226 354,222 356,219 357,215 359,213 361,210 363,209 364,207 366,207 368,206 369,206 371,206 373,207 374,207 376,208 378,209 379,209 381,210 383,210 385,210 386,210 388,210 390,209 391,208 393,207 395,206 396,204 398,202 400,200 401,198 403,195 405,193 407,190 408,188 410,185 412,182 413,180 415,177 417,173 418,170 420,167 422,163 423,159 425,155 427,150 429,146 430,141 432,136 434,130 435,125 437,120 439,115 440,110 442,106 444,102 445,98 447,96 449,94 451,93 452,93 454,94 456,96 457,98 459,102 461,107 462,112 464,118 466,125 468,132 469,139 471,147 473,155 474,163 476,171 478,179 479,187 481,195 483,202 484,208 486,215 488,221 490,227 491,232 493,237 495,242 496,246 498,250 500,254 501,258 503,261 505,265 506,268 508,271 510,274 512,277 513,279 515,282 517,284 518,286 520,287 522,289 523,290 525,292 527,293 528,293 530,294 532,295 534,295 535,296 537,296 539,297 540,298 542,298 544,299 545,300 547,302 549,303 550,305 552,306 554,308 556,310 557,312 559,315 561,317 562,320 564,323 566,325 567,328 569,331 571,333 573,336 574,339 576,341 578,344 579,346 581,348 583,350 584,353 586,354 588,356 589,358 591,360 593,361 595,363 596,364 598,365 600,367 601,368 603,369 605,370 606,371 608,372 610,374 611,375 613,376 615,377 617,378 618,378 620,379 622,380 623,381 625,382 627,382 628,383 630,383 632,384 633,384 635,385 637,385 639,385 640,385 642,385 644,384 645,384 647,384 649,383 650,383 652,382 654,382 655,381 657,381 659,381 661,380 662,380 664,380 666,380 667,380 669,380 671,380 672,380 674,380 676,381 677,381 679,382 681,382 683,383 684,383 686,384 688,384 689,385 691,385 693,386 694,386 696,387 698,387 700,387 701,388 703,388 705,389 706,389 708,390 710,390 711,391 713,391 715,392 716,392 718,393 720,394 722,394 723,395 725,395 727,396 728,396 730,397 732,398 733,398 735,398 737,399 738,399 740,400 742,400 744,401 745,401 747,402 749,402 750,402 752,403 754,403 755,404 757,405 759,405 760,406 762,407 764,407 766,408 767,409 769,410 771,411 772,411 774,412 776,413 777,414 779,414 781,415 782,416 784,416 786,417 788,418 789,418 791,419 793,419 794,420 796,420 798,421 799,421 801,422 803,422 804,423 806,424 808,425 810,425 811,426 813,427 815,428 816,429 818,430 820,432 821,433 823,434 825,435 827,436 828,438 830,439 832,440 833,441 835,442 837,443 838,444 840,445 842,446 843,447 845,448 847,448 849,449 850,450 852,451 854,451 855,452 857,453 859,453 860,454 862,455 864,455 865,456 867,457 869,457 871,458 872,459 874,459 876,460 877,461 879,461 881,462 882,462 884,463 886,464 887,464 889,465 891,465 893,466 894,466 896,466 898,467 899,467 901,467 903,467 904,468 906,468 908,468 909,468 911,469 913,469 915,469 916,469 918,470 920,470 921,470 923,470 925,471 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,446 166,445 168,445 169,444 171,443 173,442 175,442 176,441 178,440 180,439 181,438 183,437 185,436 186,435 188,434 190,433 191,431 193,430 195,429 197,428 198,427 200,426 202,425 203,424 205,423 207,422 208,421 210,421 212,420 214,419 215,419 217,418 219,418 220,417 222,416 224,416 225,415 227,414 229,414 230,413 232,412 234,411 236,410 237,408 239,407 241,406 242,404 244,403 246,401 247,400 249,398 251,396 252,394 254,392 256,390 258,389 259,387 261,385 263,383 264,381 266,379 268,377 269,375 271,374 273,372 274,371 276,369 278,368 280,366 281,365 283,364 285,363 286,361 288,360 290,359 291,357 293,356 295,355 296,353 298,351 300,350 302,348 303,346 305,344 307,342 308,339 310,337 312,334 313,331 315,328 317,325 318,322 320,318 322,314 324,310 325,306 327,302 329,297 330,292 332,288 334,282 335,277 337,272 339,267 341,261 342,256 344,250 346,245 347,240 349,235 351,230 352,226 354,222 356,219 357,215 359,213 361,210 363,209 364,207 366,207 368,206 369,206 371,206 373,207 374,207 376,208 378,209 379,209 381,210 383,210 385,210 386,210 388,210 390,209 391,208 393,207 395,206 396,204 398,202 400,200 401,198 403,195 405,193 407,190 408,188 410,185 412,182 413,180 415,177 417,173 418,170 420,167 422,163 423,159 425,155 427,150 429,146 430,141 432,136 434,130 435,125 437,120 439,115 440,110 442,106 444,102 445,98 447,96 449,94 451,93 452,93 454,94 456,96 457,98 459,102 461,107 462,112 464,118 466,125 468,132 469,139 471,147 473,155 474,163 476,171 478,179 479,187 481,195 483,202 484,208 486,215 488,221 490,227 491,232 493,237 495,242 496,246 498,250 500,254 501,258 503,261 505,265 506,268 508,271 510,274 512,277 513,279 515,282 517,284 518,286 520,287 522,289 523,290 525,292 527,293 528,293 530,294 532,295 534,295 535,296 537,296 539,297 540,298 542,298 544,299 545,300 547,302 549,303 550,305 552,306 554,308 556,310 557,312 559,315 561,317 562,320 564,323 566,325 567,328 569,331 571,333 573,336 574,339 576,341 578,344 579,346 581,348 583,350 584,353 586,354 588,356 589,358 591,360 593,361 595,363 596,364 598,365 600,367 601,368 603,369 605,370 606,371 608,372 610,374 611,375 613,376 615,377 617,378 618,378 620,379 622,380 623,381 625,382 627,382 628,383 630,383 632,384 633,384 635,385 637,385 639,385 640,385 642,385 644,384 645,384 647,384 649,383 650,383 652,382 654,382 655,381 657,381 659,381 661,380 662,380 664,380 666,380 667,380 669,380 671,380 672,380 674,380 676,381 677,381 679,382 681,382 683,383 684,383 686,384 688,384 689,385 691,385 693,386 694,386 696,387 698,387 700,387 701,388 703,388 705,389 706,389 708,390 710,390 711,391 713,391 715,392 716,392 718,393 720,394 722,394 723,395 725,395 727,396 728,396 730,397 732,398 733,398 735,398 737,399 738,399 740,400 742,400 744,401 745,401 747,402 749,402 750,402 752,403 754,403 755,404 757,405 759,405 760,406 762,407 764,407 766,408 767,409 769,410 771,411 772,411 774,412 776,413 777,414 779,414 781,415 782,416 784,416 786,417 788,418 789,418 791,419 793,419 794,420 796,420 798,421 799,421 801,422 803,422 804,423 806,424 808,425 810,425 811,426 813,427 815,428 816,429 818,430 820,432 821,433 823,434 825,435 827,436 828,438 830,439 832,440 833,441 835,442 837,443 838,444 840,445 842,446 843,447 845,448 847,448 849,449 850,450 852,451 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="435,473 435,127 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

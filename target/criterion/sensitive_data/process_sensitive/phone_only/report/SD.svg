<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
sensitive_data/process_sensitive/phone_only:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="446" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,446 86,446 "/>
<text x="77" y="397" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,397 86,397 "/>
<text x="77" y="347" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,347 86,347 "/>
<text x="77" y="298" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,298 86,298 "/>
<text x="77" y="248" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,248 86,248 "/>
<text x="77" y="199" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,199 86,199 "/>
<text x="77" y="149" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.07
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,149 86,149 "/>
<text x="77" y="100" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,100 86,100 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="169" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="169,473 169,478 "/>
<text x="347" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="347,473 347,478 "/>
<text x="526" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="526,473 526,478 "/>
<text x="704" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="704,473 704,478 "/>
<text x="883" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="883,473 883,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,471 92,470 93,470 95,469 97,469 98,468 100,468 102,467 103,467 105,466 107,466 109,465 110,465 112,464 114,464 115,463 117,463 119,462 120,462 122,461 124,461 125,460 127,459 129,459 131,458 132,458 134,457 136,456 137,456 139,455 141,454 142,454 144,453 146,452 147,451 149,451 151,450 153,449 154,448 156,447 158,446 159,446 161,445 163,444 164,443 166,442 168,441 169,440 171,439 173,438 175,437 176,436 178,435 180,434 181,433 183,432 185,431 186,430 188,429 190,428 191,426 193,425 195,424 197,423 198,422 200,421 202,420 203,419 205,418 207,417 208,416 210,415 212,414 214,413 215,411 217,410 219,409 220,408 222,407 224,405 225,404 227,403 229,401 230,400 232,399 234,397 236,396 237,394 239,393 241,391 242,389 244,388 246,386 247,384 249,383 251,381 252,379 254,377 256,376 258,374 259,372 261,370 263,368 264,366 266,365 268,363 269,361 271,359 273,357 274,356 276,354 278,352 280,350 281,348 283,347 285,345 286,343 288,341 290,339 291,338 293,336 295,334 296,332 298,330 300,328 302,327 303,325 305,323 307,321 308,319 310,317 312,315 313,314 315,312 317,310 318,308 320,306 322,304 324,302 325,301 327,299 329,297 330,295 332,293 334,291 335,289 337,288 339,286 341,284 342,282 344,280 346,278 347,276 349,274 351,272 352,270 354,267 356,265 357,263 359,261 361,258 363,256 364,254 366,252 368,249 369,247 371,245 373,243 374,240 376,238 378,236 379,234 381,232 383,230 385,228 386,226 388,223 390,221 391,219 393,217 395,215 396,213 398,212 400,210 401,208 403,206 405,204 407,202 408,200 410,198 412,197 413,195 415,193 417,191 418,189 420,188 422,186 423,184 425,182 427,180 429,178 430,177 432,175 434,173 435,171 437,169 439,167 440,165 442,163 444,161 445,159 447,157 449,155 451,153 452,151 454,149 456,147 457,145 459,144 461,142 462,140 464,138 466,136 468,135 469,133 471,131 473,130 474,128 476,127 478,125 479,124 481,122 483,121 484,120 486,118 488,117 490,116 491,114 493,113 495,112 496,111 498,110 500,108 501,107 503,106 505,105 506,104 508,103 510,102 512,101 513,100 515,99 517,98 518,98 520,97 522,96 523,96 525,95 527,95 528,94 530,94 532,94 534,94 535,94 537,94 539,94 540,94 542,94 544,95 545,95 547,95 549,96 550,96 552,97 554,97 556,98 557,98 559,99 561,99 562,100 564,100 566,100 567,101 569,101 571,102 573,102 574,102 576,103 578,103 579,103 581,104 583,104 584,105 586,105 588,106 589,106 591,107 593,108 595,109 596,110 598,111 600,112 601,113 603,114 605,116 606,117 608,118 610,120 611,121 613,123 615,124 617,126 618,128 620,129 622,131 623,132 625,134 627,136 628,137 630,139 632,141 633,142 635,144 637,146 639,147 640,149 642,151 644,153 645,155 647,157 649,159 650,161 652,163 654,165 655,167 657,169 659,171 661,173 662,176 664,178 666,180 667,183 669,185 671,187 672,190 674,192 676,194 677,197 679,199 681,202 683,204 684,206 686,209 688,211 689,213 691,216 693,218 694,221 696,223 698,225 700,228 701,230 703,233 705,235 706,238 708,240 710,243 711,245 713,248 715,250 716,253 718,256 720,258 722,261 723,263 725,266 727,269 728,271 730,274 732,277 733,279 735,282 737,285 738,287 740,290 742,292 744,295 745,298 747,300 749,303 750,305 752,308 754,310 755,313 757,315 759,318 760,320 762,323 764,325 766,328 767,330 769,332 771,335 772,337 774,339 776,341 777,344 779,346 781,348 782,350 784,353 786,355 788,357 789,359 791,361 793,363 794,365 796,368 798,370 799,372 801,374 803,376 804,378 806,380 808,382 810,383 811,385 813,387 815,389 816,391 818,393 820,394 821,396 823,398 825,399 827,401 828,403 830,404 832,406 833,407 835,409 837,410 838,412 840,413 842,415 843,416 845,418 847,419 849,421 850,422 852,423 854,425 855,426 857,428 859,429 860,431 862,432 864,434 865,435 867,436 869,438 871,439 872,441 874,442 876,443 877,445 879,446 881,447 882,448 884,449 886,450 887,452 889,453 891,454 893,455 894,456 896,457 898,458 899,458 901,459 903,460 904,461 906,462 908,463 909,463 911,464 913,465 915,466 916,467 918,467 920,468 921,469 923,469 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,443 166,442 168,441 169,440 171,439 173,438 175,437 176,436 178,435 180,434 181,433 183,432 185,431 186,430 188,429 190,428 191,426 193,425 195,424 197,423 198,422 200,421 202,420 203,419 205,418 207,417 208,416 210,415 212,414 214,413 215,411 217,410 219,409 220,408 222,407 224,405 225,404 227,403 229,401 230,400 232,399 234,397 236,396 237,394 239,393 241,391 242,389 244,388 246,386 247,384 249,383 251,381 252,379 254,377 256,376 258,374 259,372 261,370 263,368 264,366 266,365 268,363 269,361 271,359 273,357 274,356 276,354 278,352 280,350 281,348 283,347 285,345 286,343 288,341 290,339 291,338 293,336 295,334 296,332 298,330 300,328 302,327 303,325 305,323 307,321 308,319 310,317 312,315 313,314 315,312 317,310 318,308 320,306 322,304 324,302 325,301 327,299 329,297 330,295 332,293 334,291 335,289 337,288 339,286 341,284 342,282 344,280 346,278 347,276 349,274 351,272 352,270 354,267 356,265 357,263 359,261 361,258 363,256 364,254 366,252 368,249 369,247 371,245 373,243 374,240 376,238 378,236 379,234 381,232 383,230 385,228 386,226 388,223 390,221 391,219 393,217 395,215 396,213 398,212 400,210 401,208 403,206 405,204 407,202 408,200 410,198 412,197 413,195 415,193 417,191 418,189 420,188 422,186 423,184 425,182 427,180 429,178 430,177 432,175 434,173 435,171 437,169 439,167 440,165 442,163 444,161 445,159 447,157 449,155 451,153 452,151 454,149 456,147 457,145 459,144 461,142 462,140 464,138 466,136 468,135 469,133 471,131 473,130 474,128 476,127 478,125 479,124 481,122 483,121 484,120 486,118 488,117 490,116 491,114 493,113 495,112 496,111 498,110 500,108 501,107 503,106 505,105 506,104 508,103 510,102 512,101 513,100 515,99 517,98 518,98 520,97 522,96 523,96 525,95 527,95 528,94 530,94 532,94 534,94 535,94 537,94 539,94 540,94 542,94 544,95 545,95 547,95 549,96 550,96 552,97 554,97 556,98 557,98 559,99 561,99 562,100 564,100 566,100 567,101 569,101 571,102 573,102 574,102 576,103 578,103 579,103 581,104 583,104 584,105 586,105 588,106 589,106 591,107 593,108 595,109 596,110 598,111 600,112 601,113 603,114 605,116 606,117 608,118 610,120 611,121 613,123 615,124 617,126 618,128 620,129 622,131 623,132 625,134 627,136 628,137 630,139 632,141 633,142 635,144 637,146 639,147 640,149 642,151 644,153 645,155 647,157 649,159 650,161 652,163 654,165 655,167 657,169 659,171 661,173 662,176 664,178 666,180 667,183 669,185 671,187 672,190 674,192 676,194 677,197 679,199 681,202 683,204 684,206 686,209 688,211 689,213 691,216 693,218 694,221 696,223 698,225 700,228 701,230 703,233 705,235 706,238 708,240 710,243 711,245 713,248 715,250 716,253 718,256 720,258 722,261 723,263 725,266 727,269 728,271 730,274 732,277 733,279 735,282 737,285 738,287 740,290 742,292 744,295 745,298 747,300 749,303 750,305 752,308 754,310 755,313 757,315 759,318 760,320 762,323 764,325 766,328 767,330 769,332 771,335 772,337 774,339 776,341 777,344 779,346 781,348 782,350 784,353 786,355 788,357 789,359 791,361 793,363 794,365 796,368 798,370 799,372 801,374 803,376 804,378 806,380 808,382 810,383 811,385 813,387 815,389 816,391 818,393 820,394 821,396 823,398 825,399 827,401 828,403 830,404 832,406 833,407 835,409 837,410 838,412 840,413 842,415 843,416 845,418 847,419 849,421 850,422 852,423 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="552,473 552,97 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

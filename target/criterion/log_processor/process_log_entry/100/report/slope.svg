<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/100:slope
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="436" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,436 86,436 "/>
<text x="77" y="381" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,381 86,381 "/>
<text x="77" y="326" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,326 86,326 "/>
<text x="77" y="271" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,271 86,271 "/>
<text x="77" y="216" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,216 86,216 "/>
<text x="77" y="160" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,160 86,160 "/>
<text x="77" y="105" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,105 86,105 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="142" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.085
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="142,473 142,478 "/>
<text x="218" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.09
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="218,473 218,478 "/>
<text x="294" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.095
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="294,473 294,478 "/>
<text x="371" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="371,473 371,478 "/>
<text x="447" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.105
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="447,473 447,478 "/>
<text x="524" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.11
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="524,473 524,478 "/>
<text x="600" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.115
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="600,473 600,478 "/>
<text x="677" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="677,473 677,478 "/>
<text x="753" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.125
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="753,473 753,478 "/>
<text x="830" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.13
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="830,473 830,478 "/>
<text x="906" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.135
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="906,473 906,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,471 88,470 90,469 92,469 93,468 95,467 97,466 98,465 100,464 102,463 103,462 105,461 107,460 109,459 110,458 112,457 114,456 115,454 117,453 119,452 120,451 122,449 124,448 125,446 127,445 129,443 131,442 132,440 134,439 136,437 137,435 139,433 141,431 142,430 144,428 146,426 147,424 149,422 151,419 153,417 154,415 156,413 158,411 159,409 161,406 163,404 164,402 166,400 168,397 169,395 171,393 173,391 175,389 176,386 178,384 180,382 181,380 183,378 185,376 186,373 188,371 190,369 191,367 193,365 195,362 197,360 198,358 200,356 202,353 203,351 205,349 207,346 208,344 210,341 212,338 214,336 215,333 217,330 219,328 220,325 222,322 224,319 225,316 227,313 229,310 230,307 232,303 234,300 236,297 237,294 239,290 241,287 242,284 244,280 246,277 247,274 249,270 251,267 252,264 254,260 256,257 258,254 259,251 261,247 263,244 264,241 266,238 268,235 269,232 271,229 273,226 274,223 276,220 278,217 280,215 281,212 283,209 285,206 286,204 288,201 290,199 291,196 293,193 295,191 296,188 298,186 300,184 302,181 303,179 305,176 307,174 308,171 310,169 312,167 313,164 315,162 317,160 318,157 320,155 322,153 324,150 325,148 327,146 329,144 330,141 332,139 334,137 335,135 337,133 339,131 341,129 342,127 344,126 346,124 347,122 349,121 351,119 352,118 354,116 356,115 357,113 359,112 361,111 363,109 364,108 366,107 368,106 369,105 371,104 373,103 374,102 376,101 378,101 379,100 381,99 383,98 385,98 386,97 388,97 390,96 391,96 393,95 395,95 396,94 398,94 400,94 401,94 403,94 405,93 407,93 408,93 410,93 412,93 413,94 415,94 417,94 418,94 420,95 422,95 423,95 425,96 427,96 429,97 430,97 432,98 434,99 435,99 437,100 439,101 440,101 442,102 444,102 445,103 447,104 449,104 451,105 452,106 454,107 456,107 457,108 459,109 461,110 462,110 464,111 466,112 468,113 469,114 471,115 473,116 474,118 476,119 478,120 479,121 481,123 483,124 484,125 486,127 488,128 490,129 491,131 493,132 495,134 496,135 498,137 500,138 501,140 503,141 505,143 506,144 508,146 510,147 512,149 513,150 515,152 517,153 518,155 520,157 522,158 523,160 525,162 527,164 528,166 530,167 532,169 534,171 535,173 537,176 539,178 540,180 542,182 544,184 545,187 547,189 549,191 550,194 552,196 554,199 556,201 557,204 559,206 561,209 562,211 564,214 566,216 567,219 569,221 571,224 573,226 574,229 576,232 578,234 579,236 581,239 583,241 584,244 586,246 588,248 589,251 591,253 593,255 595,257 596,259 598,261 600,263 601,265 603,267 605,269 606,271 608,273 610,274 611,276 613,278 615,280 617,281 618,283 620,285 622,286 623,288 625,290 627,292 628,293 630,295 632,297 633,298 635,300 637,302 639,304 640,305 642,307 644,309 645,311 647,312 649,314 650,316 652,318 654,319 655,321 657,323 659,325 661,327 662,329 664,330 666,332 667,334 669,336 671,338 672,339 674,341 676,343 677,345 679,346 681,348 683,350 684,352 686,353 688,355 689,357 691,358 693,360 694,362 696,363 698,365 700,366 701,368 703,369 705,371 706,372 708,374 710,375 711,377 713,378 715,380 716,381 718,382 720,383 722,385 723,386 725,387 727,388 728,390 730,391 732,392 733,393 735,394 737,395 738,396 740,397 742,398 744,399 745,400 747,400 749,401 750,402 752,403 754,404 755,405 757,406 759,407 760,407 762,408 764,409 766,410 767,411 769,412 771,413 772,414 774,415 776,416 777,417 779,418 781,419 782,420 784,421 786,422 788,423 789,424 791,425 793,426 794,427 796,428 798,429 799,429 801,430 803,431 804,432 806,433 808,434 810,435 811,435 813,436 815,437 816,438 818,439 820,439 821,440 823,441 825,442 827,442 828,443 830,444 832,445 833,445 835,446 837,447 838,447 840,448 842,449 843,449 845,450 847,450 849,451 850,452 852,452 854,453 855,453 857,454 859,454 860,455 862,455 864,456 865,457 867,457 869,458 871,458 872,459 874,459 876,460 877,460 879,461 881,461 882,462 884,462 886,463 887,463 889,464 891,464 893,465 894,465 896,466 898,466 899,466 901,467 903,467 904,468 906,468 908,468 909,469 911,469 913,469 915,470 916,470 918,470 920,470 921,471 923,471 925,471 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,402 166,400 168,397 169,395 171,393 173,391 175,389 176,386 178,384 180,382 181,380 183,378 185,376 186,373 188,371 190,369 191,367 193,365 195,362 197,360 198,358 200,356 202,353 203,351 205,349 207,346 208,344 210,341 212,338 214,336 215,333 217,330 219,328 220,325 222,322 224,319 225,316 227,313 229,310 230,307 232,303 234,300 236,297 237,294 239,290 241,287 242,284 244,280 246,277 247,274 249,270 251,267 252,264 254,260 256,257 258,254 259,251 261,247 263,244 264,241 266,238 268,235 269,232 271,229 273,226 274,223 276,220 278,217 280,215 281,212 283,209 285,206 286,204 288,201 290,199 291,196 293,193 295,191 296,188 298,186 300,184 302,181 303,179 305,176 307,174 308,171 310,169 312,167 313,164 315,162 317,160 318,157 320,155 322,153 324,150 325,148 327,146 329,144 330,141 332,139 334,137 335,135 337,133 339,131 341,129 342,127 344,126 346,124 347,122 349,121 351,119 352,118 354,116 356,115 357,113 359,112 361,111 363,109 364,108 366,107 368,106 369,105 371,104 373,103 374,102 376,101 378,101 379,100 381,99 383,98 385,98 386,97 388,97 390,96 391,96 393,95 395,95 396,94 398,94 400,94 401,94 403,94 405,93 407,93 408,93 410,93 412,93 413,94 415,94 417,94 418,94 420,95 422,95 423,95 425,96 427,96 429,97 430,97 432,98 434,99 435,99 437,100 439,101 440,101 442,102 444,102 445,103 447,104 449,104 451,105 452,106 454,107 456,107 457,108 459,109 461,110 462,110 464,111 466,112 468,113 469,114 471,115 473,116 474,118 476,119 478,120 479,121 481,123 483,124 484,125 486,127 488,128 490,129 491,131 493,132 495,134 496,135 498,137 500,138 501,140 503,141 505,143 506,144 508,146 510,147 512,149 513,150 515,152 517,153 518,155 520,157 522,158 523,160 525,162 527,164 528,166 530,167 532,169 534,171 535,173 537,176 539,178 540,180 542,182 544,184 545,187 547,189 549,191 550,194 552,196 554,199 556,201 557,204 559,206 561,209 562,211 564,214 566,216 567,219 569,221 571,224 573,226 574,229 576,232 578,234 579,236 581,239 583,241 584,244 586,246 588,248 589,251 591,253 593,255 595,257 596,259 598,261 600,263 601,265 603,267 605,269 606,271 608,273 610,274 611,276 613,278 615,280 617,281 618,283 620,285 622,286 623,288 625,290 627,292 628,293 630,295 632,297 633,298 635,300 637,302 639,304 640,305 642,307 644,309 645,311 647,312 649,314 650,316 652,318 654,319 655,321 657,323 659,325 661,327 662,329 664,330 666,332 667,334 669,336 671,338 672,339 674,341 676,343 677,345 679,346 681,348 683,350 684,352 686,353 688,355 689,357 691,358 693,360 694,362 696,363 698,365 700,366 701,368 703,369 705,371 706,372 708,374 710,375 711,377 713,378 715,380 716,381 718,382 720,383 722,385 723,386 725,387 727,388 728,390 730,391 732,392 733,393 735,394 737,395 738,396 740,397 742,398 744,399 745,400 747,400 749,401 750,402 752,403 754,404 755,405 757,406 759,407 760,407 762,408 764,409 766,410 767,411 769,412 771,413 772,414 774,415 776,416 777,417 779,418 781,419 782,420 784,421 786,422 788,423 789,424 791,425 793,426 794,427 796,428 798,429 799,429 801,430 803,431 804,432 806,433 808,434 810,435 811,435 813,436 815,437 816,438 818,439 820,439 821,440 823,441 825,442 827,442 828,443 830,444 832,445 833,445 835,446 837,447 838,447 840,448 842,449 843,449 845,450 847,450 849,451 850,452 852,452 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="442,473 442,102 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/100:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="457" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.001
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,457 86,457 "/>
<text x="77" y="414" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,414 86,414 "/>
<text x="77" y="372" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.003
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,372 86,372 "/>
<text x="77" y="329" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,329 86,329 "/>
<text x="77" y="287" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,287 86,287 "/>
<text x="77" y="244" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,244 86,244 "/>
<text x="77" y="202" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.007
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,202 86,202 "/>
<text x="77" y="160" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,160 86,160 "/>
<text x="77" y="117" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.009
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,117 86,117 "/>
<text x="77" y="75" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,75 86,75 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="163" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="163,473 163,478 "/>
<text x="260" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="260,473 260,478 "/>
<text x="356" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="356,473 356,478 "/>
<text x="453" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="453,473 453,478 "/>
<text x="549" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="549,473 549,478 "/>
<text x="646" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="646,473 646,478 "/>
<text x="743" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
180
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="743,473 743,478 "/>
<text x="839" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="839,473 839,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,466 88,465 90,464 92,463 93,462 95,461 97,460 98,459 100,458 102,457 103,456 105,455 107,454 109,453 110,452 112,451 114,450 115,448 117,447 119,446 120,445 122,444 124,443 125,441 127,440 129,439 131,437 132,436 134,434 136,432 137,430 139,428 141,426 142,424 144,422 146,420 147,417 149,415 151,412 153,409 154,407 156,404 158,401 159,398 161,395 163,393 164,390 166,387 168,384 169,381 171,379 173,376 175,374 176,371 178,369 180,366 181,364 183,362 185,359 186,357 188,355 190,352 191,350 193,347 195,344 197,341 198,338 200,335 202,332 203,329 205,325 207,322 208,318 210,314 212,311 214,307 215,303 217,299 219,296 220,292 222,289 224,285 225,282 227,279 229,276 230,273 232,270 234,268 236,265 237,263 239,261 241,259 242,257 244,255 246,253 247,252 249,250 251,249 252,248 254,246 256,245 258,245 259,244 261,243 263,243 264,243 266,243 268,243 269,244 271,244 273,245 274,246 276,247 278,248 280,250 281,251 283,253 285,255 286,256 288,258 290,260 291,262 293,263 295,265 296,266 298,267 300,268 302,269 303,269 305,269 307,269 308,269 310,268 312,267 313,266 315,264 317,262 318,259 320,256 322,253 324,249 325,245 327,241 329,236 330,231 332,225 334,219 335,213 337,207 339,200 341,194 342,187 344,181 346,174 347,167 349,161 351,155 352,149 354,143 356,138 357,133 359,128 361,124 363,120 364,116 366,113 368,110 369,107 371,105 373,103 374,101 376,99 378,98 379,97 381,96 383,95 385,95 386,94 388,94 390,94 391,94 393,94 395,95 396,95 398,96 400,97 401,98 403,99 405,99 407,100 408,101 410,102 412,103 413,104 415,105 417,105 418,106 420,106 422,106 423,106 425,106 427,105 429,105 430,104 432,104 434,103 435,102 437,101 439,101 440,100 442,100 444,100 445,99 447,100 449,100 451,101 452,102 454,103 456,105 457,106 459,108 461,111 462,113 464,116 466,118 468,121 469,124 471,126 473,129 474,132 476,134 478,137 479,139 481,141 483,143 484,145 486,147 488,149 490,151 491,153 493,154 495,156 496,157 498,159 500,161 501,163 503,164 505,166 506,168 508,170 510,173 512,175 513,177 515,180 517,182 518,185 520,188 522,190 523,193 525,196 527,199 528,201 530,204 532,206 534,209 535,211 537,213 539,215 540,217 542,218 544,220 545,221 547,222 549,223 550,224 552,224 554,224 556,225 557,225 559,224 561,224 562,224 564,223 566,223 567,222 569,221 571,220 573,220 574,219 576,218 578,217 579,216 581,216 583,215 584,214 586,213 588,213 589,212 591,211 593,210 595,209 596,209 598,208 600,207 601,206 603,205 605,204 606,202 608,201 610,199 611,198 613,196 615,194 617,192 618,190 620,188 622,185 623,183 625,180 627,178 628,175 630,172 632,169 633,166 635,163 637,160 639,157 640,154 642,152 644,149 645,146 647,143 649,141 650,138 652,136 654,133 655,131 657,129 659,127 661,125 662,123 664,121 666,120 667,118 669,117 671,115 672,114 674,113 676,112 677,111 679,111 681,110 683,110 684,110 686,111 688,111 689,112 691,114 693,115 694,117 696,119 698,122 700,125 701,128 703,131 705,135 706,139 708,144 710,148 711,153 713,158 715,163 716,169 718,174 720,180 722,186 723,191 725,197 727,203 728,209 730,215 732,220 733,226 735,231 737,236 738,242 740,247 742,251 744,256 745,261 747,265 749,269 750,273 752,277 754,280 755,284 757,287 759,291 760,294 762,297 764,300 766,303 767,306 769,309 771,312 772,315 774,318 776,321 777,324 779,327 781,330 782,333 784,336 786,338 788,341 789,344 791,346 793,349 794,351 796,354 798,356 799,358 801,360 803,362 804,364 806,366 808,367 810,369 811,370 813,372 815,373 816,374 818,376 820,377 821,378 823,379 825,380 827,381 828,382 830,383 832,385 833,386 835,387 837,388 838,389 840,391 842,392 843,393 845,395 847,397 849,398 850,400 852,402 854,403 855,405 857,407 859,409 860,411 862,413 864,415 865,417 867,420 869,422 871,424 872,426 874,428 876,430 877,432 879,434 881,436 882,437 884,439 886,441 887,443 889,444 891,446 893,447 894,449 896,450 898,452 899,453 901,454 903,455 904,457 906,458 908,459 909,460 911,461 913,462 915,463 916,464 918,465 920,466 921,467 923,468 925,469 926,470 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,390 166,387 168,384 169,381 171,379 173,376 175,374 176,371 178,369 180,366 181,364 183,362 185,359 186,357 188,355 190,352 191,350 193,347 195,344 197,341 198,338 200,335 202,332 203,329 205,325 207,322 208,318 210,314 212,311 214,307 215,303 217,299 219,296 220,292 222,289 224,285 225,282 227,279 229,276 230,273 232,270 234,268 236,265 237,263 239,261 241,259 242,257 244,255 246,253 247,252 249,250 251,249 252,248 254,246 256,245 258,245 259,244 261,243 263,243 264,243 266,243 268,243 269,244 271,244 273,245 274,246 276,247 278,248 280,250 281,251 283,253 285,255 286,256 288,258 290,260 291,262 293,263 295,265 296,266 298,267 300,268 302,269 303,269 305,269 307,269 308,269 310,268 312,267 313,266 315,264 317,262 318,259 320,256 322,253 324,249 325,245 327,241 329,236 330,231 332,225 334,219 335,213 337,207 339,200 341,194 342,187 344,181 346,174 347,167 349,161 351,155 352,149 354,143 356,138 357,133 359,128 361,124 363,120 364,116 366,113 368,110 369,107 371,105 373,103 374,101 376,99 378,98 379,97 381,96 383,95 385,95 386,94 388,94 390,94 391,94 393,94 395,95 396,95 398,96 400,97 401,98 403,99 405,99 407,100 408,101 410,102 412,103 413,104 415,105 417,105 418,106 420,106 422,106 423,106 425,106 427,105 429,105 430,104 432,104 434,103 435,102 437,101 439,101 440,100 442,100 444,100 445,99 447,100 449,100 451,101 452,102 454,103 456,105 457,106 459,108 461,111 462,113 464,116 466,118 468,121 469,124 471,126 473,129 474,132 476,134 478,137 479,139 481,141 483,143 484,145 486,147 488,149 490,151 491,153 493,154 495,156 496,157 498,159 500,161 501,163 503,164 505,166 506,168 508,170 510,173 512,175 513,177 515,180 517,182 518,185 520,188 522,190 523,193 525,196 527,199 528,201 530,204 532,206 534,209 535,211 537,213 539,215 540,217 542,218 544,220 545,221 547,222 549,223 550,224 552,224 554,224 556,225 557,225 559,224 561,224 562,224 564,223 566,223 567,222 569,221 571,220 573,220 574,219 576,218 578,217 579,216 581,216 583,215 584,214 586,213 588,213 589,212 591,211 593,210 595,209 596,209 598,208 600,207 601,206 603,205 605,204 606,202 608,201 610,199 611,198 613,196 615,194 617,192 618,190 620,188 622,185 623,183 625,180 627,178 628,175 630,172 632,169 633,166 635,163 637,160 639,157 640,154 642,152 644,149 645,146 647,143 649,141 650,138 652,136 654,133 655,131 657,129 659,127 661,125 662,123 664,121 666,120 667,118 669,117 671,115 672,114 674,113 676,112 677,111 679,111 681,110 683,110 684,110 686,111 688,111 689,112 691,114 693,115 694,117 696,119 698,122 700,125 701,128 703,131 705,135 706,139 708,144 710,148 711,153 713,158 715,163 716,169 718,174 720,180 722,186 723,191 725,197 727,203 728,209 730,215 732,220 733,226 735,231 737,236 738,242 740,247 742,251 744,256 745,261 747,265 749,269 750,273 752,277 754,280 755,284 757,287 759,291 760,294 762,297 764,300 766,303 767,306 769,309 771,312 772,315 774,318 776,321 777,324 779,327 781,330 782,333 784,336 786,338 788,341 789,344 791,346 793,349 794,351 796,354 798,356 799,358 801,360 803,362 804,364 806,366 808,367 810,369 811,370 813,372 815,373 816,374 818,376 820,377 821,378 823,379 825,380 827,381 828,382 830,383 832,385 833,386 835,387 837,388 838,389 840,391 842,392 843,393 845,395 847,397 849,398 850,400 852,402 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="540,473 540,216 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

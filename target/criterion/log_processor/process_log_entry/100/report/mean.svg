<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/100:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="419" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,419 86,419 "/>
<text x="77" y="365" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,365 86,365 "/>
<text x="77" y="311" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,311 86,311 "/>
<text x="77" y="257" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,257 86,257 "/>
<text x="77" y="203" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,203 86,203 "/>
<text x="77" y="149" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,149 86,149 "/>
<text x="77" y="95" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,95 86,95 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="165" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="165,473 165,478 "/>
<text x="278" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="278,473 278,478 "/>
<text x="391" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="391,473 391,478 "/>
<text x="503" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="503,473 503,478 "/>
<text x="616" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="616,473 616,478 "/>
<text x="729" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="729,473 729,478 "/>
<text x="842" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="842,473 842,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,471 95,471 97,470 98,469 100,469 102,468 103,466 105,465 107,464 109,462 110,460 112,458 114,456 115,453 117,451 119,448 120,444 122,441 124,437 125,433 127,429 129,425 131,420 132,416 134,411 136,406 137,401 139,396 141,391 142,386 144,381 146,376 147,371 149,366 151,361 153,357 154,353 156,349 158,345 159,341 161,338 163,334 164,331 166,328 168,326 169,323 171,321 173,319 175,317 176,315 178,314 180,312 181,311 183,310 185,309 186,309 188,308 190,308 191,307 193,307 195,308 197,308 198,309 200,309 202,310 203,312 205,313 207,314 208,316 210,317 212,319 214,321 215,323 217,324 219,326 220,328 222,329 224,331 225,332 227,333 229,333 230,334 232,334 234,333 236,333 237,331 239,330 241,327 242,325 244,321 246,317 247,313 249,308 251,303 252,297 254,290 256,283 258,276 259,268 261,260 263,252 264,244 266,235 268,227 269,218 271,210 273,202 274,194 276,186 278,178 280,171 281,165 283,158 285,153 286,147 288,142 290,138 291,133 293,130 295,126 296,123 298,121 300,118 302,116 303,115 305,113 307,112 308,112 310,111 312,111 313,112 315,112 317,113 318,114 320,116 322,118 324,120 325,123 327,126 329,130 330,134 332,138 334,142 335,147 337,152 339,157 341,162 342,167 344,173 346,178 347,183 349,188 351,193 352,198 354,202 356,206 357,210 359,213 361,216 363,218 364,220 366,221 368,222 369,221 371,221 373,220 374,218 376,215 378,212 379,209 381,205 383,200 385,195 386,190 388,185 390,179 391,173 393,167 395,162 396,156 398,150 400,144 401,139 403,134 405,129 407,124 408,120 410,116 412,113 413,109 415,106 417,104 418,101 420,99 422,98 423,96 425,95 427,94 429,93 430,92 432,92 434,92 435,92 437,92 439,93 440,93 442,95 444,96 445,98 447,100 449,102 451,105 452,108 454,111 456,115 457,119 459,123 461,128 462,133 464,137 466,142 468,148 469,153 471,158 473,163 474,169 476,174 478,179 479,184 481,189 483,193 484,198 486,202 488,205 490,209 491,212 493,215 495,218 496,220 498,222 500,223 501,225 503,225 505,226 506,226 508,225 510,225 512,224 513,223 515,221 517,219 518,217 520,215 522,213 523,211 525,209 527,207 528,204 530,202 532,200 534,198 535,197 537,195 539,194 540,193 542,192 544,191 545,190 547,190 549,190 550,190 552,190 554,190 556,191 557,191 559,192 561,193 562,194 564,196 566,197 567,199 569,201 571,203 573,205 574,207 576,210 578,213 579,216 581,219 583,223 584,226 586,230 588,234 589,238 591,242 593,246 595,251 596,255 598,259 600,264 601,268 603,272 605,276 606,280 608,284 610,288 611,291 613,294 615,297 617,300 618,303 620,306 622,308 623,310 625,312 627,314 628,315 630,316 632,318 633,318 635,319 637,320 639,320 640,321 642,321 644,321 645,321 647,321 649,321 650,321 652,321 654,322 655,322 657,322 659,322 661,322 662,323 664,323 666,324 667,324 669,325 671,326 672,327 674,328 676,328 677,329 679,330 681,331 683,332 684,333 686,334 688,335 689,337 691,338 693,339 694,340 696,341 698,342 700,343 701,345 703,346 705,347 706,349 708,350 710,352 711,354 713,355 715,357 716,359 718,361 720,363 722,365 723,367 725,369 727,371 728,373 730,375 732,377 733,379 735,381 737,382 738,384 740,386 742,387 744,389 745,390 747,392 749,393 750,394 752,395 754,396 755,397 757,398 759,399 760,400 762,401 764,401 766,402 767,402 769,403 771,404 772,404 774,404 776,405 777,405 779,406 781,406 782,406 784,407 786,407 788,408 789,408 791,409 793,409 794,409 796,410 798,410 799,411 801,411 803,412 804,413 806,413 808,414 810,414 811,415 813,416 815,416 816,417 818,418 820,419 821,419 823,420 825,421 827,422 828,423 830,423 832,424 833,425 835,426 837,427 838,428 840,429 842,430 843,431 845,432 847,433 849,434 850,435 852,436 854,437 855,437 857,438 859,439 860,440 862,441 864,442 865,442 867,443 869,444 871,444 872,445 874,445 876,446 877,446 879,447 881,447 882,447 884,447 886,448 887,448 889,448 891,448 893,448 894,449 896,449 898,449 899,449 901,449 903,449 904,449 906,450 908,450 909,450 911,450 913,450 915,451 916,451 918,451 920,451 921,451 923,452 925,452 926,452 928,452 930,453 932,453 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,331 166,328 168,326 169,323 171,321 173,319 175,317 176,315 178,314 180,312 181,311 183,310 185,309 186,309 188,308 190,308 191,307 193,307 195,308 197,308 198,309 200,309 202,310 203,312 205,313 207,314 208,316 210,317 212,319 214,321 215,323 217,324 219,326 220,328 222,329 224,331 225,332 227,333 229,333 230,334 232,334 234,333 236,333 237,331 239,330 241,327 242,325 244,321 246,317 247,313 249,308 251,303 252,297 254,290 256,283 258,276 259,268 261,260 263,252 264,244 266,235 268,227 269,218 271,210 273,202 274,194 276,186 278,178 280,171 281,165 283,158 285,153 286,147 288,142 290,138 291,133 293,130 295,126 296,123 298,121 300,118 302,116 303,115 305,113 307,112 308,112 310,111 312,111 313,112 315,112 317,113 318,114 320,116 322,118 324,120 325,123 327,126 329,130 330,134 332,138 334,142 335,147 337,152 339,157 341,162 342,167 344,173 346,178 347,183 349,188 351,193 352,198 354,202 356,206 357,210 359,213 361,216 363,218 364,220 366,221 368,222 369,221 371,221 373,220 374,218 376,215 378,212 379,209 381,205 383,200 385,195 386,190 388,185 390,179 391,173 393,167 395,162 396,156 398,150 400,144 401,139 403,134 405,129 407,124 408,120 410,116 412,113 413,109 415,106 417,104 418,101 420,99 422,98 423,96 425,95 427,94 429,93 430,92 432,92 434,92 435,92 437,92 439,93 440,93 442,95 444,96 445,98 447,100 449,102 451,105 452,108 454,111 456,115 457,119 459,123 461,128 462,133 464,137 466,142 468,148 469,153 471,158 473,163 474,169 476,174 478,179 479,184 481,189 483,193 484,198 486,202 488,205 490,209 491,212 493,215 495,218 496,220 498,222 500,223 501,225 503,225 505,226 506,226 508,225 510,225 512,224 513,223 515,221 517,219 518,217 520,215 522,213 523,211 525,209 527,207 528,204 530,202 532,200 534,198 535,197 537,195 539,194 540,193 542,192 544,191 545,190 547,190 549,190 550,190 552,190 554,190 556,191 557,191 559,192 561,193 562,194 564,196 566,197 567,199 569,201 571,203 573,205 574,207 576,210 578,213 579,216 581,219 583,223 584,226 586,230 588,234 589,238 591,242 593,246 595,251 596,255 598,259 600,264 601,268 603,272 605,276 606,280 608,284 610,288 611,291 613,294 615,297 617,300 618,303 620,306 622,308 623,310 625,312 627,314 628,315 630,316 632,318 633,318 635,319 637,320 639,320 640,321 642,321 644,321 645,321 647,321 649,321 650,321 652,321 654,322 655,322 657,322 659,322 661,322 662,323 664,323 666,324 667,324 669,325 671,326 672,327 674,328 676,328 677,329 679,330 681,331 683,332 684,333 686,334 688,335 689,337 691,338 693,339 694,340 696,341 698,342 700,343 701,345 703,346 705,347 706,349 708,350 710,352 711,354 713,355 715,357 716,359 718,361 720,363 722,365 723,367 725,369 727,371 728,373 730,375 732,377 733,379 735,381 737,382 738,384 740,386 742,387 744,389 745,390 747,392 749,393 750,394 752,395 754,396 755,397 757,398 759,399 760,400 762,401 764,401 766,402 767,402 769,403 771,404 772,404 774,404 776,405 777,405 779,406 781,406 782,406 784,407 786,407 788,408 789,408 791,409 793,409 794,409 796,410 798,410 799,411 801,411 803,412 804,413 806,413 808,414 810,414 811,415 813,416 815,416 816,417 818,418 820,419 821,419 823,420 825,421 827,422 828,423 830,423 832,424 833,425 835,426 837,427 838,428 840,429 842,430 843,431 845,432 847,433 849,434 850,435 852,436 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="459,473 459,123 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/100:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="427" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,427 86,427 "/>
<text x="77" y="371" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,371 86,371 "/>
<text x="77" y="316" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,316 86,316 "/>
<text x="77" y="260" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,260 86,260 "/>
<text x="77" y="205" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,205 86,205 "/>
<text x="77" y="149" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,149 86,149 "/>
<text x="77" y="93" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,93 86,93 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="129" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="129,473 129,478 "/>
<text x="248" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="248,473 248,478 "/>
<text x="367" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="367,473 367,478 "/>
<text x="486" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="486,473 486,478 "/>
<text x="605" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="605,473 605,478 "/>
<text x="724" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="724,473 724,478 "/>
<text x="843" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="843,473 843,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,469 95,468 97,466 98,465 100,464 102,462 103,461 105,459 107,458 109,456 110,454 112,452 114,450 115,448 117,446 119,444 120,442 122,439 124,437 125,435 127,432 129,430 131,427 132,424 134,422 136,419 137,416 139,413 141,411 142,408 144,405 146,402 147,399 149,395 151,392 153,389 154,386 156,382 158,379 159,375 161,372 163,368 164,364 166,360 168,356 169,353 171,349 173,345 175,341 176,337 178,333 180,329 181,325 183,321 185,317 186,313 188,309 190,305 191,302 193,298 195,294 197,291 198,287 200,284 202,281 203,278 205,274 207,271 208,268 210,265 212,262 214,259 215,256 217,253 219,250 220,247 222,244 224,241 225,237 227,234 229,230 230,226 232,222 234,218 236,214 237,209 239,205 241,200 242,195 244,190 246,185 247,180 249,175 251,170 252,165 254,160 256,156 258,152 259,148 261,145 263,142 264,140 266,139 268,139 269,139 271,141 273,143 274,146 276,151 278,156 280,162 281,170 283,178 285,187 286,197 288,207 290,218 291,230 293,242 295,254 296,266 298,278 300,291 302,303 303,314 305,325 307,336 308,346 310,356 312,364 313,372 315,379 317,385 318,390 320,395 322,398 324,400 325,402 327,402 329,402 330,401 332,399 334,396 335,393 337,389 339,385 341,380 342,375 344,369 346,364 347,358 349,352 351,346 352,340 354,335 356,330 357,325 359,320 361,316 363,313 364,310 366,308 368,306 369,305 371,304 373,304 374,305 376,306 378,308 379,310 381,313 383,316 385,319 386,323 388,326 390,330 391,334 393,338 395,342 396,346 398,350 400,353 401,357 403,360 405,363 407,365 408,368 410,370 412,371 413,373 415,373 417,374 418,374 420,374 422,373 423,372 425,371 427,369 429,366 430,364 432,361 434,358 435,354 437,350 439,346 440,342 442,338 444,334 445,329 447,325 449,321 451,317 452,313 454,309 456,306 457,302 459,299 461,296 462,294 464,291 466,289 468,287 469,285 471,284 473,283 474,281 476,280 478,280 479,279 481,279 483,278 484,278 486,278 488,278 490,278 491,278 493,278 495,279 496,279 498,279 500,280 501,280 503,281 505,281 506,281 508,282 510,282 512,282 513,282 515,282 517,281 518,281 520,281 522,281 523,280 525,280 527,280 528,280 530,280 532,280 534,280 535,280 537,280 539,281 540,282 542,283 544,284 545,285 547,286 549,287 550,288 552,289 554,290 556,290 557,291 559,291 561,291 562,291 564,291 566,290 567,290 569,289 571,288 573,286 574,285 576,284 578,282 579,281 581,280 583,278 584,277 586,277 588,276 589,276 591,276 593,276 595,277 596,278 598,279 600,280 601,281 603,283 605,284 606,285 608,286 610,287 611,288 613,288 615,287 617,286 618,285 620,283 622,280 623,277 625,273 627,268 628,263 630,257 632,250 633,243 635,236 637,229 639,221 640,213 642,205 644,197 645,190 647,182 649,175 650,169 652,163 654,158 655,153 657,149 659,146 661,143 662,142 664,141 666,140 667,141 669,142 671,144 672,146 674,149 676,152 677,156 679,159 681,163 683,168 684,172 686,176 688,180 689,184 691,188 693,192 694,195 696,198 698,201 700,204 701,206 703,208 705,210 706,212 708,213 710,214 711,215 713,216 715,217 716,218 718,218 720,219 722,219 723,220 725,220 727,220 728,220 730,220 732,220 733,220 735,219 737,218 738,217 740,216 742,215 744,213 745,211 747,209 749,206 750,203 752,200 754,197 755,193 757,189 759,185 760,181 762,176 764,172 766,167 767,162 769,157 771,152 772,148 774,143 776,138 777,133 779,129 781,124 782,120 784,116 786,112 788,108 789,105 791,102 793,99 794,97 796,95 798,93 799,93 801,92 803,93 804,94 806,96 808,98 810,101 811,105 813,110 815,115 816,122 818,129 820,136 821,145 823,154 825,163 827,173 828,184 830,195 832,206 833,218 835,229 837,241 838,253 840,265 842,276 843,288 845,299 847,310 849,320 850,330 852,339 854,348 855,357 857,365 859,372 860,379 862,386 864,392 865,397 867,402 869,407 871,411 872,415 874,418 876,421 877,424 879,427 881,429 882,431 884,433 886,435 887,436 889,438 891,439 893,440 894,441 896,442 898,443 899,444 901,445 903,446 904,447 906,448 908,448 909,449 911,450 913,451 915,452 916,452 918,453 920,454 921,455 923,456 925,457 926,458 928,458 930,459 932,460 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,364 166,360 168,356 169,353 171,349 173,345 175,341 176,337 178,333 180,329 181,325 183,321 185,317 186,313 188,309 190,305 191,302 193,298 195,294 197,291 198,287 200,284 202,281 203,278 205,274 207,271 208,268 210,265 212,262 214,259 215,256 217,253 219,250 220,247 222,244 224,241 225,237 227,234 229,230 230,226 232,222 234,218 236,214 237,209 239,205 241,200 242,195 244,190 246,185 247,180 249,175 251,170 252,165 254,160 256,156 258,152 259,148 261,145 263,142 264,140 266,139 268,139 269,139 271,141 273,143 274,146 276,151 278,156 280,162 281,170 283,178 285,187 286,197 288,207 290,218 291,230 293,242 295,254 296,266 298,278 300,291 302,303 303,314 305,325 307,336 308,346 310,356 312,364 313,372 315,379 317,385 318,390 320,395 322,398 324,400 325,402 327,402 329,402 330,401 332,399 334,396 335,393 337,389 339,385 341,380 342,375 344,369 346,364 347,358 349,352 351,346 352,340 354,335 356,330 357,325 359,320 361,316 363,313 364,310 366,308 368,306 369,305 371,304 373,304 374,305 376,306 378,308 379,310 381,313 383,316 385,319 386,323 388,326 390,330 391,334 393,338 395,342 396,346 398,350 400,353 401,357 403,360 405,363 407,365 408,368 410,370 412,371 413,373 415,373 417,374 418,374 420,374 422,373 423,372 425,371 427,369 429,366 430,364 432,361 434,358 435,354 437,350 439,346 440,342 442,338 444,334 445,329 447,325 449,321 451,317 452,313 454,309 456,306 457,302 459,299 461,296 462,294 464,291 466,289 468,287 469,285 471,284 473,283 474,281 476,280 478,280 479,279 481,279 483,278 484,278 486,278 488,278 490,278 491,278 493,278 495,279 496,279 498,279 500,280 501,280 503,281 505,281 506,281 508,282 510,282 512,282 513,282 515,282 517,281 518,281 520,281 522,281 523,280 525,280 527,280 528,280 530,280 532,280 534,280 535,280 537,280 539,281 540,282 542,283 544,284 545,285 547,286 549,287 550,288 552,289 554,290 556,290 557,291 559,291 561,291 562,291 564,291 566,290 567,290 569,289 571,288 573,286 574,285 576,284 578,282 579,281 581,280 583,278 584,277 586,277 588,276 589,276 591,276 593,276 595,277 596,278 598,279 600,280 601,281 603,283 605,284 606,285 608,286 610,287 611,288 613,288 615,287 617,286 618,285 620,283 622,280 623,277 625,273 627,268 628,263 630,257 632,250 633,243 635,236 637,229 639,221 640,213 642,205 644,197 645,190 647,182 649,175 650,169 652,163 654,158 655,153 657,149 659,146 661,143 662,142 664,141 666,140 667,141 669,142 671,144 672,146 674,149 676,152 677,156 679,159 681,163 683,168 684,172 686,176 688,180 689,184 691,188 693,192 694,195 696,198 698,201 700,204 701,206 703,208 705,210 706,212 708,213 710,214 711,215 713,216 715,217 716,218 718,218 720,219 722,219 723,220 725,220 727,220 728,220 730,220 732,220 733,220 735,219 737,218 738,217 740,216 742,215 744,213 745,211 747,209 749,206 750,203 752,200 754,197 755,193 757,189 759,185 760,181 762,176 764,172 766,167 767,162 769,157 771,152 772,148 774,143 776,138 777,133 779,129 781,124 782,120 784,116 786,112 788,108 789,105 791,102 793,99 794,97 796,95 798,93 799,93 801,92 803,93 804,94 806,96 808,98 810,101 811,105 813,110 815,115 816,122 818,129 820,136 821,145 823,154 825,163 827,173 828,184 830,195 832,206 833,218 835,229 837,241 838,253 840,265 842,276 843,288 845,299 847,310 849,320 850,330 852,339 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="589,473 589,276 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/100:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="437" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,437 86,437 "/>
<text x="77" y="401" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,401 86,401 "/>
<text x="77" y="366" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,366 86,366 "/>
<text x="77" y="330" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,330 86,330 "/>
<text x="77" y="294" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,294 86,294 "/>
<text x="77" y="259" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,259 86,259 "/>
<text x="77" y="223" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,223 86,223 "/>
<text x="77" y="187" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,187 86,187 "/>
<text x="77" y="151" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,151 86,151 "/>
<text x="77" y="116" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,116 86,116 "/>
<text x="77" y="80" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,80 86,80 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="115" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="115,473 115,478 "/>
<text x="223" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="223,473 223,478 "/>
<text x="330" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="330,473 330,478 "/>
<text x="438" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="438,473 438,478 "/>
<text x="545" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="545,473 545,478 "/>
<text x="652" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="652,473 652,478 "/>
<text x="760" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="760,473 760,478 "/>
<text x="867" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="867,473 867,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,472 98,471 100,471 102,471 103,470 105,470 107,469 109,469 110,468 112,467 114,466 115,465 117,463 119,462 120,460 122,459 124,457 125,455 127,453 129,450 131,448 132,446 134,443 136,440 137,438 139,435 141,432 142,430 144,427 146,425 147,422 149,420 151,418 153,416 154,415 156,413 158,412 159,411 161,411 163,411 164,411 166,411 168,412 169,413 171,415 173,416 175,418 176,420 178,422 180,424 181,427 183,429 185,432 186,434 188,437 190,439 191,441 193,443 195,445 197,446 198,447 200,448 202,448 203,448 205,447 207,446 208,445 210,443 212,441 214,439 215,436 217,432 219,429 220,425 222,421 224,417 225,413 227,409 229,406 230,402 232,399 234,396 236,394 237,392 239,391 241,390 242,390 244,390 246,390 247,392 249,393 251,395 252,397 254,399 256,402 258,404 259,406 261,409 263,411 264,413 266,415 268,416 269,418 271,419 273,420 274,421 276,422 278,422 280,423 281,423 283,424 285,425 286,426 288,426 290,427 291,428 293,430 295,431 296,432 298,434 300,435 302,436 303,438 305,439 307,441 308,442 310,444 312,445 313,446 315,448 317,449 318,450 320,451 322,452 324,453 325,454 327,455 329,456 330,457 332,458 334,459 335,460 337,461 339,461 341,462 342,463 344,463 346,464 347,465 349,465 351,466 352,466 354,467 356,467 357,468 359,468 361,469 363,469 364,469 366,470 368,470 369,470 371,470 373,471 374,471 376,471 378,471 379,471 381,471 383,472 385,472 386,472 388,472 390,472 391,472 393,472 395,472 396,471 398,471 400,471 401,470 403,470 405,469 407,468 408,467 410,465 412,463 413,461 415,459 417,455 418,452 420,448 422,443 423,438 425,431 427,424 429,417 430,408 432,399 434,389 435,378 437,366 439,354 440,341 442,328 444,314 445,300 447,285 449,271 451,256 452,242 454,229 456,215 457,203 459,191 461,181 462,171 464,163 466,156 468,150 469,146 471,144 473,143 474,143 476,145 478,148 479,153 481,159 483,166 484,174 486,183 488,194 490,204 491,216 493,228 495,240 496,253 498,265 500,278 501,291 503,303 505,315 506,327 508,339 510,350 512,360 513,370 515,380 517,389 518,397 520,405 522,412 523,419 525,424 527,430 528,435 530,439 532,443 534,446 535,449 537,451 539,453 540,455 542,456 544,456 545,456 547,455 549,453 550,451 552,448 554,445 556,440 557,435 559,429 561,422 562,414 564,404 566,394 567,383 569,370 571,356 573,342 574,326 576,310 578,293 579,275 581,257 583,239 584,221 586,204 588,186 589,170 591,155 593,140 595,128 596,117 598,107 600,100 601,95 603,92 605,92 606,93 608,97 610,103 611,111 613,121 615,132 617,145 618,159 620,175 622,191 623,207 625,225 627,242 628,259 630,276 632,293 633,309 635,324 637,338 639,352 640,364 642,375 644,385 645,394 647,402 649,409 650,414 652,418 654,420 655,422 657,422 659,421 661,419 662,415 664,410 666,405 667,397 669,389 671,380 672,370 674,359 676,347 677,335 679,323 681,310 683,297 684,284 686,271 688,259 689,248 691,237 693,228 694,220 696,214 698,209 700,205 701,204 703,204 705,206 706,210 708,215 710,222 711,230 713,239 715,250 716,261 718,273 720,286 722,298 723,311 725,323 727,336 728,347 730,358 732,369 733,378 735,387 737,394 738,400 740,406 742,410 744,413 745,414 747,415 749,415 750,414 752,411 754,408 755,404 757,400 759,395 760,389 762,384 764,378 766,372 767,366 769,360 771,355 772,350 774,346 776,342 777,340 779,338 781,337 782,336 784,337 786,339 788,341 789,345 791,349 793,353 794,359 796,364 798,370 799,376 801,383 803,389 804,395 806,401 808,407 810,412 811,417 813,421 815,425 816,428 818,430 820,432 821,434 823,435 825,435 827,435 828,434 830,434 832,432 833,431 835,430 837,428 838,427 840,425 842,424 843,423 845,422 847,421 849,421 850,421 852,421 854,421 855,422 857,423 859,425 860,426 862,428 864,430 865,433 867,435 869,437 871,439 872,441 874,444 876,446 877,447 879,449 881,451 882,452 884,453 886,454 887,454 889,455 891,455 893,456 894,456 896,456 898,456 899,455 901,455 903,455 904,455 906,455 908,455 909,455 911,455 913,455 915,456 916,456 918,457 920,457 921,458 923,458 925,459 926,460 928,460 930,461 932,462 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,411 166,411 168,412 169,413 171,415 173,416 175,418 176,420 178,422 180,424 181,427 183,429 185,432 186,434 188,437 190,439 191,441 193,443 195,445 197,446 198,447 200,448 202,448 203,448 205,447 207,446 208,445 210,443 212,441 214,439 215,436 217,432 219,429 220,425 222,421 224,417 225,413 227,409 229,406 230,402 232,399 234,396 236,394 237,392 239,391 241,390 242,390 244,390 246,390 247,392 249,393 251,395 252,397 254,399 256,402 258,404 259,406 261,409 263,411 264,413 266,415 268,416 269,418 271,419 273,420 274,421 276,422 278,422 280,423 281,423 283,424 285,425 286,426 288,426 290,427 291,428 293,430 295,431 296,432 298,434 300,435 302,436 303,438 305,439 307,441 308,442 310,444 312,445 313,446 315,448 317,449 318,450 320,451 322,452 324,453 325,454 327,455 329,456 330,457 332,458 334,459 335,460 337,461 339,461 341,462 342,463 344,463 346,464 347,465 349,465 351,466 352,466 354,467 356,467 357,468 359,468 361,469 363,469 364,469 366,470 368,470 369,470 371,470 373,471 374,471 376,471 378,471 379,471 381,471 383,472 385,472 386,472 388,472 390,472 391,472 393,472 395,472 396,471 398,471 400,471 401,470 403,470 405,469 407,468 408,467 410,465 412,463 413,461 415,459 417,455 418,452 420,448 422,443 423,438 425,431 427,424 429,417 430,408 432,399 434,389 435,378 437,366 439,354 440,341 442,328 444,314 445,300 447,285 449,271 451,256 452,242 454,229 456,215 457,203 459,191 461,181 462,171 464,163 466,156 468,150 469,146 471,144 473,143 474,143 476,145 478,148 479,153 481,159 483,166 484,174 486,183 488,194 490,204 491,216 493,228 495,240 496,253 498,265 500,278 501,291 503,303 505,315 506,327 508,339 510,350 512,360 513,370 515,380 517,389 518,397 520,405 522,412 523,419 525,424 527,430 528,435 530,439 532,443 534,446 535,449 537,451 539,453 540,455 542,456 544,456 545,456 547,455 549,453 550,451 552,448 554,445 556,440 557,435 559,429 561,422 562,414 564,404 566,394 567,383 569,370 571,356 573,342 574,326 576,310 578,293 579,275 581,257 583,239 584,221 586,204 588,186 589,170 591,155 593,140 595,128 596,117 598,107 600,100 601,95 603,92 605,92 606,93 608,97 610,103 611,111 613,121 615,132 617,145 618,159 620,175 622,191 623,207 625,225 627,242 628,259 630,276 632,293 633,309 635,324 637,338 639,352 640,364 642,375 644,385 645,394 647,402 649,409 650,414 652,418 654,420 655,422 657,422 659,421 661,419 662,415 664,410 666,405 667,397 669,389 671,380 672,370 674,359 676,347 677,335 679,323 681,310 683,297 684,284 686,271 688,259 689,248 691,237 693,228 694,220 696,214 698,209 700,205 701,204 703,204 705,206 706,210 708,215 710,222 711,230 713,239 715,250 716,261 718,273 720,286 722,298 723,311 725,323 727,336 728,347 730,358 732,369 733,378 735,387 737,394 738,400 740,406 742,410 744,413 745,414 747,415 749,415 750,414 752,411 754,408 755,404 757,400 759,395 760,389 762,384 764,378 766,372 767,366 769,360 771,355 772,350 774,346 776,342 777,340 779,338 781,337 782,336 784,337 786,339 788,341 789,345 791,349 793,353 794,359 796,364 798,370 799,376 801,383 803,389 804,395 806,401 808,407 810,412 811,417 813,421 815,425 816,428 818,430 820,432 821,434 823,435 825,435 827,435 828,434 830,434 832,432 833,431 835,430 837,428 838,427 840,425 842,424 843,423 845,422 847,421 849,421 850,421 852,421 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="605,473 605,92 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

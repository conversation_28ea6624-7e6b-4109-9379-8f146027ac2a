<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="442" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,442 86,442 "/>
<text x="77" y="390" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,390 86,390 "/>
<text x="77" y="337" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,337 86,337 "/>
<text x="77" y="285" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,285 86,285 "/>
<text x="77" y="233" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,233 86,233 "/>
<text x="77" y="181" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,181 86,181 "/>
<text x="77" y="129" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,129 86,129 "/>
<text x="77" y="77" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,77 86,77 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="142" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.53
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="142,473 142,478 "/>
<text x="227" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.535
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="227,473 227,478 "/>
<text x="312" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.54
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="312,473 312,478 "/>
<text x="397" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.545
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="397,473 397,478 "/>
<text x="482" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.55
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="482,473 482,478 "/>
<text x="567" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.555
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="567,473 567,478 "/>
<text x="652" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.56
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="652,473 652,478 "/>
<text x="737" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.565
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="737,473 737,478 "/>
<text x="822" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.57
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="822,473 822,478 "/>
<text x="907" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.575
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="907,473 907,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,471 88,470 90,470 92,469 93,468 95,467 97,466 98,465 100,464 102,463 103,462 105,461 107,460 109,459 110,458 112,457 114,456 115,455 117,454 119,453 120,451 122,450 124,449 125,448 127,447 129,445 131,444 132,443 134,442 136,441 137,439 139,438 141,437 142,435 144,434 146,433 147,431 149,430 151,428 153,427 154,425 156,423 158,422 159,420 161,418 163,416 164,415 166,413 168,411 169,409 171,407 173,405 175,404 176,402 178,400 180,398 181,396 183,394 185,392 186,390 188,389 190,387 191,385 193,383 195,381 197,379 198,378 200,376 202,374 203,372 205,370 207,368 208,366 210,364 212,361 214,359 215,357 217,354 219,352 220,349 222,347 224,344 225,342 227,339 229,336 230,333 232,330 234,328 236,325 237,322 239,319 241,316 242,313 244,310 246,307 247,304 249,302 251,299 252,296 254,293 256,290 258,288 259,285 261,282 263,280 264,277 266,275 268,272 269,270 271,267 273,265 274,262 276,260 278,257 280,255 281,252 283,250 285,247 286,245 288,242 290,240 291,237 293,235 295,232 296,230 298,227 300,225 302,222 303,220 305,217 307,214 308,212 310,209 312,206 313,204 315,201 317,198 318,195 320,193 322,190 324,187 325,184 327,182 329,179 330,177 332,174 334,172 335,169 337,167 339,164 341,162 342,160 344,158 346,156 347,154 349,152 351,150 352,148 354,146 356,144 357,143 359,141 361,139 363,138 364,136 366,135 368,133 369,132 371,130 373,129 374,127 376,126 378,124 379,123 381,122 383,120 385,119 386,118 388,116 390,115 391,114 393,113 395,112 396,111 398,110 400,108 401,108 403,107 405,106 407,105 408,104 410,103 412,102 413,102 415,101 417,100 418,100 420,99 422,99 423,98 425,98 427,97 429,97 430,96 432,96 434,96 435,95 437,95 439,95 440,94 442,94 444,94 445,94 447,94 449,94 451,94 452,94 454,94 456,94 457,94 459,94 461,94 462,94 464,95 466,95 468,96 469,96 471,97 473,98 474,98 476,99 478,100 479,101 481,102 483,103 484,104 486,105 488,106 490,107 491,108 493,109 495,110 496,110 498,111 500,112 501,113 503,114 505,115 506,116 508,117 510,118 512,119 513,120 515,121 517,122 518,124 520,125 522,126 523,128 525,129 527,130 528,132 530,133 532,135 534,136 535,138 537,139 539,141 540,142 542,144 544,145 545,146 547,148 549,149 550,151 552,152 554,153 556,155 557,156 559,157 561,159 562,160 564,162 566,163 567,165 569,167 571,168 573,170 574,172 576,173 578,175 579,177 581,179 583,181 584,183 586,185 588,187 589,189 591,191 593,193 595,196 596,198 598,200 600,202 601,204 603,207 605,209 606,211 608,213 610,215 611,218 613,220 615,222 617,224 618,226 620,229 622,231 623,233 625,235 627,238 628,240 630,242 632,245 633,247 635,250 637,252 639,255 640,257 642,260 644,262 645,265 647,267 649,269 650,272 652,274 654,276 655,279 657,281 659,283 661,285 662,287 664,289 666,291 667,293 669,295 671,297 672,299 674,301 676,303 677,304 679,306 681,308 683,310 684,311 686,313 688,315 689,316 691,318 693,320 694,322 696,323 698,325 700,327 701,328 703,330 705,332 706,333 708,335 710,337 711,339 713,340 715,342 716,344 718,345 720,347 722,348 723,350 725,352 727,353 728,355 730,356 732,358 733,359 735,361 737,362 738,364 740,366 742,367 744,369 745,370 747,372 749,373 750,375 752,376 754,378 755,380 757,381 759,383 760,384 762,386 764,387 766,389 767,390 769,392 771,393 772,394 774,396 776,397 777,399 779,400 781,401 782,403 784,404 786,405 788,406 789,408 791,409 793,410 794,411 796,412 798,413 799,415 801,416 803,417 804,418 806,419 808,420 810,421 811,422 813,423 815,424 816,425 818,426 820,427 821,428 823,429 825,431 827,432 828,433 830,434 832,435 833,436 835,437 837,438 838,438 840,439 842,440 843,441 845,442 847,443 849,444 850,444 852,445 854,446 855,447 857,447 859,448 860,449 862,449 864,450 865,450 867,451 869,451 871,452 872,452 874,453 876,454 877,454 879,455 881,455 882,456 884,456 886,457 887,457 889,458 891,458 893,459 894,459 896,460 898,461 899,461 901,462 903,463 904,463 906,464 908,465 909,465 911,466 913,466 915,467 916,468 918,468 920,469 921,469 923,470 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,415 166,413 168,411 169,409 171,407 173,405 175,404 176,402 178,400 180,398 181,396 183,394 185,392 186,390 188,389 190,387 191,385 193,383 195,381 197,379 198,378 200,376 202,374 203,372 205,370 207,368 208,366 210,364 212,361 214,359 215,357 217,354 219,352 220,349 222,347 224,344 225,342 227,339 229,336 230,333 232,330 234,328 236,325 237,322 239,319 241,316 242,313 244,310 246,307 247,304 249,302 251,299 252,296 254,293 256,290 258,288 259,285 261,282 263,280 264,277 266,275 268,272 269,270 271,267 273,265 274,262 276,260 278,257 280,255 281,252 283,250 285,247 286,245 288,242 290,240 291,237 293,235 295,232 296,230 298,227 300,225 302,222 303,220 305,217 307,214 308,212 310,209 312,206 313,204 315,201 317,198 318,195 320,193 322,190 324,187 325,184 327,182 329,179 330,177 332,174 334,172 335,169 337,167 339,164 341,162 342,160 344,158 346,156 347,154 349,152 351,150 352,148 354,146 356,144 357,143 359,141 361,139 363,138 364,136 366,135 368,133 369,132 371,130 373,129 374,127 376,126 378,124 379,123 381,122 383,120 385,119 386,118 388,116 390,115 391,114 393,113 395,112 396,111 398,110 400,108 401,108 403,107 405,106 407,105 408,104 410,103 412,102 413,102 415,101 417,100 418,100 420,99 422,99 423,98 425,98 427,97 429,97 430,96 432,96 434,96 435,95 437,95 439,95 440,94 442,94 444,94 445,94 447,94 449,94 451,94 452,94 454,94 456,94 457,94 459,94 461,94 462,94 464,95 466,95 468,96 469,96 471,97 473,98 474,98 476,99 478,100 479,101 481,102 483,103 484,104 486,105 488,106 490,107 491,108 493,109 495,110 496,110 498,111 500,112 501,113 503,114 505,115 506,116 508,117 510,118 512,119 513,120 515,121 517,122 518,124 520,125 522,126 523,128 525,129 527,130 528,132 530,133 532,135 534,136 535,138 537,139 539,141 540,142 542,144 544,145 545,146 547,148 549,149 550,151 552,152 554,153 556,155 557,156 559,157 561,159 562,160 564,162 566,163 567,165 569,167 571,168 573,170 574,172 576,173 578,175 579,177 581,179 583,181 584,183 586,185 588,187 589,189 591,191 593,193 595,196 596,198 598,200 600,202 601,204 603,207 605,209 606,211 608,213 610,215 611,218 613,220 615,222 617,224 618,226 620,229 622,231 623,233 625,235 627,238 628,240 630,242 632,245 633,247 635,250 637,252 639,255 640,257 642,260 644,262 645,265 647,267 649,269 650,272 652,274 654,276 655,279 657,281 659,283 661,285 662,287 664,289 666,291 667,293 669,295 671,297 672,299 674,301 676,303 677,304 679,306 681,308 683,310 684,311 686,313 688,315 689,316 691,318 693,320 694,322 696,323 698,325 700,327 701,328 703,330 705,332 706,333 708,335 710,337 711,339 713,340 715,342 716,344 718,345 720,347 722,348 723,350 725,352 727,353 728,355 730,356 732,358 733,359 735,361 737,362 738,364 740,366 742,367 744,369 745,370 747,372 749,373 750,375 752,376 754,378 755,380 757,381 759,383 760,384 762,386 764,387 766,389 767,390 769,392 771,393 772,394 774,396 776,397 777,399 779,400 781,401 782,403 784,404 786,405 788,406 789,408 791,409 793,410 794,411 796,412 798,413 799,415 801,416 803,417 804,418 806,419 808,420 810,421 811,422 813,423 815,424 816,425 818,426 820,427 821,428 823,429 825,431 827,432 828,433 830,434 832,435 833,436 835,437 837,438 838,438 840,439 842,440 843,441 845,442 847,443 849,444 850,444 852,445 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="479,473 479,101 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

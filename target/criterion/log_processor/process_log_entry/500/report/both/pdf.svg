<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average Time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="425" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,425 86,425 "/>
<text x="77" y="378" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,378 86,378 "/>
<text x="77" y="330" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,330 86,330 "/>
<text x="77" y="283" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,283 86,283 "/>
<text x="77" y="235" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,235 86,235 "/>
<text x="77" y="188" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,188 86,188 "/>
<text x="77" y="140" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,140 86,140 "/>
<text x="77" y="93" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,93 86,93 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="172" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="172,473 172,478 "/>
<text x="421" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="421,473 421,478 "/>
<text x="670" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="670,473 670,478 "/>
<text x="920" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="920,473 920,478 "/>
<polygon opacity="0.5" fill="#E31A1C" points="87,472 88,472 89,472 90,472 91,472 92,472 94,472 95,472 96,472 97,472 98,472 100,472 101,472 102,472 103,472 104,472 105,472 107,472 108,472 109,472 110,472 111,472 113,472 114,472 115,472 116,472 117,471 118,471 120,471 121,471 122,471 123,471 124,471 126,471 127,471 128,471 129,470 130,470 131,470 133,470 134,470 135,470 136,469 137,469 139,469 140,469 141,469 142,469 143,468 144,468 146,468 147,468 148,468 149,467 150,467 152,467 153,467 154,467 155,466 156,466 157,466 159,466 160,466 161,466 162,465 163,465 165,465 166,465 167,465 168,465 169,465 170,464 172,464 173,464 174,464 175,464 176,464 178,464 179,464 180,464 181,464 182,464 183,464 185,464 186,463 187,463 188,463 189,463 191,463 192,463 193,463 194,463 195,463 196,463 198,463 199,463 200,463 201,462 202,462 204,462 205,462 206,462 207,461 208,461 210,461 211,461 212,460 213,460 214,459 215,459 217,459 218,458 219,458 220,457 221,456 223,456 224,455 225,454 226,453 227,453 228,452 230,451 231,450 232,448 233,447 234,446 236,445 237,443 238,442 239,440 240,439 241,437 243,435 244,433 245,431 246,429 247,427 249,424 250,422 251,419 252,416 253,413 254,410 256,407 257,404 258,400 259,397 260,393 262,389 263,385 264,381 265,376 266,372 267,367 269,362 270,357 271,352 272,346 273,341 275,335 276,329 277,323 278,317 279,311 280,304 282,298 283,291 284,284 285,278 286,271 288,264 289,257 290,250 291,242 292,235 293,228 295,221 296,214 297,206 298,199 299,192 301,185 302,178 303,171 304,164 305,158 306,151 308,145 309,138 310,132 311,126 312,121 314,115 315,110 316,104 317,99 318,95 320,90 321,86 322,82 323,78 324,75 325,71 327,68 328,66 329,63 330,61 331,59 333,57 334,56 335,55 336,54 337,54 338,53 340,54 341,54 342,54 343,55 344,56 346,57 347,59 348,61 349,63 350,65 351,67 353,70 354,73 355,76 356,79 357,83 359,86 360,90 361,94 362,98 363,102 364,107 366,111 367,116 368,121 369,126 370,130 372,135 373,141 374,146 375,151 376,156 377,161 379,167 380,172 381,178 382,183 383,188 385,194 386,199 387,205 388,210 389,215 390,221 392,226 393,231 394,236 395,242 396,247 398,252 399,257 400,262 401,267 402,271 403,276 405,281 406,285 407,290 408,294 409,298 411,303 412,307 413,311 414,315 415,319 416,322 418,326 419,330 420,333 421,337 422,340 424,343 425,346 426,350 427,352 428,355 430,358 431,361 432,364 433,366 434,369 435,371 437,373 438,375 439,378 440,380 441,382 443,383 444,385 445,387 446,389 447,390 448,392 450,393 451,394 452,396 453,397 454,398 456,399 457,400 458,401 459,402 460,402 461,403 463,404 464,404 465,405 466,405 467,406 469,406 470,406 471,406 472,407 473,407 474,407 476,407 477,407 478,407 479,406 480,406 482,406 483,406 484,405 485,405 486,405 487,404 489,404 490,404 491,403 492,403 493,403 495,402 496,402 497,401 498,401 499,401 500,400 502,400 503,400 504,399 505,399 506,399 508,399 509,398 510,398 511,398 512,398 513,398 515,398 516,398 517,398 518,399 519,399 521,399 522,400 523,400 524,401 525,401 526,402 528,402 529,403 530,404 531,405 532,405 534,406 535,407 536,408 537,409 538,410 539,411 541,412 542,414 543,415 544,416 545,417 547,418 548,420 549,421 550,422 551,423 553,425 554,426 555,427 556,428 557,430 558,431 560,432 561,433 562,435 563,436 564,437 566,438 567,439 568,440 569,441 570,442 571,444 573,445 574,446 575,446 576,447 577,448 579,449 580,450 581,451 582,452 583,453 584,453 586,454 587,455 588,455 589,456 590,457 592,457 593,458 594,459 595,459 596,460 597,460 599,461 600,461 601,462 602,462 603,463 605,463 606,463 607,464 608,464 609,465 610,465 612,465 613,466 614,466 615,466 616,467 618,467 619,467 620,468 621,468 622,468 623,468 625,469 626,469 627,469 628,469 629,469 631,470 632,470 633,470 634,470 635,470 636,470 638,471 639,471 640,471 641,471 642,471 644,471 645,471 646,471 647,472 648,472 649,472 651,472 652,472 653,472 654,472 655,472 657,472 658,472 659,472 660,472 661,472 663,472 664,472 665,472 666,472 667,472 668,472 670,472 671,472 672,472 673,472 674,472 676,472 677,472 677,472 87,472 "/>
<polygon opacity="0.5" fill="#1F78B4" points="108,472 110,472 111,472 113,472 115,472 116,472 118,472 120,472 121,472 123,472 125,472 126,472 128,472 130,472 131,471 133,471 135,471 136,471 138,471 139,471 141,471 143,470 144,470 146,470 148,470 149,469 151,469 153,469 154,468 156,468 158,468 159,467 161,467 163,466 164,466 166,465 168,464 169,464 171,463 172,462 174,461 176,460 177,459 179,458 181,457 182,456 184,455 186,454 187,452 189,451 191,449 192,448 194,446 196,444 197,443 199,441 201,439 202,437 204,434 205,432 207,430 209,427 210,425 212,422 214,419 215,416 217,413 219,410 220,407 222,403 224,400 225,396 227,393 229,389 230,385 232,381 234,377 235,373 237,368 238,364 240,359 242,355 243,350 245,345 247,340 248,335 250,330 252,325 253,319 255,314 257,309 258,303 260,298 262,292 263,287 265,281 267,275 268,270 270,264 271,258 273,253 275,247 276,242 278,236 280,230 281,225 283,220 285,214 286,209 288,204 290,199 291,194 293,189 295,184 296,180 298,176 300,171 301,167 303,164 304,160 306,156 308,153 309,150 311,147 313,145 314,143 316,141 318,139 319,137 321,136 323,135 324,134 326,134 328,133 329,133 331,134 333,134 334,135 336,136 337,137 339,139 341,141 342,143 344,145 346,147 347,150 349,153 351,156 352,159 354,163 356,166 357,170 359,174 361,178 362,182 364,187 366,191 367,196 369,200 370,205 372,210 374,214 375,219 377,224 379,229 380,234 382,238 384,243 385,248 387,253 389,258 390,262 392,267 394,272 395,276 397,281 399,285 400,289 402,294 403,298 405,302 407,306 408,310 410,314 412,318 413,321 415,325 417,329 418,332 420,335 422,339 423,342 425,345 427,348 428,351 430,354 432,357 433,360 435,362 436,365 438,367 440,370 441,372 443,375 445,377 446,379 448,381 450,383 451,386 453,388 455,390 456,391 458,393 460,395 461,397 463,399 465,400 466,402 468,404 469,405 471,407 473,408 474,410 476,411 478,413 479,414 481,415 483,417 484,418 486,419 488,420 489,421 491,423 493,424 494,425 496,426 498,427 499,428 501,429 502,429 504,430 506,431 507,432 509,433 511,433 512,434 514,435 516,435 517,436 519,437 521,437 522,438 524,438 526,439 527,439 529,440 531,440 532,441 534,441 535,442 537,442 539,442 540,443 542,443 544,444 545,444 547,444 549,445 550,445 552,446 554,446 555,446 557,447 559,447 560,448 562,448 564,448 565,449 567,449 568,450 570,450 572,451 573,451 575,452 577,452 578,452 580,453 582,453 583,454 585,454 587,455 588,455 590,456 592,456 593,457 595,457 597,458 598,458 600,459 601,459 603,460 605,460 606,461 608,461 610,462 611,462 613,463 615,463 616,464 618,464 620,464 621,465 623,465 625,466 626,466 628,466 630,467 631,467 633,467 634,468 636,468 638,468 639,468 641,469 643,469 644,469 646,469 648,470 649,470 651,470 653,470 654,470 656,470 658,471 659,471 661,471 663,471 664,471 666,471 667,471 669,471 671,471 672,471 674,472 676,472 677,472 679,472 681,472 682,472 684,472 686,472 687,472 689,472 691,472 692,472 694,472 696,472 697,471 699,471 700,471 702,471 704,471 705,471 707,471 709,471 710,471 712,471 714,471 715,471 717,470 719,470 720,470 722,470 724,470 725,470 727,469 729,469 730,469 732,469 733,469 735,468 737,468 738,468 740,468 742,468 743,467 745,467 747,467 748,467 750,466 752,466 753,466 755,466 757,466 758,465 760,465 762,465 763,465 765,464 766,464 768,464 770,464 771,464 773,463 775,463 776,463 778,463 780,463 781,463 783,463 785,462 786,462 788,462 790,462 791,462 793,462 795,462 796,462 798,462 799,462 801,462 803,462 804,462 806,462 808,462 809,462 811,463 813,463 814,463 816,463 818,463 819,463 821,463 823,464 824,464 826,464 828,464 829,464 831,465 832,465 834,465 836,465 837,466 839,466 841,466 842,466 844,467 846,467 847,467 849,467 851,467 852,468 854,468 856,468 857,468 859,469 861,469 862,469 864,469 866,469 867,469 869,470 870,470 872,470 874,470 875,470 877,470 879,471 880,471 882,471 884,471 885,471 887,471 889,471 890,471 892,471 894,472 895,472 897,472 899,472 900,472 902,472 903,472 905,472 907,472 908,472 910,472 912,472 913,472 915,472 917,472 918,472 920,472 922,472 923,472 925,472 927,472 928,472 930,472 932,472 932,472 108,472 "/>
<polyline fill="none" opacity="1" stroke="#E31A1C" stroke-width="2" points="367,473 367,118 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="2" points="358,473 358,173 "/>
<text x="869" y="235" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Base PDF
</text>
<text x="869" y="250" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
New PDF
</text>
<text x="869" y="265" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Base Mean
</text>
<text x="869" y="280" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
New Mean
</text>
<rect x="839" y="235" width="20" height="10" opacity="0.5" fill="#E31A1C" stroke="none"/>
<rect x="839" y="250" width="20" height="10" opacity="0.5" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#E31A1C" stroke-width="1" points="839,270 859,270 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="839,285 859,285 "/>
</svg>

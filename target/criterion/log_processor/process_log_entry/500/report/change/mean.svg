<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="438" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,438 86,438 "/>
<text x="77" y="380" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,380 86,380 "/>
<text x="77" y="322" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,322 86,322 "/>
<text x="77" y="263" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,263 86,263 "/>
<text x="77" y="205" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,205 86,205 "/>
<text x="77" y="147" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,147 86,147 "/>
<text x="77" y="89" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,89 86,89 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="178" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="178,473 178,478 "/>
<text x="312" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="312,473 312,478 "/>
<text x="445" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="445,473 445,478 "/>
<text x="579" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="579,473 579,478 "/>
<text x="713" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="713,473 713,478 "/>
<text x="847" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="847,473 847,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,470 93,469 95,468 97,467 98,466 100,466 102,465 103,464 105,463 107,462 109,461 110,460 112,459 114,458 115,457 117,455 119,454 120,453 122,452 124,451 125,449 127,448 129,447 131,446 132,444 134,443 136,441 137,440 139,439 141,437 142,436 144,434 146,433 147,431 149,430 151,428 153,427 154,425 156,423 158,422 159,420 161,418 163,416 164,415 166,413 168,411 169,409 171,407 173,405 175,403 176,401 178,399 180,397 181,395 183,393 185,390 186,388 188,386 190,384 191,382 193,380 195,378 197,375 198,373 200,371 202,369 203,366 205,364 207,362 208,360 210,357 212,355 214,353 215,350 217,348 219,345 220,343 222,341 224,338 225,336 227,333 229,331 230,328 232,326 234,323 236,321 237,318 239,316 241,313 242,311 244,308 246,305 247,303 249,300 251,297 252,295 254,292 256,289 258,286 259,284 261,281 263,278 264,275 266,273 268,270 269,267 271,265 273,262 274,259 276,257 278,254 280,251 281,249 283,246 285,244 286,241 288,238 290,236 291,233 293,231 295,228 296,226 298,223 300,220 302,218 303,215 305,212 307,210 308,207 310,205 312,202 313,199 315,197 317,194 318,192 320,189 322,187 324,184 325,182 327,180 329,177 330,175 332,173 334,171 335,169 337,167 339,164 341,162 342,160 344,158 346,156 347,154 349,151 351,149 352,147 354,145 356,142 357,140 359,138 361,135 363,133 364,130 366,128 368,126 369,123 371,121 373,119 374,116 376,114 378,112 379,110 381,108 383,106 385,104 386,102 388,100 390,98 391,97 393,95 395,94 396,92 398,91 400,89 401,88 403,87 405,85 407,84 408,83 410,82 412,80 413,79 415,78 417,76 418,75 420,74 422,73 423,71 425,70 427,69 429,68 430,66 432,65 434,64 435,63 437,62 439,61 440,60 442,59 444,58 445,58 447,57 449,56 451,56 452,55 454,55 456,54 457,54 459,54 461,54 462,54 464,54 466,54 468,53 469,54 471,54 473,54 474,54 476,54 478,54 479,54 481,54 483,54 484,54 486,54 488,55 490,55 491,55 493,55 495,55 496,55 498,55 500,55 501,56 503,56 505,56 506,56 508,57 510,57 512,58 513,58 515,59 517,60 518,61 520,62 522,63 523,64 525,65 527,66 528,67 530,69 532,70 534,72 535,73 537,75 539,76 540,78 542,80 544,81 545,83 547,85 549,86 550,88 552,90 554,92 556,94 557,96 559,98 561,100 562,102 564,104 566,106 567,108 569,111 571,113 573,115 574,118 576,120 578,123 579,125 581,128 583,130 584,133 586,136 588,138 589,141 591,144 593,146 595,149 596,151 598,154 600,157 601,159 603,162 605,165 606,167 608,170 610,172 611,175 613,178 615,180 617,183 618,186 620,188 622,191 623,194 625,196 627,199 628,202 630,205 632,207 633,210 635,213 637,216 639,218 640,221 642,224 644,226 645,229 647,232 649,234 650,237 652,239 654,242 655,244 657,247 659,249 661,252 662,254 664,256 666,259 667,261 669,263 671,266 672,268 674,271 676,273 677,275 679,277 681,280 683,282 684,284 686,287 688,289 689,291 691,293 693,296 694,298 696,300 698,303 700,305 701,307 703,309 705,312 706,314 708,316 710,318 711,320 713,323 715,325 716,327 718,329 720,331 722,333 723,335 725,337 727,339 728,341 730,343 732,345 733,347 735,349 737,351 738,353 740,355 742,357 744,359 745,361 747,362 749,364 750,366 752,368 754,370 755,372 757,374 759,376 760,378 762,379 764,381 766,383 767,384 769,386 771,388 772,389 774,391 776,392 777,394 779,395 781,397 782,398 784,399 786,401 788,402 789,403 791,404 793,406 794,407 796,408 798,409 799,410 801,412 803,413 804,414 806,415 808,416 810,418 811,419 813,420 815,422 816,423 818,424 820,425 821,427 823,428 825,429 827,431 828,432 830,433 832,435 833,436 835,437 837,438 838,439 840,440 842,442 843,443 845,444 847,444 849,445 850,446 852,447 854,448 855,449 857,450 859,450 860,451 862,452 864,452 865,453 867,454 869,454 871,455 872,456 874,456 876,457 877,458 879,458 881,459 882,460 884,460 886,461 887,461 889,462 891,463 893,463 894,464 896,464 898,465 899,465 901,466 903,466 904,467 906,467 908,468 909,468 911,468 913,469 915,469 916,470 918,470 920,470 921,471 923,471 925,471 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,415 166,413 168,411 169,409 171,407 173,405 175,403 176,401 178,399 180,397 181,395 183,393 185,390 186,388 188,386 190,384 191,382 193,380 195,378 197,375 198,373 200,371 202,369 203,366 205,364 207,362 208,360 210,357 212,355 214,353 215,350 217,348 219,345 220,343 222,341 224,338 225,336 227,333 229,331 230,328 232,326 234,323 236,321 237,318 239,316 241,313 242,311 244,308 246,305 247,303 249,300 251,297 252,295 254,292 256,289 258,286 259,284 261,281 263,278 264,275 266,273 268,270 269,267 271,265 273,262 274,259 276,257 278,254 280,251 281,249 283,246 285,244 286,241 288,238 290,236 291,233 293,231 295,228 296,226 298,223 300,220 302,218 303,215 305,212 307,210 308,207 310,205 312,202 313,199 315,197 317,194 318,192 320,189 322,187 324,184 325,182 327,180 329,177 330,175 332,173 334,171 335,169 337,167 339,164 341,162 342,160 344,158 346,156 347,154 349,151 351,149 352,147 354,145 356,142 357,140 359,138 361,135 363,133 364,130 366,128 368,126 369,123 371,121 373,119 374,116 376,114 378,112 379,110 381,108 383,106 385,104 386,102 388,100 390,98 391,97 393,95 395,94 396,92 398,91 400,89 401,88 403,87 405,85 407,84 408,83 410,82 412,80 413,79 415,78 417,76 418,75 420,74 422,73 423,71 425,70 427,69 429,68 430,66 432,65 434,64 435,63 437,62 439,61 440,60 442,59 444,58 445,58 447,57 449,56 451,56 452,55 454,55 456,54 457,54 459,54 461,54 462,54 464,54 466,54 468,53 469,54 471,54 473,54 474,54 476,54 478,54 479,54 481,54 483,54 484,54 486,54 488,55 490,55 491,55 493,55 495,55 496,55 498,55 500,55 501,56 503,56 505,56 506,56 508,57 510,57 512,58 513,58 515,59 517,60 518,61 520,62 522,63 523,64 525,65 527,66 528,67 530,69 532,70 534,72 535,73 537,75 539,76 540,78 542,80 544,81 545,83 547,85 549,86 550,88 552,90 554,92 556,94 557,96 559,98 561,100 562,102 564,104 566,106 567,108 569,111 571,113 573,115 574,118 576,120 578,123 579,125 581,128 583,130 584,133 586,136 588,138 589,141 591,144 593,146 595,149 596,151 598,154 600,157 601,159 603,162 605,165 606,167 608,170 610,172 611,175 613,178 615,180 617,183 618,186 620,188 622,191 623,194 625,196 627,199 628,202 630,205 632,207 633,210 635,213 637,216 639,218 640,221 642,224 644,226 645,229 647,232 649,234 650,237 652,239 654,242 655,244 657,247 659,249 661,252 662,254 664,256 666,259 667,261 669,263 671,266 672,268 674,271 676,273 677,275 679,277 681,280 683,282 684,284 686,287 688,289 689,291 691,293 693,296 694,298 696,300 698,303 700,305 701,307 703,309 705,312 706,314 708,316 710,318 711,320 713,323 715,325 716,327 718,329 720,331 722,333 723,335 725,337 727,339 728,341 730,343 732,345 733,347 735,349 737,351 738,353 740,355 742,357 744,359 745,361 747,362 749,364 750,366 752,368 754,370 755,372 757,374 759,376 760,378 762,379 764,381 766,383 767,384 769,386 771,388 772,389 774,391 776,392 777,394 779,395 781,397 782,398 784,399 786,401 788,402 789,403 791,404 793,406 794,407 796,408 798,409 799,410 801,412 803,413 804,414 806,415 808,416 810,418 811,419 813,420 815,422 816,423 818,424 820,425 821,427 823,428 825,429 827,431 828,432 830,433 832,435 833,436 835,437 837,438 838,439 840,440 842,442 843,443 845,444 847,444 849,445 850,446 852,447 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="475,473 475,54 "/>
<rect x="87" y="53" width="845" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="406" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,406 86,406 "/>
<text x="77" y="316" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,316 86,316 "/>
<text x="77" y="226" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,226 86,226 "/>
<text x="77" y="135" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,135 86,135 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="111" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="111,473 111,478 "/>
<text x="217" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="217,473 217,478 "/>
<text x="323" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="323,473 323,478 "/>
<text x="428" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.003
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="428,473 428,478 "/>
<text x="534" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="534,473 534,478 "/>
<text x="640" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.001
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="640,473 640,478 "/>
<text x="746" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="746,473 746,478 "/>
<text x="852" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.001
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="852,473 852,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,469 93,468 95,467 97,466 98,465 100,463 102,462 103,461 105,460 107,459 109,457 110,456 112,455 114,454 115,453 117,452 119,451 120,450 122,448 124,447 125,446 127,445 129,444 131,442 132,441 134,439 136,438 137,436 139,434 141,432 142,429 144,427 146,424 147,422 149,419 151,416 153,413 154,410 156,407 158,404 159,401 161,398 163,396 164,393 166,390 168,388 169,385 171,383 173,381 175,379 176,377 178,376 180,374 181,372 183,371 185,370 186,368 188,367 190,365 191,364 193,362 195,361 197,359 198,358 200,356 202,354 203,352 205,350 207,348 208,346 210,344 212,341 214,339 215,337 217,335 219,333 220,330 222,328 224,326 225,324 227,322 229,321 230,319 232,317 234,316 236,314 237,313 239,311 241,310 242,309 244,308 246,306 247,305 249,304 251,303 252,301 254,300 256,299 258,297 259,296 261,294 263,292 264,290 266,288 268,286 269,284 271,282 273,280 274,277 276,275 278,272 280,270 281,267 283,264 285,261 286,258 288,256 290,253 291,250 293,247 295,244 296,241 298,238 300,235 302,231 303,228 305,225 307,222 308,219 310,216 312,213 313,210 315,207 317,204 318,201 320,198 322,195 324,192 325,189 327,187 329,184 330,182 332,179 334,177 335,174 337,172 339,170 341,167 342,165 344,163 346,160 347,158 349,155 351,152 352,149 354,146 356,143 357,140 359,137 361,134 363,130 364,127 366,124 368,120 369,117 371,113 373,110 374,107 376,104 378,101 379,98 381,95 383,92 385,90 386,87 388,85 390,83 391,80 393,78 395,76 396,74 398,72 400,70 401,68 403,67 405,65 407,63 408,62 410,60 412,59 413,58 415,57 417,56 418,55 420,55 422,54 423,54 425,54 427,54 429,53 430,54 432,54 434,54 435,54 437,55 439,55 440,56 442,56 444,57 445,57 447,58 449,59 451,60 452,61 454,62 456,63 457,64 459,65 461,67 462,68 464,70 466,72 468,73 469,75 471,77 473,80 474,82 476,84 478,86 479,89 481,91 483,94 484,96 486,99 488,101 490,103 491,106 493,108 495,110 496,113 498,115 500,117 501,119 503,121 505,122 506,124 508,125 510,127 512,128 513,129 515,130 517,131 518,132 520,133 522,134 523,135 525,135 527,136 528,137 530,138 532,139 534,140 535,141 537,142 539,143 540,144 542,145 544,146 545,148 547,149 549,150 550,152 552,153 554,154 556,156 557,157 559,158 561,159 562,161 564,162 566,163 567,164 569,165 571,165 573,166 574,167 576,168 578,169 579,169 581,170 583,171 584,171 586,172 588,173 589,174 591,174 593,175 595,176 596,177 598,177 600,178 601,179 603,179 605,180 606,181 608,181 610,182 611,183 613,183 615,184 617,184 618,185 620,186 622,186 623,187 625,188 627,189 628,190 630,191 632,192 633,193 635,195 637,196 639,198 640,199 642,201 644,202 645,204 647,206 649,208 650,209 652,211 654,213 655,215 657,217 659,219 661,221 662,223 664,225 666,227 667,229 669,232 671,234 672,236 674,239 676,241 677,244 679,247 681,249 683,252 684,255 686,258 688,261 689,264 691,267 693,270 694,273 696,276 698,279 700,283 701,286 703,289 705,292 706,295 708,298 710,301 711,304 713,307 715,310 716,313 718,316 720,319 722,322 723,325 725,327 727,330 728,333 730,336 732,338 733,341 735,344 737,346 738,349 740,351 742,354 744,356 745,359 747,361 749,364 750,366 752,368 754,370 755,373 757,375 759,377 760,379 762,381 764,383 766,385 767,387 769,389 771,391 772,392 774,394 776,396 777,398 779,400 781,401 782,403 784,405 786,406 788,408 789,410 791,411 793,413 794,415 796,416 798,418 799,419 801,420 803,422 804,423 806,425 808,426 810,427 811,428 813,429 815,430 816,432 818,433 820,433 821,434 823,435 825,436 827,437 828,438 830,438 832,439 833,440 835,440 837,441 838,442 840,442 842,443 843,443 845,444 847,444 849,445 850,445 852,445 854,446 855,446 857,447 859,447 860,447 862,448 864,448 865,448 867,449 869,449 871,449 872,450 874,450 876,451 877,451 879,451 881,452 882,452 884,453 886,453 887,454 889,454 891,455 893,455 894,456 896,456 898,457 899,457 901,458 903,458 904,459 906,459 908,460 909,461 911,461 913,462 915,462 916,463 918,463 920,464 921,464 923,465 925,466 926,466 928,467 930,468 932,468 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,393 166,390 168,388 169,385 171,383 173,381 175,379 176,377 178,376 180,374 181,372 183,371 185,370 186,368 188,367 190,365 191,364 193,362 195,361 197,359 198,358 200,356 202,354 203,352 205,350 207,348 208,346 210,344 212,341 214,339 215,337 217,335 219,333 220,330 222,328 224,326 225,324 227,322 229,321 230,319 232,317 234,316 236,314 237,313 239,311 241,310 242,309 244,308 246,306 247,305 249,304 251,303 252,301 254,300 256,299 258,297 259,296 261,294 263,292 264,290 266,288 268,286 269,284 271,282 273,280 274,277 276,275 278,272 280,270 281,267 283,264 285,261 286,258 288,256 290,253 291,250 293,247 295,244 296,241 298,238 300,235 302,231 303,228 305,225 307,222 308,219 310,216 312,213 313,210 315,207 317,204 318,201 320,198 322,195 324,192 325,189 327,187 329,184 330,182 332,179 334,177 335,174 337,172 339,170 341,167 342,165 344,163 346,160 347,158 349,155 351,152 352,149 354,146 356,143 357,140 359,137 361,134 363,130 364,127 366,124 368,120 369,117 371,113 373,110 374,107 376,104 378,101 379,98 381,95 383,92 385,90 386,87 388,85 390,83 391,80 393,78 395,76 396,74 398,72 400,70 401,68 403,67 405,65 407,63 408,62 410,60 412,59 413,58 415,57 417,56 418,55 420,55 422,54 423,54 425,54 427,54 429,53 430,54 432,54 434,54 435,54 437,55 439,55 440,56 442,56 444,57 445,57 447,58 449,59 451,60 452,61 454,62 456,63 457,64 459,65 461,67 462,68 464,70 466,72 468,73 469,75 471,77 473,80 474,82 476,84 478,86 479,89 481,91 483,94 484,96 486,99 488,101 490,103 491,106 493,108 495,110 496,113 498,115 500,117 501,119 503,121 505,122 506,124 508,125 510,127 512,128 513,129 515,130 517,131 518,132 520,133 522,134 523,135 525,135 527,136 528,137 530,138 532,139 534,140 535,141 537,142 539,143 540,144 542,145 544,146 545,148 547,149 549,150 550,152 552,153 554,154 556,156 557,157 559,158 561,159 562,161 564,162 566,163 567,164 569,165 571,165 573,166 574,167 576,168 578,169 579,169 581,170 583,171 584,171 586,172 588,173 589,174 591,174 593,175 595,176 596,177 598,177 600,178 601,179 603,179 605,180 606,181 608,181 610,182 611,183 613,183 615,184 617,184 618,185 620,186 622,186 623,187 625,188 627,189 628,190 630,191 632,192 633,193 635,195 637,196 639,198 640,199 642,201 644,202 645,204 647,206 649,208 650,209 652,211 654,213 655,215 657,217 659,219 661,221 662,223 664,225 666,227 667,229 669,232 671,234 672,236 674,239 676,241 677,244 679,247 681,249 683,252 684,255 686,258 688,261 689,264 691,267 693,270 694,273 696,276 698,279 700,283 701,286 703,289 705,292 706,295 708,298 710,301 711,304 713,307 715,310 716,313 718,316 720,319 722,322 723,325 725,327 727,330 728,333 730,336 732,338 733,341 735,344 737,346 738,349 740,351 742,354 744,356 745,359 747,361 749,364 750,366 752,368 754,370 755,373 757,375 759,377 760,379 762,381 764,383 766,385 767,387 769,389 771,391 772,392 774,394 776,396 777,398 779,400 781,401 782,403 784,405 786,406 788,408 789,410 791,411 793,413 794,415 796,416 798,418 799,419 801,420 803,422 804,423 806,425 808,426 810,427 811,428 813,429 815,430 816,432 818,433 820,433 821,434 823,435 825,436 827,437 828,438 830,438 832,439 833,440 835,440 837,441 838,442 840,442 842,443 843,443 845,444 847,444 849,445 850,445 852,445 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="459,473 459,65 "/>
<rect x="87" y="53" width="845" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

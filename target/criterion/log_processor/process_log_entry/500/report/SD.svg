<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="422" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,422 86,422 "/>
<text x="77" y="353" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,353 86,353 "/>
<text x="77" y="284" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,284 86,284 "/>
<text x="77" y="215" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,215 86,215 "/>
<text x="77" y="146" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,146 86,146 "/>
<text x="77" y="77" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,77 86,77 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="139" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="139,473 139,478 "/>
<text x="250" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="250,473 250,478 "/>
<text x="361" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="361,473 361,478 "/>
<text x="472" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="472,473 472,478 "/>
<text x="583" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="583,473 583,478 "/>
<text x="694" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
90
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="694,473 694,478 "/>
<text x="805" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="805,473 805,478 "/>
<text x="916" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
110
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="916,473 916,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,470 95,469 97,468 98,467 100,466 102,465 103,464 105,463 107,462 109,461 110,460 112,459 114,458 115,456 117,455 119,454 120,452 122,451 124,449 125,448 127,446 129,445 131,443 132,442 134,440 136,438 137,436 139,435 141,433 142,431 144,429 146,427 147,425 149,423 151,421 153,418 154,416 156,414 158,412 159,409 161,407 163,404 164,402 166,399 168,397 169,394 171,392 173,389 175,386 176,384 178,381 180,378 181,375 183,372 185,370 186,367 188,364 190,361 191,358 193,356 195,353 197,350 198,347 200,344 202,342 203,339 205,336 207,333 208,331 210,328 212,325 214,323 215,320 217,317 219,315 220,312 222,309 224,307 225,304 227,301 229,299 230,296 232,294 234,291 236,288 237,286 239,283 241,281 242,278 244,276 246,274 247,271 249,269 251,267 252,265 254,263 256,261 258,259 259,257 261,255 263,253 264,252 266,250 268,249 269,247 271,246 273,245 274,244 276,243 278,242 280,241 281,240 283,240 285,239 286,239 288,239 290,239 291,239 293,239 295,239 296,239 298,239 300,240 302,240 303,241 305,242 307,242 308,243 310,244 312,245 313,246 315,247 317,249 318,250 320,251 322,253 324,254 325,256 327,257 329,259 330,261 332,262 334,264 335,266 337,268 339,269 341,271 342,273 344,275 346,277 347,279 349,281 351,283 352,285 354,286 356,288 357,290 359,292 361,294 363,295 364,297 366,299 368,300 369,302 371,303 373,305 374,306 376,307 378,308 379,309 381,310 383,311 385,312 386,312 388,313 390,313 391,313 393,313 395,313 396,313 398,313 400,312 401,311 403,310 405,309 407,308 408,306 410,305 412,303 413,301 415,299 417,296 418,294 420,291 422,288 423,285 425,282 427,278 429,274 430,271 432,267 434,263 435,258 437,254 439,250 440,245 442,240 444,236 445,231 447,226 449,221 451,216 452,211 454,206 456,201 457,196 459,191 461,186 462,181 464,176 466,171 468,166 469,162 471,157 473,152 474,148 476,144 478,140 479,135 481,132 483,128 484,124 486,121 488,117 490,114 491,111 493,109 495,106 496,104 498,102 500,100 501,98 503,97 505,95 506,94 508,94 510,93 512,93 513,93 515,94 517,95 518,96 520,97 522,99 523,101 525,103 527,106 528,109 530,112 532,116 534,120 535,124 537,129 539,133 540,138 542,143 544,149 545,154 547,160 549,166 550,172 552,178 554,184 556,191 557,197 559,203 561,209 562,216 564,222 566,228 567,234 569,239 571,245 573,250 574,255 576,260 578,265 579,270 581,274 583,278 584,282 586,285 588,289 589,292 591,294 593,297 595,299 596,300 598,302 600,303 601,304 603,305 605,305 606,305 608,305 610,305 611,304 613,303 615,302 617,300 618,299 620,297 622,295 623,293 625,290 627,288 628,285 630,282 632,279 633,276 635,273 637,270 639,267 640,264 642,261 644,258 645,256 647,253 649,250 650,248 652,246 654,243 655,241 657,240 659,238 661,237 662,236 664,235 666,234 667,234 669,234 671,234 672,235 674,236 676,237 677,238 679,240 681,242 683,244 684,246 686,249 688,252 689,255 691,258 693,261 694,265 696,269 698,273 700,277 701,281 703,285 705,290 706,294 708,299 710,303 711,308 713,313 715,317 716,322 718,327 720,331 722,336 723,340 725,344 727,349 728,353 730,357 732,360 733,364 735,368 737,371 738,374 740,377 742,380 744,383 745,385 747,388 749,390 750,392 752,393 754,395 755,396 757,397 759,398 760,399 762,400 764,400 766,400 767,400 769,400 771,400 772,400 774,400 776,399 777,399 779,398 781,397 782,397 784,396 786,395 788,395 789,394 791,393 793,393 794,392 796,392 798,391 799,391 801,391 803,391 804,391 806,391 808,392 810,392 811,393 813,393 815,394 816,395 818,396 820,397 821,399 823,400 825,402 827,403 828,405 830,407 832,408 833,410 835,412 837,414 838,416 840,418 842,420 843,423 845,425 847,427 849,429 850,431 852,433 854,435 855,437 857,439 859,441 860,443 862,445 864,446 865,448 867,449 869,451 871,452 872,453 874,454 876,455 877,456 879,457 881,458 882,459 884,459 886,460 887,460 889,461 891,461 893,461 894,461 896,462 898,462 899,462 901,462 903,462 904,462 906,462 908,462 909,462 911,462 913,462 915,462 916,462 918,462 920,462 921,462 923,463 925,463 926,463 928,463 930,464 932,464 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,402 166,399 168,397 169,394 171,392 173,389 175,386 176,384 178,381 180,378 181,375 183,372 185,370 186,367 188,364 190,361 191,358 193,356 195,353 197,350 198,347 200,344 202,342 203,339 205,336 207,333 208,331 210,328 212,325 214,323 215,320 217,317 219,315 220,312 222,309 224,307 225,304 227,301 229,299 230,296 232,294 234,291 236,288 237,286 239,283 241,281 242,278 244,276 246,274 247,271 249,269 251,267 252,265 254,263 256,261 258,259 259,257 261,255 263,253 264,252 266,250 268,249 269,247 271,246 273,245 274,244 276,243 278,242 280,241 281,240 283,240 285,239 286,239 288,239 290,239 291,239 293,239 295,239 296,239 298,239 300,240 302,240 303,241 305,242 307,242 308,243 310,244 312,245 313,246 315,247 317,249 318,250 320,251 322,253 324,254 325,256 327,257 329,259 330,261 332,262 334,264 335,266 337,268 339,269 341,271 342,273 344,275 346,277 347,279 349,281 351,283 352,285 354,286 356,288 357,290 359,292 361,294 363,295 364,297 366,299 368,300 369,302 371,303 373,305 374,306 376,307 378,308 379,309 381,310 383,311 385,312 386,312 388,313 390,313 391,313 393,313 395,313 396,313 398,313 400,312 401,311 403,310 405,309 407,308 408,306 410,305 412,303 413,301 415,299 417,296 418,294 420,291 422,288 423,285 425,282 427,278 429,274 430,271 432,267 434,263 435,258 437,254 439,250 440,245 442,240 444,236 445,231 447,226 449,221 451,216 452,211 454,206 456,201 457,196 459,191 461,186 462,181 464,176 466,171 468,166 469,162 471,157 473,152 474,148 476,144 478,140 479,135 481,132 483,128 484,124 486,121 488,117 490,114 491,111 493,109 495,106 496,104 498,102 500,100 501,98 503,97 505,95 506,94 508,94 510,93 512,93 513,93 515,94 517,95 518,96 520,97 522,99 523,101 525,103 527,106 528,109 530,112 532,116 534,120 535,124 537,129 539,133 540,138 542,143 544,149 545,154 547,160 549,166 550,172 552,178 554,184 556,191 557,197 559,203 561,209 562,216 564,222 566,228 567,234 569,239 571,245 573,250 574,255 576,260 578,265 579,270 581,274 583,278 584,282 586,285 588,289 589,292 591,294 593,297 595,299 596,300 598,302 600,303 601,304 603,305 605,305 606,305 608,305 610,305 611,304 613,303 615,302 617,300 618,299 620,297 622,295 623,293 625,290 627,288 628,285 630,282 632,279 633,276 635,273 637,270 639,267 640,264 642,261 644,258 645,256 647,253 649,250 650,248 652,246 654,243 655,241 657,240 659,238 661,237 662,236 664,235 666,234 667,234 669,234 671,234 672,235 674,236 676,237 677,238 679,240 681,242 683,244 684,246 686,249 688,252 689,255 691,258 693,261 694,265 696,269 698,273 700,277 701,281 703,285 705,290 706,294 708,299 710,303 711,308 713,313 715,317 716,322 718,327 720,331 722,336 723,340 725,344 727,349 728,353 730,357 732,360 733,364 735,368 737,371 738,374 740,377 742,380 744,383 745,385 747,388 749,390 750,392 752,393 754,395 755,396 757,397 759,398 760,399 762,400 764,400 766,400 767,400 769,400 771,400 772,400 774,400 776,399 777,399 779,398 781,397 782,397 784,396 786,395 788,395 789,394 791,393 793,393 794,392 796,392 798,391 799,391 801,391 803,391 804,391 806,391 808,392 810,392 811,393 813,393 815,394 816,395 818,396 820,397 821,399 823,400 825,402 827,403 828,405 830,407 832,408 833,410 835,412 837,414 838,416 840,418 842,420 843,423 845,425 847,427 849,429 850,431 852,433 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="519,473 519,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

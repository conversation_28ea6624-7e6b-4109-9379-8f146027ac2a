<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:typical
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="452" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,452 86,452 "/>
<text x="77" y="410" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,410 86,410 "/>
<text x="77" y="367" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,367 86,367 "/>
<text x="77" y="325" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,325 86,325 "/>
<text x="77" y="282" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,282 86,282 "/>
<text x="77" y="239" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,239 86,239 "/>
<text x="77" y="197" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,197 86,197 "/>
<text x="77" y="154" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,154 86,154 "/>
<text x="77" y="112" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,112 86,112 "/>
<text x="77" y="69" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,69 86,69 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="149" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.53
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="149,473 149,478 "/>
<text x="253" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.535
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="253,473 253,478 "/>
<text x="357" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.54
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="357,473 357,478 "/>
<text x="461" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.545
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="461,473 461,478 "/>
<text x="564" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.55
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="564,473 564,478 "/>
<text x="668" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.555
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="668,473 668,478 "/>
<text x="772" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.56
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="772,473 772,478 "/>
<text x="875" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.565
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="875,473 875,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,470 88,470 90,469 92,468 93,467 95,467 97,466 98,465 100,464 102,464 103,463 105,462 107,461 109,460 110,459 112,458 114,458 115,457 117,456 119,455 120,454 122,453 124,452 125,451 127,450 129,449 131,448 132,447 134,446 136,445 137,444 139,443 141,442 142,440 144,439 146,438 147,436 149,435 151,434 153,432 154,431 156,429 158,427 159,426 161,424 163,422 164,420 166,418 168,417 169,415 171,413 173,411 175,408 176,406 178,404 180,402 181,400 183,398 185,396 186,394 188,391 190,389 191,387 193,385 195,383 197,381 198,378 200,376 202,374 203,372 205,369 207,367 208,364 210,362 212,360 214,357 215,355 217,352 219,350 220,347 222,345 224,342 225,339 227,337 229,334 230,332 232,329 234,326 236,324 237,321 239,319 241,316 242,313 244,311 246,308 247,306 249,303 251,301 252,298 254,296 256,293 258,291 259,288 261,286 263,283 264,281 266,278 268,276 269,273 271,270 273,268 274,265 276,263 278,260 280,257 281,255 283,252 285,249 286,247 288,244 290,242 291,239 293,237 295,234 296,232 298,230 300,227 302,225 303,223 305,220 307,218 308,216 310,214 312,212 313,209 315,207 317,205 318,203 320,201 322,199 324,196 325,194 327,192 329,190 330,188 332,186 334,184 335,182 337,180 339,178 341,176 342,174 344,172 346,170 347,168 349,166 351,164 352,163 354,161 356,159 357,157 359,155 361,154 363,152 364,150 366,148 368,146 369,144 371,143 373,141 374,139 376,137 378,135 379,134 381,132 383,130 385,128 386,126 388,125 390,123 391,121 393,120 395,118 396,116 398,115 400,113 401,112 403,110 405,109 407,108 408,106 410,105 412,104 413,103 415,102 417,101 418,100 420,99 422,98 423,97 425,96 427,96 429,95 430,95 432,95 434,94 435,94 437,94 439,94 440,94 442,94 444,94 445,94 447,94 449,94 451,94 452,94 454,95 456,95 457,95 459,95 461,96 462,96 464,96 466,97 468,97 469,97 471,97 473,98 474,98 476,98 478,98 479,99 481,99 483,99 484,100 486,100 488,100 490,101 491,101 493,102 495,103 496,103 498,104 500,105 501,105 503,106 505,107 506,108 508,109 510,111 512,112 513,113 515,115 517,116 518,118 520,119 522,121 523,122 525,124 527,125 528,127 530,129 532,130 534,132 535,134 537,136 539,137 540,139 542,141 544,143 545,144 547,146 549,148 550,150 552,151 554,153 556,155 557,157 559,158 561,160 562,162 564,164 566,165 567,167 569,169 571,171 573,172 574,174 576,176 578,177 579,179 581,181 583,182 584,184 586,186 588,188 589,189 591,191 593,193 595,195 596,197 598,199 600,201 601,203 603,205 605,207 606,209 608,211 610,213 611,215 613,217 615,219 617,222 618,224 620,226 622,228 623,230 625,233 627,235 628,237 630,239 632,242 633,244 635,246 637,249 639,251 640,253 642,255 644,258 645,260 647,262 649,264 650,267 652,269 654,271 655,274 657,276 659,278 661,280 662,283 664,285 666,287 667,289 669,291 671,294 672,296 674,298 676,300 677,302 679,304 681,306 683,308 684,310 686,312 688,314 689,316 691,319 693,321 694,323 696,325 698,327 700,329 701,331 703,333 705,335 706,337 708,339 710,341 711,343 713,345 715,346 716,348 718,350 720,352 722,354 723,356 725,357 727,359 728,361 730,362 732,364 733,366 735,367 737,369 738,371 740,372 742,374 744,376 745,377 747,379 749,380 750,382 752,384 754,385 755,387 757,388 759,389 760,391 762,392 764,394 766,395 767,396 769,398 771,399 772,400 774,401 776,403 777,404 779,405 781,406 782,407 784,408 786,409 788,410 789,412 791,413 793,414 794,415 796,416 798,417 799,418 801,419 803,420 804,421 806,422 808,423 810,424 811,426 813,427 815,428 816,429 818,430 820,431 821,432 823,433 825,434 827,435 828,436 830,437 832,437 833,438 835,439 837,440 838,441 840,442 842,442 843,443 845,444 847,445 849,445 850,446 852,446 854,447 855,448 857,448 859,449 860,449 862,450 864,450 865,451 867,451 869,452 871,452 872,453 874,453 876,454 877,454 879,454 881,455 882,455 884,456 886,457 887,457 889,458 891,458 893,459 894,459 896,460 898,460 899,461 901,462 903,462 904,463 906,463 908,464 909,465 911,465 913,466 915,466 916,467 918,468 920,468 921,469 923,469 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,420 166,418 168,417 169,415 171,413 173,411 175,408 176,406 178,404 180,402 181,400 183,398 185,396 186,394 188,391 190,389 191,387 193,385 195,383 197,381 198,378 200,376 202,374 203,372 205,369 207,367 208,364 210,362 212,360 214,357 215,355 217,352 219,350 220,347 222,345 224,342 225,339 227,337 229,334 230,332 232,329 234,326 236,324 237,321 239,319 241,316 242,313 244,311 246,308 247,306 249,303 251,301 252,298 254,296 256,293 258,291 259,288 261,286 263,283 264,281 266,278 268,276 269,273 271,270 273,268 274,265 276,263 278,260 280,257 281,255 283,252 285,249 286,247 288,244 290,242 291,239 293,237 295,234 296,232 298,230 300,227 302,225 303,223 305,220 307,218 308,216 310,214 312,212 313,209 315,207 317,205 318,203 320,201 322,199 324,196 325,194 327,192 329,190 330,188 332,186 334,184 335,182 337,180 339,178 341,176 342,174 344,172 346,170 347,168 349,166 351,164 352,163 354,161 356,159 357,157 359,155 361,154 363,152 364,150 366,148 368,146 369,144 371,143 373,141 374,139 376,137 378,135 379,134 381,132 383,130 385,128 386,126 388,125 390,123 391,121 393,120 395,118 396,116 398,115 400,113 401,112 403,110 405,109 407,108 408,106 410,105 412,104 413,103 415,102 417,101 418,100 420,99 422,98 423,97 425,96 427,96 429,95 430,95 432,95 434,94 435,94 437,94 439,94 440,94 442,94 444,94 445,94 447,94 449,94 451,94 452,94 454,95 456,95 457,95 459,95 461,96 462,96 464,96 466,97 468,97 469,97 471,97 473,98 474,98 476,98 478,98 479,99 481,99 483,99 484,100 486,100 488,100 490,101 491,101 493,102 495,103 496,103 498,104 500,105 501,105 503,106 505,107 506,108 508,109 510,111 512,112 513,113 515,115 517,116 518,118 520,119 522,121 523,122 525,124 527,125 528,127 530,129 532,130 534,132 535,134 537,136 539,137 540,139 542,141 544,143 545,144 547,146 549,148 550,150 552,151 554,153 556,155 557,157 559,158 561,160 562,162 564,164 566,165 567,167 569,169 571,171 573,172 574,174 576,176 578,177 579,179 581,181 583,182 584,184 586,186 588,188 589,189 591,191 593,193 595,195 596,197 598,199 600,201 601,203 603,205 605,207 606,209 608,211 610,213 611,215 613,217 615,219 617,222 618,224 620,226 622,228 623,230 625,233 627,235 628,237 630,239 632,242 633,244 635,246 637,249 639,251 640,253 642,255 644,258 645,260 647,262 649,264 650,267 652,269 654,271 655,274 657,276 659,278 661,280 662,283 664,285 666,287 667,289 669,291 671,294 672,296 674,298 676,300 677,302 679,304 681,306 683,308 684,310 686,312 688,314 689,316 691,319 693,321 694,323 696,325 698,327 700,329 701,331 703,333 705,335 706,337 708,339 710,341 711,343 713,345 715,346 716,348 718,350 720,352 722,354 723,356 725,357 727,359 728,361 730,362 732,364 733,366 735,367 737,369 738,371 740,372 742,374 744,376 745,377 747,379 749,380 750,382 752,384 754,385 755,387 757,388 759,389 760,391 762,392 764,394 766,395 767,396 769,398 771,399 772,400 774,401 776,403 777,404 779,405 781,406 782,407 784,408 786,409 788,410 789,412 791,413 793,414 794,415 796,416 798,417 799,418 801,419 803,420 804,421 806,422 808,423 810,424 811,426 813,427 815,428 816,429 818,430 820,431 821,432 823,433 825,434 827,435 828,436 830,437 832,437 833,438 835,439 837,440 838,441 840,442 842,442 843,443 845,444 847,445 849,445 850,446 852,446 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="477,473 477,98 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

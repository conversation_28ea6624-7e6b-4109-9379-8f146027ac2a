<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (µs)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="450" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,450 86,450 "/>
<text x="77" y="410" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,410 86,410 "/>
<text x="77" y="370" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,370 86,370 "/>
<text x="77" y="330" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,330 86,330 "/>
<text x="77" y="290" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,290 86,290 "/>
<text x="77" y="250" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,250 86,250 "/>
<text x="77" y="210" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,210 86,210 "/>
<text x="77" y="170" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,170 86,170 "/>
<text x="77" y="130" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
90
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,130 86,130 "/>
<text x="77" y="90" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,90 86,90 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="138" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.52
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="138,473 138,478 "/>
<text x="288" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.525
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="288,473 288,478 "/>
<text x="438" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.53
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="438,473 438,478 "/>
<text x="588" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.535
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="588,473 588,478 "/>
<text x="738" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.54
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="738,473 738,478 "/>
<text x="887" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.545
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="887,473 887,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,467 88,467 90,466 92,465 93,464 95,464 97,463 98,462 100,462 102,461 103,461 105,460 107,460 109,460 110,459 112,459 114,459 115,459 117,458 119,458 120,458 122,458 124,458 125,458 127,458 129,458 131,457 132,457 134,457 136,457 137,457 139,457 141,457 142,456 144,456 146,456 147,456 149,456 151,456 153,456 154,456 156,456 158,456 159,456 161,456 163,456 164,456 166,457 168,457 169,457 171,457 173,458 175,458 176,458 178,458 180,458 181,458 183,458 185,458 186,457 188,456 190,455 191,454 193,453 195,452 197,451 198,449 200,448 202,446 203,444 205,443 207,441 208,440 210,439 212,438 214,437 215,437 217,437 219,437 220,437 222,438 224,438 225,439 227,440 229,441 230,441 232,442 234,442 236,441 237,440 239,439 241,437 242,433 244,430 246,425 247,419 249,412 251,404 252,396 254,386 256,376 258,365 259,353 261,341 263,329 264,317 266,305 268,293 269,282 271,272 273,263 274,255 276,248 278,243 280,239 281,236 283,235 285,236 286,237 288,240 290,244 291,250 293,255 295,262 296,269 298,276 300,283 302,290 303,297 305,303 307,309 308,314 310,318 312,322 313,325 315,327 317,329 318,331 320,332 322,332 324,333 325,333 327,333 329,333 330,333 332,333 334,333 335,333 337,333 339,333 341,333 342,332 344,332 346,331 347,330 349,329 351,328 352,327 354,325 356,324 357,322 359,321 361,320 363,319 364,318 366,317 368,316 369,316 371,316 373,316 374,316 376,316 378,316 379,316 381,316 383,316 385,316 386,315 388,315 390,314 391,314 393,313 395,312 396,311 398,310 400,309 401,308 403,307 405,306 407,305 408,303 410,302 412,300 413,298 415,296 417,295 418,293 420,291 422,289 423,288 425,286 427,285 429,284 430,283 432,282 434,282 435,282 437,282 439,282 440,282 442,282 444,282 445,282 447,282 449,282 451,282 452,282 454,281 456,281 457,280 459,279 461,278 462,277 464,276 466,274 468,273 469,271 471,270 473,268 474,266 476,264 478,262 479,259 481,257 483,255 484,253 486,251 488,249 490,247 491,245 493,244 495,243 496,242 498,241 500,241 501,240 503,240 505,240 506,239 508,239 510,239 512,238 513,237 515,237 517,236 518,234 520,233 522,231 523,229 525,227 527,225 528,223 530,220 532,217 534,214 535,210 537,207 539,203 540,199 542,195 544,191 545,187 547,183 549,178 550,174 552,170 554,165 556,161 557,156 559,152 561,147 562,143 564,138 566,133 567,129 569,125 571,120 573,116 574,112 576,108 578,105 579,102 581,99 583,97 584,95 586,94 588,93 589,93 591,94 593,96 595,98 596,101 598,105 600,109 601,114 603,120 605,126 606,133 608,140 610,148 611,155 613,164 615,172 617,180 618,189 620,198 622,206 623,214 625,223 627,231 628,239 630,247 632,254 633,262 635,269 637,276 639,282 640,289 642,295 644,301 645,306 647,312 649,317 650,321 652,326 654,330 655,333 657,337 659,340 661,343 662,346 664,348 666,350 667,353 669,355 671,357 672,359 674,360 676,362 677,364 679,365 681,367 683,368 684,370 686,371 688,372 689,373 691,374 693,375 694,376 696,376 698,377 700,377 701,378 703,378 705,379 706,379 708,379 710,380 711,380 713,381 715,381 716,382 718,383 720,384 722,385 723,386 725,387 727,389 728,391 730,392 732,394 733,397 735,399 737,401 738,404 740,407 742,409 744,412 745,415 747,418 749,422 750,425 752,428 754,431 755,434 757,436 759,439 760,441 762,443 764,445 766,447 767,448 769,449 771,449 772,450 774,450 776,450 777,449 779,449 781,448 782,447 784,447 786,446 788,445 789,444 791,443 793,443 794,442 796,442 798,442 799,442 801,443 803,443 804,444 806,445 808,447 810,448 811,449 813,451 815,453 816,454 818,456 820,457 821,459 823,460 825,461 827,462 828,463 830,463 832,463 833,463 835,463 837,463 838,462 840,461 842,460 843,459 845,458 847,457 849,456 850,455 852,453 854,452 855,451 857,450 859,449 860,449 862,448 864,448 865,447 867,447 869,447 871,447 872,447 874,448 876,448 877,449 879,449 881,450 882,451 884,451 886,452 887,453 889,454 891,455 893,456 894,456 896,457 898,458 899,459 901,460 903,461 904,462 906,462 908,463 909,464 911,465 913,465 915,466 916,467 918,467 920,468 921,469 923,469 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,456 166,457 168,457 169,457 171,457 173,458 175,458 176,458 178,458 180,458 181,458 183,458 185,458 186,457 188,456 190,455 191,454 193,453 195,452 197,451 198,449 200,448 202,446 203,444 205,443 207,441 208,440 210,439 212,438 214,437 215,437 217,437 219,437 220,437 222,438 224,438 225,439 227,440 229,441 230,441 232,442 234,442 236,441 237,440 239,439 241,437 242,433 244,430 246,425 247,419 249,412 251,404 252,396 254,386 256,376 258,365 259,353 261,341 263,329 264,317 266,305 268,293 269,282 271,272 273,263 274,255 276,248 278,243 280,239 281,236 283,235 285,236 286,237 288,240 290,244 291,250 293,255 295,262 296,269 298,276 300,283 302,290 303,297 305,303 307,309 308,314 310,318 312,322 313,325 315,327 317,329 318,331 320,332 322,332 324,333 325,333 327,333 329,333 330,333 332,333 334,333 335,333 337,333 339,333 341,333 342,332 344,332 346,331 347,330 349,329 351,328 352,327 354,325 356,324 357,322 359,321 361,320 363,319 364,318 366,317 368,316 369,316 371,316 373,316 374,316 376,316 378,316 379,316 381,316 383,316 385,316 386,315 388,315 390,314 391,314 393,313 395,312 396,311 398,310 400,309 401,308 403,307 405,306 407,305 408,303 410,302 412,300 413,298 415,296 417,295 418,293 420,291 422,289 423,288 425,286 427,285 429,284 430,283 432,282 434,282 435,282 437,282 439,282 440,282 442,282 444,282 445,282 447,282 449,282 451,282 452,282 454,281 456,281 457,280 459,279 461,278 462,277 464,276 466,274 468,273 469,271 471,270 473,268 474,266 476,264 478,262 479,259 481,257 483,255 484,253 486,251 488,249 490,247 491,245 493,244 495,243 496,242 498,241 500,241 501,240 503,240 505,240 506,239 508,239 510,239 512,238 513,237 515,237 517,236 518,234 520,233 522,231 523,229 525,227 527,225 528,223 530,220 532,217 534,214 535,210 537,207 539,203 540,199 542,195 544,191 545,187 547,183 549,178 550,174 552,170 554,165 556,161 557,156 559,152 561,147 562,143 564,138 566,133 567,129 569,125 571,120 573,116 574,112 576,108 578,105 579,102 581,99 583,97 584,95 586,94 588,93 589,93 591,94 593,96 595,98 596,101 598,105 600,109 601,114 603,120 605,126 606,133 608,140 610,148 611,155 613,164 615,172 617,180 618,189 620,198 622,206 623,214 625,223 627,231 628,239 630,247 632,254 633,262 635,269 637,276 639,282 640,289 642,295 644,301 645,306 647,312 649,317 650,321 652,326 654,330 655,333 657,337 659,340 661,343 662,346 664,348 666,350 667,353 669,355 671,357 672,359 674,360 676,362 677,364 679,365 681,367 683,368 684,370 686,371 688,372 689,373 691,374 693,375 694,376 696,376 698,377 700,377 701,378 703,378 705,379 706,379 708,379 710,380 711,380 713,381 715,381 716,382 718,383 720,384 722,385 723,386 725,387 727,389 728,391 730,392 732,394 733,397 735,399 737,401 738,404 740,407 742,409 744,412 745,415 747,418 749,422 750,425 752,428 754,431 755,434 757,436 759,439 760,441 762,443 764,445 766,447 767,448 769,449 771,449 772,450 774,450 776,450 777,449 779,449 781,448 782,447 784,447 786,446 788,445 789,444 791,443 793,443 794,442 796,442 798,442 799,442 801,443 803,443 804,444 806,445 808,447 810,448 811,449 813,451 815,453 816,454 818,456 820,457 821,459 823,460 825,461 827,462 828,463 830,463 832,463 833,463 835,463 837,463 838,462 840,461 842,460 843,459 845,458 847,457 849,456 850,455 852,453 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="520,473 520,233 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_processor/process_log_entry/500:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="446" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,446 86,446 "/>
<text x="77" y="405" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,405 86,405 "/>
<text x="77" y="363" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,363 86,363 "/>
<text x="77" y="322" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,322 86,322 "/>
<text x="77" y="281" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,281 86,281 "/>
<text x="77" y="240" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,240 86,240 "/>
<text x="77" y="198" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.035
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,198 86,198 "/>
<text x="77" y="157" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,157 86,157 "/>
<text x="77" y="116" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.045
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,116 86,116 "/>
<text x="77" y="75" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,75 86,75 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="138" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="138,473 138,478 "/>
<text x="231" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="231,473 231,478 "/>
<text x="323" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="323,473 323,478 "/>
<text x="415" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="415,473 415,478 "/>
<text x="507" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="507,473 507,478 "/>
<text x="600" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="600,473 600,478 "/>
<text x="692" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="692,473 692,478 "/>
<text x="784" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
55
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="784,473 784,478 "/>
<text x="876" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="876,473 876,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,471 88,470 90,469 92,468 93,468 95,467 97,466 98,465 100,464 102,463 103,462 105,461 107,460 109,459 110,458 112,456 114,455 115,453 117,452 119,450 120,449 122,447 124,445 125,443 127,441 129,439 131,437 132,435 134,433 136,430 137,428 139,426 141,423 142,420 144,418 146,415 147,412 149,409 151,406 153,403 154,400 156,397 158,393 159,390 161,386 163,382 164,378 166,375 168,371 169,366 171,362 173,358 175,353 176,349 178,344 180,340 181,335 183,330 185,325 186,320 188,315 190,310 191,305 193,299 195,294 197,289 198,284 200,279 202,273 203,268 205,263 207,259 208,254 210,249 212,245 214,241 215,237 217,233 219,229 220,226 222,222 224,219 225,217 227,214 229,212 230,210 232,208 234,206 236,204 237,203 239,201 241,200 242,199 244,198 246,197 247,196 249,194 251,193 252,192 254,191 256,190 258,188 259,187 261,185 263,184 264,182 266,180 268,178 269,176 271,174 273,171 274,169 276,166 278,164 280,161 281,158 283,155 285,152 286,148 288,145 290,142 291,139 293,136 295,133 296,130 298,127 300,125 302,123 303,121 305,120 307,118 308,118 310,117 312,117 313,117 315,117 317,118 318,119 320,120 322,121 324,122 325,123 327,124 329,126 330,127 332,128 334,128 335,129 337,129 339,130 341,129 342,129 344,129 346,128 347,127 349,125 351,124 352,122 354,120 356,118 357,115 359,113 361,111 363,108 364,106 366,103 368,101 369,99 371,97 373,96 374,94 376,93 378,93 379,93 381,93 383,94 385,96 386,98 388,101 390,104 391,108 393,112 395,117 396,122 398,127 400,133 401,139 403,145 405,151 407,157 408,162 410,168 412,173 413,177 415,181 417,185 418,188 420,190 422,192 423,193 425,193 427,193 429,193 430,192 432,190 434,188 435,186 437,184 439,182 440,180 442,178 444,177 445,176 447,175 449,175 451,175 452,176 454,178 456,180 457,183 459,186 461,190 462,195 464,200 466,205 468,210 469,216 471,222 473,228 474,234 476,240 478,246 479,251 481,257 483,262 484,267 486,271 488,275 490,278 491,281 493,284 495,286 496,288 498,289 500,289 501,289 503,289 505,287 506,286 508,284 510,281 512,278 513,275 515,271 517,267 518,263 520,259 522,254 523,250 525,245 527,241 528,237 530,233 532,230 534,227 535,225 537,224 539,223 540,223 542,224 544,225 545,228 547,231 549,234 550,239 552,244 554,249 556,255 557,261 559,267 561,274 562,281 564,287 566,294 567,300 569,306 571,311 573,316 574,321 576,326 578,330 579,333 581,336 583,339 584,341 586,343 588,344 589,345 591,345 593,346 595,345 596,345 598,344 600,343 601,342 603,340 605,339 606,337 608,335 610,333 611,331 613,329 615,327 617,325 618,324 620,322 622,321 623,320 625,320 627,320 628,320 630,320 632,321 633,323 635,325 637,327 639,329 640,332 642,335 644,338 645,341 647,345 649,348 650,351 652,355 654,358 655,361 657,364 659,367 661,370 662,372 664,374 666,376 667,378 669,379 671,380 672,381 674,382 676,383 677,384 679,384 681,385 683,385 684,385 686,386 688,386 689,387 691,388 693,388 694,389 696,390 698,392 700,393 701,394 703,396 705,397 706,399 708,401 710,402 711,404 713,405 715,407 716,409 718,410 720,411 722,413 723,414 725,415 727,416 728,417 730,417 732,418 733,418 735,419 737,419 738,420 740,420 742,420 744,421 745,421 747,421 749,421 750,421 752,421 754,421 755,421 757,421 759,421 760,421 762,421 764,421 766,421 767,421 769,420 771,420 772,420 774,420 776,419 777,419 779,419 781,419 782,418 784,418 786,418 788,419 789,419 791,419 793,420 794,420 796,421 798,422 799,422 801,423 803,424 804,425 806,426 808,427 810,428 811,429 813,430 815,431 816,432 818,433 820,434 821,435 823,436 825,437 827,438 828,438 830,439 832,440 833,441 835,442 837,443 838,444 840,445 842,446 843,447 845,448 847,449 849,450 850,451 852,452 854,454 855,455 857,456 859,457 860,458 862,459 864,460 865,461 867,462 869,463 871,463 872,464 874,465 876,465 877,466 879,467 881,467 882,468 884,468 886,468 887,469 889,469 891,469 893,470 894,470 896,470 898,471 899,471 901,471 903,471 904,472 906,472 908,472 909,472 911,472 913,472 915,472 916,472 918,472 920,472 921,472 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,378 166,375 168,371 169,366 171,362 173,358 175,353 176,349 178,344 180,340 181,335 183,330 185,325 186,320 188,315 190,310 191,305 193,299 195,294 197,289 198,284 200,279 202,273 203,268 205,263 207,259 208,254 210,249 212,245 214,241 215,237 217,233 219,229 220,226 222,222 224,219 225,217 227,214 229,212 230,210 232,208 234,206 236,204 237,203 239,201 241,200 242,199 244,198 246,197 247,196 249,194 251,193 252,192 254,191 256,190 258,188 259,187 261,185 263,184 264,182 266,180 268,178 269,176 271,174 273,171 274,169 276,166 278,164 280,161 281,158 283,155 285,152 286,148 288,145 290,142 291,139 293,136 295,133 296,130 298,127 300,125 302,123 303,121 305,120 307,118 308,118 310,117 312,117 313,117 315,117 317,118 318,119 320,120 322,121 324,122 325,123 327,124 329,126 330,127 332,128 334,128 335,129 337,129 339,130 341,129 342,129 344,129 346,128 347,127 349,125 351,124 352,122 354,120 356,118 357,115 359,113 361,111 363,108 364,106 366,103 368,101 369,99 371,97 373,96 374,94 376,93 378,93 379,93 381,93 383,94 385,96 386,98 388,101 390,104 391,108 393,112 395,117 396,122 398,127 400,133 401,139 403,145 405,151 407,157 408,162 410,168 412,173 413,177 415,181 417,185 418,188 420,190 422,192 423,193 425,193 427,193 429,193 430,192 432,190 434,188 435,186 437,184 439,182 440,180 442,178 444,177 445,176 447,175 449,175 451,175 452,176 454,178 456,180 457,183 459,186 461,190 462,195 464,200 466,205 468,210 469,216 471,222 473,228 474,234 476,240 478,246 479,251 481,257 483,262 484,267 486,271 488,275 490,278 491,281 493,284 495,286 496,288 498,289 500,289 501,289 503,289 505,287 506,286 508,284 510,281 512,278 513,275 515,271 517,267 518,263 520,259 522,254 523,250 525,245 527,241 528,237 530,233 532,230 534,227 535,225 537,224 539,223 540,223 542,224 544,225 545,228 547,231 549,234 550,239 552,244 554,249 556,255 557,261 559,267 561,274 562,281 564,287 566,294 567,300 569,306 571,311 573,316 574,321 576,326 578,330 579,333 581,336 583,339 584,341 586,343 588,344 589,345 591,345 593,346 595,345 596,345 598,344 600,343 601,342 603,340 605,339 606,337 608,335 610,333 611,331 613,329 615,327 617,325 618,324 620,322 622,321 623,320 625,320 627,320 628,320 630,320 632,321 633,323 635,325 637,327 639,329 640,332 642,335 644,338 645,341 647,345 649,348 650,351 652,355 654,358 655,361 657,364 659,367 661,370 662,372 664,374 666,376 667,378 669,379 671,380 672,381 674,382 676,383 677,384 679,384 681,385 683,385 684,385 686,386 688,386 689,387 691,388 693,388 694,389 696,390 698,392 700,393 701,394 703,396 705,397 706,399 708,401 710,402 711,404 713,405 715,407 716,409 718,410 720,411 722,413 723,414 725,415 727,416 728,417 730,417 732,418 733,418 735,419 737,419 738,420 740,420 742,420 744,421 745,421 747,421 749,421 750,421 752,421 754,421 755,421 757,421 759,421 760,421 762,421 764,421 766,421 767,421 769,420 771,420 772,420 774,420 776,419 777,419 779,419 781,419 782,418 784,418 786,418 788,419 789,419 791,419 793,420 794,420 796,421 798,422 799,422 801,423 803,424 804,425 806,426 808,427 810,428 811,429 813,430 815,431 816,432 818,433 820,434 821,435 823,436 825,437 827,438 828,438 830,439 832,440 833,441 835,442 837,443 838,444 840,445 842,446 843,447 845,448 847,449 849,450 850,451 852,452 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="447,473 447,175 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="457" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,457 86,457 "/>
<text x="77" y="419" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,419 86,419 "/>
<text x="77" y="382" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,382 86,382 "/>
<text x="77" y="344" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,344 86,344 "/>
<text x="77" y="307" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,307 86,307 "/>
<text x="77" y="269" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,269 86,269 "/>
<text x="77" y="231" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,231 86,231 "/>
<text x="77" y="194" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,194 86,194 "/>
<text x="77" y="156" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,156 86,156 "/>
<text x="77" y="119" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,119 86,119 "/>
<text x="77" y="81" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.55
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,81 86,81 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="154" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="154,473 154,478 "/>
<text x="269" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="269,473 269,478 "/>
<text x="384" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="384,473 384,478 "/>
<text x="500" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="500,473 500,478 "/>
<text x="615" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="615,473 615,478 "/>
<text x="731" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="731,473 731,478 "/>
<text x="846" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="846,473 846,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,470 88,469 90,468 92,468 93,467 95,466 97,466 98,465 100,464 102,464 103,463 105,462 107,461 109,461 110,460 112,459 114,459 115,458 117,457 119,457 120,456 122,455 124,454 125,454 127,453 129,452 131,451 132,450 134,449 136,448 137,447 139,446 141,445 142,444 144,443 146,441 147,440 149,439 151,437 153,436 154,434 156,433 158,431 159,430 161,428 163,426 164,425 166,423 168,422 169,420 171,419 173,417 175,416 176,414 178,413 180,411 181,410 183,408 185,407 186,406 188,404 190,403 191,402 193,400 195,399 197,398 198,396 200,395 202,393 203,391 205,390 207,388 208,386 210,384 212,382 214,380 215,378 217,375 219,373 220,370 222,368 224,365 225,363 227,360 229,357 230,354 232,351 234,348 236,345 237,342 239,338 241,335 242,332 244,328 246,324 247,321 249,317 251,313 252,309 254,305 256,300 258,296 259,292 261,288 263,283 264,279 266,275 268,271 269,268 271,264 273,261 274,258 276,255 278,252 280,250 281,248 283,247 285,246 286,245 288,244 290,243 291,243 293,243 295,243 296,243 298,243 300,244 302,244 303,244 305,244 307,244 308,244 310,244 312,243 313,243 315,243 317,242 318,241 320,240 322,239 324,238 325,237 327,236 329,234 330,233 332,231 334,230 335,228 337,226 339,223 341,221 342,218 344,215 346,212 347,208 349,205 351,200 352,196 354,191 356,186 357,181 359,175 361,170 363,164 364,158 366,152 368,146 369,141 371,135 373,130 374,124 376,119 378,115 379,111 381,107 383,104 385,101 386,99 388,97 390,95 391,94 393,94 395,94 396,94 398,94 400,95 401,97 403,98 405,100 407,102 408,104 410,106 412,109 413,111 415,114 417,117 418,120 420,122 422,125 423,128 425,131 427,133 429,136 430,138 432,141 434,143 435,145 437,146 439,148 440,149 442,150 444,151 445,151 447,151 449,151 451,151 452,150 454,149 456,148 457,147 459,145 461,144 462,142 464,141 466,139 468,137 469,136 471,134 473,133 474,132 476,131 478,130 479,129 481,128 483,128 484,127 486,127 488,126 490,126 491,126 493,126 495,127 496,127 498,127 500,127 501,128 503,128 505,128 506,129 508,129 510,130 512,130 513,131 515,132 517,133 518,133 520,134 522,135 523,136 525,138 527,139 528,140 530,141 532,143 534,144 535,145 537,147 539,148 540,149 542,150 544,152 545,153 547,153 549,154 550,155 552,155 554,156 556,156 557,157 559,157 561,157 562,157 564,157 566,157 567,158 569,158 571,159 573,159 574,160 576,161 578,163 579,164 581,166 583,168 584,170 586,173 588,175 589,178 591,181 593,184 595,187 596,190 598,193 600,196 601,199 603,201 605,204 606,206 608,209 610,211 611,213 613,215 615,217 617,219 618,221 620,223 622,225 623,227 625,228 627,230 628,232 630,234 632,236 633,238 635,240 637,242 639,244 640,246 642,248 644,251 645,253 647,255 649,257 650,258 652,260 654,262 655,264 657,266 659,268 661,270 662,272 664,273 666,275 667,277 669,279 671,280 672,282 674,284 676,285 677,287 679,288 681,290 683,292 684,293 686,295 688,296 689,297 691,299 693,300 694,302 696,304 698,305 700,307 701,308 703,310 705,311 706,313 708,315 710,316 711,318 713,320 715,322 716,323 718,325 720,327 722,328 723,330 725,331 727,333 728,335 730,336 732,338 733,339 735,341 737,342 738,344 740,345 742,346 744,348 745,349 747,350 749,352 750,353 752,354 754,355 755,356 757,358 759,359 760,360 762,361 764,363 766,364 767,366 769,367 771,369 772,371 774,372 776,374 777,376 779,379 781,381 782,383 784,385 786,387 788,390 789,392 791,394 793,397 794,399 796,401 798,403 799,405 801,407 803,409 804,411 806,413 808,415 810,416 811,418 813,419 815,421 816,422 818,424 820,425 821,426 823,428 825,429 827,430 828,431 830,432 832,433 833,434 835,435 837,436 838,437 840,438 842,439 843,440 845,441 847,442 849,442 850,443 852,444 854,445 855,446 857,447 859,448 860,449 862,450 864,451 865,451 867,452 869,453 871,454 872,455 874,455 876,456 877,457 879,458 881,458 882,459 884,459 886,460 887,460 889,461 891,461 893,462 894,462 896,463 898,463 899,463 901,464 903,464 904,465 906,465 908,465 909,466 911,466 913,467 915,467 916,468 918,468 920,469 921,469 923,470 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,425 166,423 168,422 169,420 171,419 173,417 175,416 176,414 178,413 180,411 181,410 183,408 185,407 186,406 188,404 190,403 191,402 193,400 195,399 197,398 198,396 200,395 202,393 203,391 205,390 207,388 208,386 210,384 212,382 214,380 215,378 217,375 219,373 220,370 222,368 224,365 225,363 227,360 229,357 230,354 232,351 234,348 236,345 237,342 239,338 241,335 242,332 244,328 246,324 247,321 249,317 251,313 252,309 254,305 256,300 258,296 259,292 261,288 263,283 264,279 266,275 268,271 269,268 271,264 273,261 274,258 276,255 278,252 280,250 281,248 283,247 285,246 286,245 288,244 290,243 291,243 293,243 295,243 296,243 298,243 300,244 302,244 303,244 305,244 307,244 308,244 310,244 312,243 313,243 315,243 317,242 318,241 320,240 322,239 324,238 325,237 327,236 329,234 330,233 332,231 334,230 335,228 337,226 339,223 341,221 342,218 344,215 346,212 347,208 349,205 351,200 352,196 354,191 356,186 357,181 359,175 361,170 363,164 364,158 366,152 368,146 369,141 371,135 373,130 374,124 376,119 378,115 379,111 381,107 383,104 385,101 386,99 388,97 390,95 391,94 393,94 395,94 396,94 398,94 400,95 401,97 403,98 405,100 407,102 408,104 410,106 412,109 413,111 415,114 417,117 418,120 420,122 422,125 423,128 425,131 427,133 429,136 430,138 432,141 434,143 435,145 437,146 439,148 440,149 442,150 444,151 445,151 447,151 449,151 451,151 452,150 454,149 456,148 457,147 459,145 461,144 462,142 464,141 466,139 468,137 469,136 471,134 473,133 474,132 476,131 478,130 479,129 481,128 483,128 484,127 486,127 488,126 490,126 491,126 493,126 495,127 496,127 498,127 500,127 501,128 503,128 505,128 506,129 508,129 510,130 512,130 513,131 515,132 517,133 518,133 520,134 522,135 523,136 525,138 527,139 528,140 530,141 532,143 534,144 535,145 537,147 539,148 540,149 542,150 544,152 545,153 547,153 549,154 550,155 552,155 554,156 556,156 557,157 559,157 561,157 562,157 564,157 566,157 567,158 569,158 571,159 573,159 574,160 576,161 578,163 579,164 581,166 583,168 584,170 586,173 588,175 589,178 591,181 593,184 595,187 596,190 598,193 600,196 601,199 603,201 605,204 606,206 608,209 610,211 611,213 613,215 615,217 617,219 618,221 620,223 622,225 623,227 625,228 627,230 628,232 630,234 632,236 633,238 635,240 637,242 639,244 640,246 642,248 644,251 645,253 647,255 649,257 650,258 652,260 654,262 655,264 657,266 659,268 661,270 662,272 664,273 666,275 667,277 669,279 671,280 672,282 674,284 676,285 677,287 679,288 681,290 683,292 684,293 686,295 688,296 689,297 691,299 693,300 694,302 696,304 698,305 700,307 701,308 703,310 705,311 706,313 708,315 710,316 711,318 713,320 715,322 716,323 718,325 720,327 722,328 723,330 725,331 727,333 728,335 730,336 732,338 733,339 735,341 737,342 738,344 740,345 742,346 744,348 745,349 747,350 749,352 750,353 752,354 754,355 755,356 757,358 759,359 760,360 762,361 764,363 766,364 767,366 769,367 771,369 772,371 774,372 776,374 777,376 779,379 781,381 782,383 784,385 786,387 788,390 789,392 791,394 793,397 794,399 796,401 798,403 799,405 801,407 803,409 804,411 806,413 808,415 810,416 811,418 813,419 815,421 816,422 818,424 820,425 821,426 823,428 825,429 827,430 828,431 830,432 832,433 833,434 835,435 837,436 838,437 840,438 842,439 843,440 845,441 847,442 849,442 850,443 852,444 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="457,473 457,147 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

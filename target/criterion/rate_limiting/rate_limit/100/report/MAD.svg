<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="444" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,444 86,444 "/>
<text x="77" y="403" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,403 86,403 "/>
<text x="77" y="361" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,361 86,361 "/>
<text x="77" y="319" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,319 86,319 "/>
<text x="77" y="278" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,278 86,278 "/>
<text x="77" y="236" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,236 86,236 "/>
<text x="77" y="194" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,194 86,194 "/>
<text x="77" y="152" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,152 86,152 "/>
<text x="77" y="111" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,111 86,111 "/>
<text x="77" y="69" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,69 86,69 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="115" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="115,473 115,478 "/>
<text x="194" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="194,473 194,478 "/>
<text x="272" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="272,473 272,478 "/>
<text x="351" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="351,473 351,478 "/>
<text x="430" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="430,473 430,478 "/>
<text x="509" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="509,473 509,478 "/>
<text x="587" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="587,473 587,478 "/>
<text x="666" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="666,473 666,478 "/>
<text x="745" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="745,473 745,478 "/>
<text x="824" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="824,473 824,478 "/>
<text x="902" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="902,473 902,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,465 88,465 90,464 92,463 93,462 95,461 97,461 98,460 100,459 102,458 103,458 105,457 107,457 109,456 110,456 112,455 114,455 115,455 117,455 119,454 120,454 122,454 124,454 125,454 127,454 129,454 131,454 132,453 134,453 136,453 137,453 139,452 141,452 142,452 144,451 146,451 147,450 149,449 151,448 153,448 154,447 156,445 158,444 159,443 161,442 163,440 164,438 166,437 168,435 169,433 171,431 173,429 175,427 176,424 178,422 180,420 181,417 183,415 185,412 186,410 188,407 190,405 191,402 193,400 195,397 197,395 198,392 200,390 202,387 203,385 205,382 207,379 208,377 210,374 212,371 214,368 215,365 217,362 219,359 220,355 222,352 224,349 225,345 227,341 229,338 230,334 232,330 234,327 236,323 237,319 239,316 241,312 242,309 244,305 246,301 247,298 249,294 251,291 252,287 254,283 256,280 258,276 259,272 261,268 263,264 264,259 266,255 268,250 269,245 271,240 273,234 274,228 276,223 278,217 280,210 281,204 283,197 285,191 286,184 288,177 290,170 291,164 293,157 295,150 296,144 298,138 300,132 302,126 303,121 305,116 307,111 308,107 310,104 312,100 313,98 315,96 317,94 318,93 320,93 322,93 324,94 325,96 327,98 329,101 330,104 332,108 334,112 335,117 337,122 339,128 341,134 342,141 344,147 346,154 347,161 349,168 351,174 352,181 354,187 356,194 357,200 359,205 361,211 363,216 364,220 366,225 368,229 369,232 371,235 373,238 374,241 376,243 378,246 379,248 381,250 383,251 385,253 386,255 388,257 390,258 391,260 393,262 395,264 396,265 398,267 400,268 401,270 403,271 405,271 407,272 408,272 410,272 412,271 413,270 415,268 417,266 418,263 420,260 422,256 423,252 425,247 427,242 429,237 430,231 432,226 434,220 435,215 437,210 439,206 440,202 442,198 444,196 445,194 447,193 449,192 451,192 452,194 454,195 456,198 457,201 459,204 461,208 462,212 464,216 466,220 468,224 469,228 471,231 473,235 474,238 476,240 478,242 479,244 481,245 483,246 484,246 486,246 488,245 490,244 491,243 493,242 495,240 496,238 498,237 500,235 501,233 503,231 505,229 506,228 508,226 510,224 512,223 513,221 515,220 517,218 518,217 520,216 522,215 523,215 525,214 527,214 528,214 530,214 532,215 534,216 535,217 537,219 539,221 540,223 542,226 544,229 545,232 547,236 549,240 550,244 552,249 554,253 556,258 557,262 559,266 561,271 562,275 564,278 566,282 567,285 569,288 571,291 573,294 574,296 576,298 578,299 579,300 581,302 583,302 584,303 586,303 588,303 589,303 591,303 593,302 595,302 596,301 598,301 600,300 601,300 603,300 605,299 606,299 608,300 610,300 611,301 613,302 615,303 617,305 618,307 620,310 622,312 623,315 625,318 627,322 628,325 630,329 632,332 633,336 635,339 637,343 639,346 640,349 642,352 644,354 645,357 647,359 649,361 650,363 652,365 654,366 655,367 657,369 659,370 661,371 662,372 664,373 666,374 667,375 669,376 671,377 672,378 674,379 676,380 677,380 679,381 681,382 683,383 684,384 686,384 688,385 689,386 691,387 693,388 694,389 696,390 698,391 700,392 701,393 703,395 705,396 706,398 708,400 710,402 711,404 713,406 715,408 716,410 718,412 720,414 722,415 723,417 725,419 727,421 728,422 730,424 732,425 733,426 735,428 737,428 738,429 740,430 742,430 744,431 745,431 747,431 749,431 750,431 752,430 754,430 755,429 757,429 759,429 760,428 762,428 764,427 766,427 767,426 769,426 771,426 772,425 774,425 776,425 777,425 779,425 781,426 782,426 784,426 786,427 788,428 789,428 791,429 793,430 794,430 796,431 798,432 799,433 801,434 803,434 804,435 806,436 808,437 810,438 811,438 813,439 815,440 816,441 818,441 820,442 821,443 823,443 825,444 827,445 828,445 830,446 832,446 833,447 835,447 837,448 838,448 840,449 842,449 843,450 845,450 847,451 849,451 850,452 852,453 854,453 855,454 857,454 859,455 860,456 862,456 864,457 865,457 867,458 869,459 871,459 872,460 874,460 876,460 877,461 879,461 881,461 882,462 884,462 886,462 887,462 889,462 891,463 893,463 894,463 896,463 898,464 899,464 901,464 903,465 904,465 906,466 908,466 909,467 911,467 913,468 915,468 916,469 918,469 920,470 921,470 923,471 925,471 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,438 166,437 168,435 169,433 171,431 173,429 175,427 176,424 178,422 180,420 181,417 183,415 185,412 186,410 188,407 190,405 191,402 193,400 195,397 197,395 198,392 200,390 202,387 203,385 205,382 207,379 208,377 210,374 212,371 214,368 215,365 217,362 219,359 220,355 222,352 224,349 225,345 227,341 229,338 230,334 232,330 234,327 236,323 237,319 239,316 241,312 242,309 244,305 246,301 247,298 249,294 251,291 252,287 254,283 256,280 258,276 259,272 261,268 263,264 264,259 266,255 268,250 269,245 271,240 273,234 274,228 276,223 278,217 280,210 281,204 283,197 285,191 286,184 288,177 290,170 291,164 293,157 295,150 296,144 298,138 300,132 302,126 303,121 305,116 307,111 308,107 310,104 312,100 313,98 315,96 317,94 318,93 320,93 322,93 324,94 325,96 327,98 329,101 330,104 332,108 334,112 335,117 337,122 339,128 341,134 342,141 344,147 346,154 347,161 349,168 351,174 352,181 354,187 356,194 357,200 359,205 361,211 363,216 364,220 366,225 368,229 369,232 371,235 373,238 374,241 376,243 378,246 379,248 381,250 383,251 385,253 386,255 388,257 390,258 391,260 393,262 395,264 396,265 398,267 400,268 401,270 403,271 405,271 407,272 408,272 410,272 412,271 413,270 415,268 417,266 418,263 420,260 422,256 423,252 425,247 427,242 429,237 430,231 432,226 434,220 435,215 437,210 439,206 440,202 442,198 444,196 445,194 447,193 449,192 451,192 452,194 454,195 456,198 457,201 459,204 461,208 462,212 464,216 466,220 468,224 469,228 471,231 473,235 474,238 476,240 478,242 479,244 481,245 483,246 484,246 486,246 488,245 490,244 491,243 493,242 495,240 496,238 498,237 500,235 501,233 503,231 505,229 506,228 508,226 510,224 512,223 513,221 515,220 517,218 518,217 520,216 522,215 523,215 525,214 527,214 528,214 530,214 532,215 534,216 535,217 537,219 539,221 540,223 542,226 544,229 545,232 547,236 549,240 550,244 552,249 554,253 556,258 557,262 559,266 561,271 562,275 564,278 566,282 567,285 569,288 571,291 573,294 574,296 576,298 578,299 579,300 581,302 583,302 584,303 586,303 588,303 589,303 591,303 593,302 595,302 596,301 598,301 600,300 601,300 603,300 605,299 606,299 608,300 610,300 611,301 613,302 615,303 617,305 618,307 620,310 622,312 623,315 625,318 627,322 628,325 630,329 632,332 633,336 635,339 637,343 639,346 640,349 642,352 644,354 645,357 647,359 649,361 650,363 652,365 654,366 655,367 657,369 659,370 661,371 662,372 664,373 666,374 667,375 669,376 671,377 672,378 674,379 676,380 677,380 679,381 681,382 683,383 684,384 686,384 688,385 689,386 691,387 693,388 694,389 696,390 698,391 700,392 701,393 703,395 705,396 706,398 708,400 710,402 711,404 713,406 715,408 716,410 718,412 720,414 722,415 723,417 725,419 727,421 728,422 730,424 732,425 733,426 735,428 737,428 738,429 740,430 742,430 744,431 745,431 747,431 749,431 750,431 752,430 754,430 755,429 757,429 759,429 760,428 762,428 764,427 766,427 767,426 769,426 771,426 772,425 774,425 776,425 777,425 779,425 781,426 782,426 784,426 786,427 788,428 789,428 791,429 793,430 794,430 796,431 798,432 799,433 801,434 803,434 804,435 806,436 808,437 810,438 811,438 813,439 815,440 816,441 818,441 820,442 821,443 823,443 825,444 827,445 828,445 830,446 832,446 833,447 835,447 837,448 838,448 840,449 842,449 843,450 845,450 847,451 849,451 850,452 852,453 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="406,473 406,272 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

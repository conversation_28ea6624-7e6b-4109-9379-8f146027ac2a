<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="421" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,421 86,421 "/>
<text x="77" y="346" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,346 86,346 "/>
<text x="77" y="270" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,270 86,270 "/>
<text x="77" y="195" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,195 86,195 "/>
<text x="77" y="119" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,119 86,119 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="91" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="91,473 91,478 "/>
<text x="220" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="220,473 220,478 "/>
<text x="350" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="350,473 350,478 "/>
<text x="480" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="480,473 480,478 "/>
<text x="610" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="610,473 610,478 "/>
<text x="740" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="740,473 740,478 "/>
<text x="870" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="870,473 870,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,471 95,470 97,470 98,470 100,469 102,469 103,468 105,468 107,467 109,467 110,466 112,465 114,465 115,464 117,464 119,463 120,462 122,462 124,461 125,460 127,459 129,459 131,458 132,457 134,457 136,456 137,455 139,454 141,453 142,453 144,452 146,451 147,450 149,449 151,449 153,448 154,447 156,446 158,445 159,445 161,444 163,443 164,442 166,441 168,441 169,440 171,439 173,438 175,438 176,437 178,436 180,435 181,434 183,433 185,432 186,432 188,431 190,430 191,429 193,428 195,427 197,426 198,425 200,424 202,423 203,422 205,421 207,420 208,419 210,418 212,416 214,415 215,414 217,413 219,411 220,410 222,409 224,407 225,406 227,404 229,403 230,401 232,400 234,398 236,397 237,395 239,394 241,392 242,391 244,389 246,388 247,386 249,385 251,383 252,382 254,380 256,379 258,377 259,376 261,374 263,373 264,372 266,370 268,368 269,367 271,365 273,364 274,362 276,361 278,359 280,357 281,356 283,354 285,353 286,351 288,349 290,348 291,346 293,344 295,343 296,341 298,339 300,338 302,336 303,334 305,333 307,331 308,329 310,327 312,326 313,324 315,322 317,320 318,318 320,316 322,314 324,312 325,310 327,308 329,306 330,303 332,301 334,299 335,297 337,295 339,292 341,290 342,288 344,285 346,283 347,281 349,279 351,277 352,275 354,272 356,270 357,268 359,266 361,264 363,262 364,260 366,258 368,256 369,254 371,252 373,250 374,248 376,245 378,243 379,241 381,239 383,237 385,234 386,232 388,230 390,227 391,225 393,222 395,220 396,218 398,215 400,213 401,211 403,208 405,206 407,204 408,201 410,199 412,197 413,195 415,192 417,190 418,188 420,186 422,184 423,181 425,179 427,177 429,175 430,173 432,170 434,168 435,166 437,164 439,162 440,160 442,157 444,155 445,153 447,151 449,149 451,147 452,145 454,143 456,140 457,138 459,136 461,134 462,132 464,131 466,129 468,127 469,125 471,123 473,121 474,120 476,118 478,116 479,114 481,113 483,111 484,110 486,108 488,106 490,105 491,103 493,102 495,100 496,99 498,97 500,96 501,95 503,93 505,92 506,90 508,89 510,88 512,86 513,85 515,84 517,82 518,81 520,80 522,79 523,77 525,76 527,75 528,74 530,73 532,71 534,70 535,69 537,68 539,67 540,66 542,64 544,63 545,62 547,61 549,60 550,59 552,59 554,58 556,57 557,56 559,56 561,55 562,55 564,54 566,54 567,54 569,54 571,54 573,53 574,54 576,54 578,54 579,54 581,55 583,55 584,55 586,56 588,56 589,57 591,57 593,58 595,59 596,59 598,60 600,61 601,61 603,62 605,63 606,63 608,64 610,65 611,66 613,66 615,67 617,68 618,69 620,70 622,71 623,72 625,73 627,74 628,75 630,76 632,77 633,78 635,80 637,81 639,82 640,84 642,85 644,87 645,88 647,90 649,91 650,93 652,95 654,96 655,98 657,100 659,102 661,104 662,106 664,108 666,110 667,112 669,114 671,116 672,118 674,120 676,123 677,125 679,127 681,129 683,132 684,134 686,136 688,139 689,141 691,144 693,146 694,148 696,151 698,153 700,156 701,159 703,161 705,164 706,166 708,169 710,172 711,174 713,177 715,180 716,183 718,185 720,188 722,191 723,194 725,197 727,200 728,202 730,205 732,208 733,211 735,214 737,217 738,220 740,223 742,226 744,229 745,232 747,235 749,238 750,241 752,244 754,247 755,250 757,253 759,256 760,259 762,262 764,265 766,268 767,271 769,274 771,277 772,279 774,282 776,285 777,288 779,291 781,294 782,297 784,300 786,302 788,305 789,308 791,311 793,314 794,317 796,320 798,322 799,325 801,328 803,331 804,334 806,336 808,339 810,342 811,344 813,347 815,350 816,352 818,355 820,357 821,360 823,362 825,364 827,367 828,369 830,372 832,374 833,376 835,378 837,381 838,383 840,385 842,387 843,390 845,392 847,394 849,396 850,398 852,401 854,403 855,405 857,407 859,409 860,411 862,413 864,415 865,417 867,419 869,421 871,423 872,425 874,427 876,429 877,431 879,433 881,434 882,436 884,438 886,439 887,441 889,442 891,444 893,445 894,447 896,448 898,450 899,451 901,452 903,454 904,455 906,456 908,457 909,459 911,460 913,461 915,462 916,463 918,464 920,465 921,466 923,467 925,468 926,469 928,470 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,442 166,441 168,441 169,440 171,439 173,438 175,438 176,437 178,436 180,435 181,434 183,433 185,432 186,432 188,431 190,430 191,429 193,428 195,427 197,426 198,425 200,424 202,423 203,422 205,421 207,420 208,419 210,418 212,416 214,415 215,414 217,413 219,411 220,410 222,409 224,407 225,406 227,404 229,403 230,401 232,400 234,398 236,397 237,395 239,394 241,392 242,391 244,389 246,388 247,386 249,385 251,383 252,382 254,380 256,379 258,377 259,376 261,374 263,373 264,372 266,370 268,368 269,367 271,365 273,364 274,362 276,361 278,359 280,357 281,356 283,354 285,353 286,351 288,349 290,348 291,346 293,344 295,343 296,341 298,339 300,338 302,336 303,334 305,333 307,331 308,329 310,327 312,326 313,324 315,322 317,320 318,318 320,316 322,314 324,312 325,310 327,308 329,306 330,303 332,301 334,299 335,297 337,295 339,292 341,290 342,288 344,285 346,283 347,281 349,279 351,277 352,275 354,272 356,270 357,268 359,266 361,264 363,262 364,260 366,258 368,256 369,254 371,252 373,250 374,248 376,245 378,243 379,241 381,239 383,237 385,234 386,232 388,230 390,227 391,225 393,222 395,220 396,218 398,215 400,213 401,211 403,208 405,206 407,204 408,201 410,199 412,197 413,195 415,192 417,190 418,188 420,186 422,184 423,181 425,179 427,177 429,175 430,173 432,170 434,168 435,166 437,164 439,162 440,160 442,157 444,155 445,153 447,151 449,149 451,147 452,145 454,143 456,140 457,138 459,136 461,134 462,132 464,131 466,129 468,127 469,125 471,123 473,121 474,120 476,118 478,116 479,114 481,113 483,111 484,110 486,108 488,106 490,105 491,103 493,102 495,100 496,99 498,97 500,96 501,95 503,93 505,92 506,90 508,89 510,88 512,86 513,85 515,84 517,82 518,81 520,80 522,79 523,77 525,76 527,75 528,74 530,73 532,71 534,70 535,69 537,68 539,67 540,66 542,64 544,63 545,62 547,61 549,60 550,59 552,59 554,58 556,57 557,56 559,56 561,55 562,55 564,54 566,54 567,54 569,54 571,54 573,53 574,54 576,54 578,54 579,54 581,55 583,55 584,55 586,56 588,56 589,57 591,57 593,58 595,59 596,59 598,60 600,61 601,61 603,62 605,63 606,63 608,64 610,65 611,66 613,66 615,67 617,68 618,69 620,70 622,71 623,72 625,73 627,74 628,75 630,76 632,77 633,78 635,80 637,81 639,82 640,84 642,85 644,87 645,88 647,90 649,91 650,93 652,95 654,96 655,98 657,100 659,102 661,104 662,106 664,108 666,110 667,112 669,114 671,116 672,118 674,120 676,123 677,125 679,127 681,129 683,132 684,134 686,136 688,139 689,141 691,144 693,146 694,148 696,151 698,153 700,156 701,159 703,161 705,164 706,166 708,169 710,172 711,174 713,177 715,180 716,183 718,185 720,188 722,191 723,194 725,197 727,200 728,202 730,205 732,208 733,211 735,214 737,217 738,220 740,223 742,226 744,229 745,232 747,235 749,238 750,241 752,244 754,247 755,250 757,253 759,256 760,259 762,262 764,265 766,268 767,271 769,274 771,277 772,279 774,282 776,285 777,288 779,291 781,294 782,297 784,300 786,302 788,305 789,308 791,311 793,314 794,317 796,320 798,322 799,325 801,328 803,331 804,334 806,336 808,339 810,342 811,344 813,347 815,350 816,352 818,355 820,357 821,360 823,362 825,364 827,367 828,369 830,372 832,374 833,376 835,378 837,381 838,383 840,385 842,387 843,390 845,392 847,394 849,396 850,398 852,401 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="542,473 542,65 "/>
<rect x="740" y="53" width="192" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

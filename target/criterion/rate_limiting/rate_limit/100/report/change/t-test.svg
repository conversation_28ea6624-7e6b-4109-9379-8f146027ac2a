<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100: Welch t test
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
t score
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="472" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,472 86,472 "/>
<text x="77" y="420" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,420 86,420 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="315" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,315 86,315 "/>
<text x="77" y="263" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,263 86,263 "/>
<text x="77" y="210" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,210 86,210 "/>
<text x="77" y="158" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,158 86,158 "/>
<text x="77" y="105" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,105 86,105 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="115" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-5.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="115,473 115,478 "/>
<text x="211" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-4.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="211,473 211,478 "/>
<text x="307" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-3.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="307,473 307,478 "/>
<text x="403" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-2.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="403,473 403,478 "/>
<text x="499" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-1.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="499,473 499,478 "/>
<text x="595" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="595,473 595,478 "/>
<text x="691" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="691,473 691,478 "/>
<text x="787" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="787,473 787,478 "/>
<text x="883" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="883,473 883,478 "/>
<polygon opacity="0.25" fill="#1F78B4" points="87,472 88,472 90,472 92,472 93,472 95,472 97,472 98,472 100,472 102,472 103,472 105,472 107,472 109,472 110,472 112,472 114,472 115,472 117,472 119,472 120,472 122,472 124,472 125,472 127,472 129,472 131,472 132,472 134,472 136,472 137,472 139,472 141,472 142,472 144,472 146,472 147,472 149,472 151,472 153,472 154,472 156,472 158,472 159,472 161,472 163,472 164,472 166,472 168,472 169,472 171,472 173,472 175,472 176,472 178,472 180,472 181,472 183,472 185,472 186,472 188,472 190,472 191,472 193,472 195,472 197,472 198,472 200,472 202,472 203,472 205,472 207,472 208,472 210,472 212,472 214,472 215,472 217,472 219,472 220,472 222,472 224,472 225,472 227,472 229,472 230,472 232,472 234,472 236,472 237,472 239,472 241,472 242,472 244,472 246,472 247,472 249,472 251,472 252,472 254,472 256,472 258,472 259,472 261,472 263,471 264,471 266,471 268,471 269,471 271,471 273,471 274,471 276,471 278,470 280,470 281,470 283,470 285,470 286,469 288,469 290,469 291,468 293,468 295,468 296,467 298,467 300,466 302,465 303,465 305,464 307,463 308,462 310,461 312,460 313,459 315,458 317,457 318,455 320,454 322,453 324,451 325,449 327,448 329,446 330,444 332,442 334,440 335,437 337,435 339,432 341,430 342,427 344,424 346,421 347,418 349,415 351,412 352,408 354,405 356,401 357,397 359,393 361,389 363,385 364,381 366,377 368,372 369,368 371,363 373,359 374,354 376,349 378,345 379,340 381,335 383,330 385,326 386,321 388,316 390,311 391,306 393,302 395,297 396,292 398,287 400,283 401,278 403,274 405,270 407,265 408,261 410,257 412,254 413,250 415,246 417,243 418,239 420,235 422,232 423,228 425,225 427,222 429,218 430,215 432,212 434,209 435,207 437,204 439,202 440,200 442,198 444,197 445,195 447,194 449,193 451,193 452,192 454,192 456,192 457,192 459,192 461,193 462,193 464,194 466,195 468,196 469,196 471,197 473,198 474,200 476,201 478,202 479,204 481,205 483,207 484,208 486,210 488,212 490,214 491,216 493,217 495,219 496,221 498,222 500,224 501,225 503,227 505,228 506,229 508,230 510,231 512,232 513,233 515,234 517,235 518,235 520,236 522,237 523,238 525,239 527,239 528,240 530,240 532,241 534,241 535,242 537,242 539,242 540,243 542,243 544,243 545,243 547,242 549,242 550,242 552,241 554,240 556,239 557,238 559,236 561,234 562,232 564,230 566,228 567,226 569,223 571,220 573,217 574,214 576,211 578,208 579,205 581,201 583,198 584,194 586,191 588,187 589,184 591,180 593,176 595,172 596,168 598,164 600,159 601,155 603,151 605,146 606,142 608,137 610,133 611,129 613,125 615,121 617,118 618,114 620,111 622,109 623,106 625,104 627,102 628,100 630,98 632,97 633,96 635,94 637,93 639,93 640,92 642,92 644,92 645,92 647,92 649,93 650,94 652,95 654,97 655,99 657,101 659,104 661,107 662,111 664,115 666,119 667,123 669,128 671,132 672,137 674,142 676,147 677,153 679,158 681,164 683,169 684,175 686,181 688,187 689,193 691,200 693,206 694,212 696,219 698,226 700,233 701,240 703,247 705,254 706,261 708,268 710,275 711,282 713,289 715,296 716,303 718,309 720,316 722,322 723,329 725,335 727,341 728,346 730,352 732,357 733,363 735,368 737,373 738,379 740,384 742,389 744,393 745,398 747,403 749,407 750,412 752,416 754,420 755,424 757,427 759,431 760,434 762,437 764,440 766,442 767,445 769,447 771,449 772,451 774,452 776,454 777,455 779,456 781,458 782,459 784,460 786,461 788,461 789,462 791,463 793,464 794,464 796,465 798,465 799,466 801,466 803,467 804,467 806,468 808,468 810,469 811,469 813,469 815,469 816,470 818,470 820,470 821,470 823,470 825,470 827,470 828,471 830,471 832,471 833,471 835,471 837,471 838,471 840,472 842,472 843,472 845,472 847,472 849,472 850,472 852,472 854,472 855,472 857,472 859,472 860,472 862,472 864,472 865,472 867,472 869,472 871,472 872,472 874,472 876,472 877,472 879,472 881,472 882,472 884,472 886,472 887,472 889,472 891,472 893,472 894,472 896,472 898,472 899,472 901,472 903,472 904,472 906,472 908,472 909,472 911,472 913,472 915,472 916,472 918,472 920,472 921,472 923,472 925,472 926,472 928,472 930,472 932,472 932,472 87,472 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="2" points="423,472 423,53 "/>
<text x="842" y="250" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
t distribution
</text>
<text x="842" y="265" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
t statistic
</text>
<rect x="812" y="250" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="812,270 832,270 "/>
</svg>

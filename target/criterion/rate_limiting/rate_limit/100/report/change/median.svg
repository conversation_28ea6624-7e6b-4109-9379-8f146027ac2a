<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="437" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,437 86,437 "/>
<text x="77" y="382" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,382 86,382 "/>
<text x="77" y="326" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,326 86,326 "/>
<text x="77" y="271" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,271 86,271 "/>
<text x="77" y="216" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,216 86,216 "/>
<text x="77" y="161" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,161 86,161 "/>
<text x="77" y="105" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,105 86,105 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="177" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="177,473 177,478 "/>
<text x="315" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="315,473 315,478 "/>
<text x="452" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="452,473 452,478 "/>
<text x="590" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="590,473 590,478 "/>
<text x="728" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="728,473 728,478 "/>
<text x="866" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="866,473 866,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,471 95,470 97,470 98,469 100,469 102,468 103,468 105,467 107,467 109,466 110,466 112,465 114,464 115,464 117,463 119,463 120,462 122,461 124,461 125,460 127,459 129,459 131,458 132,457 134,456 136,455 137,455 139,454 141,453 142,452 144,451 146,450 147,449 149,448 151,447 153,446 154,445 156,444 158,443 159,442 161,441 163,440 164,439 166,438 168,437 169,435 171,434 173,433 175,431 176,430 178,428 180,427 181,426 183,424 185,422 186,421 188,419 190,417 191,416 193,414 195,412 197,410 198,408 200,406 202,404 203,402 205,400 207,397 208,395 210,393 212,390 214,388 215,385 217,382 219,379 220,376 222,374 224,371 225,368 227,365 229,362 230,358 232,355 234,352 236,349 237,346 239,343 241,340 242,337 244,335 246,332 247,329 249,326 251,323 252,320 254,317 256,314 258,311 259,308 261,305 263,302 264,299 266,295 268,291 269,288 271,284 273,280 274,276 276,272 278,268 280,264 281,260 283,256 285,252 286,248 288,244 290,240 291,237 293,233 295,229 296,226 298,222 300,219 302,215 303,212 305,208 307,205 308,201 310,198 312,194 313,190 315,186 317,183 318,179 320,175 322,171 324,167 325,163 327,159 329,155 330,151 332,147 334,144 335,140 337,137 339,133 341,130 342,127 344,124 346,121 347,118 349,115 351,111 352,108 354,105 356,101 357,98 359,94 361,91 363,87 364,83 366,79 368,75 369,71 371,68 373,64 374,61 376,59 378,57 379,55 381,54 383,53 385,54 386,54 388,56 390,58 391,61 393,64 395,68 396,72 398,76 400,80 401,85 403,90 405,94 407,99 408,103 410,107 412,111 413,114 415,117 417,120 418,122 420,124 422,125 423,127 425,128 427,129 429,129 430,130 432,130 434,131 435,131 437,131 439,132 440,132 442,132 444,131 445,131 447,131 449,130 451,129 452,128 454,126 456,124 457,123 459,121 461,119 462,117 464,115 466,114 468,113 469,112 471,111 473,112 474,113 476,114 478,116 479,119 481,123 483,127 484,131 486,136 488,141 490,146 491,151 493,157 495,162 496,166 498,171 500,175 501,178 503,181 505,184 506,186 508,188 510,189 512,190 513,191 515,192 517,193 518,193 520,194 522,195 523,197 525,199 527,201 528,203 530,206 532,208 534,212 535,215 537,219 539,222 540,226 542,229 544,233 545,236 547,239 549,242 550,245 552,247 554,249 556,251 557,252 559,254 561,255 562,256 564,256 566,257 567,257 569,258 571,258 573,258 574,259 576,259 578,260 579,260 581,261 583,262 584,262 586,263 588,264 589,265 591,266 593,267 595,268 596,269 598,270 600,271 601,273 603,274 605,276 606,277 608,279 610,282 611,284 613,287 615,289 617,293 618,296 620,299 622,303 623,307 625,311 627,315 628,319 630,323 632,327 633,330 635,334 637,337 639,340 640,343 642,346 644,348 645,350 647,352 649,354 650,355 652,356 654,357 655,358 657,359 659,360 661,361 662,362 664,363 666,364 667,364 669,365 671,366 672,367 674,368 676,370 677,371 679,372 681,373 683,374 684,376 686,377 688,378 689,380 691,381 693,382 694,384 696,385 698,386 700,388 701,389 703,390 705,392 706,393 708,394 710,395 711,397 713,398 715,399 716,400 718,401 720,402 722,403 723,404 725,405 727,406 728,407 730,408 732,409 733,410 735,411 737,412 738,413 740,414 742,415 744,416 745,417 747,418 749,419 750,420 752,421 754,422 755,423 757,424 759,425 760,425 762,426 764,427 766,428 767,429 769,430 771,431 772,432 774,433 776,434 777,435 779,436 781,437 782,438 784,439 786,440 788,441 789,441 791,442 793,443 794,443 796,444 798,444 799,444 801,444 803,445 804,445 806,445 808,445 810,445 811,444 813,444 815,444 816,444 818,444 820,444 821,444 823,444 825,444 827,445 828,445 830,445 832,446 833,447 835,447 837,448 838,449 840,450 842,451 843,451 845,452 847,453 849,454 850,455 852,456 854,457 855,458 857,458 859,459 860,460 862,460 864,461 865,461 867,462 869,462 871,463 872,463 874,463 876,464 877,464 879,465 881,465 882,465 884,466 886,466 887,466 889,467 891,467 893,467 894,468 896,468 898,468 899,468 901,468 903,468 904,468 906,468 908,468 909,468 911,468 913,468 915,468 916,468 918,468 920,468 921,468 923,468 925,468 926,468 928,469 930,469 932,469 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,439 166,438 168,437 169,435 171,434 173,433 175,431 176,430 178,428 180,427 181,426 183,424 185,422 186,421 188,419 190,417 191,416 193,414 195,412 197,410 198,408 200,406 202,404 203,402 205,400 207,397 208,395 210,393 212,390 214,388 215,385 217,382 219,379 220,376 222,374 224,371 225,368 227,365 229,362 230,358 232,355 234,352 236,349 237,346 239,343 241,340 242,337 244,335 246,332 247,329 249,326 251,323 252,320 254,317 256,314 258,311 259,308 261,305 263,302 264,299 266,295 268,291 269,288 271,284 273,280 274,276 276,272 278,268 280,264 281,260 283,256 285,252 286,248 288,244 290,240 291,237 293,233 295,229 296,226 298,222 300,219 302,215 303,212 305,208 307,205 308,201 310,198 312,194 313,190 315,186 317,183 318,179 320,175 322,171 324,167 325,163 327,159 329,155 330,151 332,147 334,144 335,140 337,137 339,133 341,130 342,127 344,124 346,121 347,118 349,115 351,111 352,108 354,105 356,101 357,98 359,94 361,91 363,87 364,83 366,79 368,75 369,71 371,68 373,64 374,61 376,59 378,57 379,55 381,54 383,53 385,54 386,54 388,56 390,58 391,61 393,64 395,68 396,72 398,76 400,80 401,85 403,90 405,94 407,99 408,103 410,107 412,111 413,114 415,117 417,120 418,122 420,124 422,125 423,127 425,128 427,129 429,129 430,130 432,130 434,131 435,131 437,131 439,132 440,132 442,132 444,131 445,131 447,131 449,130 451,129 452,128 454,126 456,124 457,123 459,121 461,119 462,117 464,115 466,114 468,113 469,112 471,111 473,112 474,113 476,114 478,116 479,119 481,123 483,127 484,131 486,136 488,141 490,146 491,151 493,157 495,162 496,166 498,171 500,175 501,178 503,181 505,184 506,186 508,188 510,189 512,190 513,191 515,192 517,193 518,193 520,194 522,195 523,197 525,199 527,201 528,203 530,206 532,208 534,212 535,215 537,219 539,222 540,226 542,229 544,233 545,236 547,239 549,242 550,245 552,247 554,249 556,251 557,252 559,254 561,255 562,256 564,256 566,257 567,257 569,258 571,258 573,258 574,259 576,259 578,260 579,260 581,261 583,262 584,262 586,263 588,264 589,265 591,266 593,267 595,268 596,269 598,270 600,271 601,273 603,274 605,276 606,277 608,279 610,282 611,284 613,287 615,289 617,293 618,296 620,299 622,303 623,307 625,311 627,315 628,319 630,323 632,327 633,330 635,334 637,337 639,340 640,343 642,346 644,348 645,350 647,352 649,354 650,355 652,356 654,357 655,358 657,359 659,360 661,361 662,362 664,363 666,364 667,364 669,365 671,366 672,367 674,368 676,370 677,371 679,372 681,373 683,374 684,376 686,377 688,378 689,380 691,381 693,382 694,384 696,385 698,386 700,388 701,389 703,390 705,392 706,393 708,394 710,395 711,397 713,398 715,399 716,400 718,401 720,402 722,403 723,404 725,405 727,406 728,407 730,408 732,409 733,410 735,411 737,412 738,413 740,414 742,415 744,416 745,417 747,418 749,419 750,420 752,421 754,422 755,423 757,424 759,425 760,425 762,426 764,427 766,428 767,429 769,430 771,431 772,432 774,433 776,434 777,435 779,436 781,437 782,438 784,439 786,440 788,441 789,441 791,442 793,443 794,443 796,444 798,444 799,444 801,444 803,445 804,445 806,445 808,445 810,445 811,444 813,444 815,444 816,444 818,444 820,444 821,444 823,444 825,444 827,445 828,445 830,445 832,446 833,447 835,447 837,448 838,449 840,450 842,451 843,451 845,452 847,453 849,454 850,455 852,456 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="426,473 426,128 "/>
<rect x="315" y="53" width="551" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

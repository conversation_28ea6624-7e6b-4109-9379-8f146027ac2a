<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="437" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,437 86,437 "/>
<text x="77" y="401" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,401 86,401 "/>
<text x="77" y="364" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,364 86,364 "/>
<text x="77" y="328" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,328 86,328 "/>
<text x="77" y="292" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,292 86,292 "/>
<text x="77" y="256" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,256 86,256 "/>
<text x="77" y="220" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.035
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,220 86,220 "/>
<text x="77" y="184" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,184 86,184 "/>
<text x="77" y="148" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.045
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,148 86,148 "/>
<text x="77" y="112" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,112 86,112 "/>
<text x="77" y="76" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.055
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,76 86,76 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="139" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="139,473 139,478 "/>
<text x="244" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="244,473 244,478 "/>
<text x="349" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="349,473 349,478 "/>
<text x="454" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="454,473 454,478 "/>
<text x="559" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="559,473 559,478 "/>
<text x="664" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="664,473 664,478 "/>
<text x="769" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="769,473 769,478 "/>
<text x="874" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="874,473 874,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,472 98,471 100,471 102,471 103,470 105,470 107,469 109,468 110,467 112,465 114,464 115,462 117,460 119,458 120,455 122,452 124,448 125,444 127,440 129,435 131,430 132,425 134,419 136,413 137,406 139,400 141,393 142,386 144,380 146,373 147,366 149,360 151,355 153,349 154,345 156,341 158,338 159,336 161,334 163,334 164,334 166,335 168,338 169,341 171,344 173,349 175,354 176,360 178,366 180,372 181,379 183,385 185,392 186,399 188,406 190,412 191,418 193,424 195,430 197,435 198,439 200,444 202,448 203,451 205,454 207,457 208,459 210,461 212,463 214,464 215,465 217,465 219,466 220,466 222,465 224,464 225,463 227,462 229,460 230,457 232,454 234,451 236,447 237,442 239,437 241,432 242,425 244,419 246,411 247,404 249,396 251,387 252,379 254,371 256,362 258,354 259,347 261,340 263,333 264,328 266,324 268,320 269,318 271,317 273,318 274,319 276,322 278,326 280,331 281,337 283,343 285,350 286,358 288,366 290,374 291,381 293,389 295,396 296,403 298,409 300,414 302,419 303,422 305,424 307,426 308,426 310,425 312,423 313,419 315,414 317,409 318,402 320,394 322,385 324,375 325,364 327,353 329,341 330,329 332,317 334,305 335,294 337,283 339,273 341,264 342,256 344,249 346,244 347,241 349,239 351,239 352,241 354,244 356,249 357,254 359,261 361,269 363,278 364,286 366,295 368,304 369,312 371,319 373,326 374,331 376,336 378,338 379,340 381,340 383,339 385,336 386,333 388,328 390,322 391,316 393,310 395,303 396,297 398,291 400,285 401,280 403,276 405,272 407,270 408,268 410,267 412,267 413,268 415,269 417,270 418,271 420,271 422,272 423,271 425,270 427,267 429,263 430,258 432,252 434,244 435,235 437,225 439,215 440,204 442,192 444,181 445,170 447,160 449,150 451,142 452,135 454,130 456,126 457,123 459,123 461,123 462,125 464,128 466,131 468,136 469,140 471,144 473,149 474,152 476,156 478,158 479,160 481,160 483,160 484,160 486,158 488,157 490,155 491,153 493,151 495,149 496,148 498,147 500,147 501,147 503,148 505,149 506,150 508,152 510,153 512,154 513,154 515,154 517,153 518,151 520,149 522,145 523,141 525,136 527,131 528,125 530,119 532,114 534,108 535,103 537,99 539,96 540,93 542,92 544,92 545,92 547,94 549,96 550,99 552,103 554,107 556,111 557,115 559,118 561,122 562,125 564,128 566,131 567,133 569,135 571,136 573,137 574,138 576,139 578,139 579,139 581,139 583,139 584,139 586,138 588,137 589,136 591,134 593,132 595,130 596,128 598,125 600,122 601,119 603,117 605,114 606,111 608,109 610,108 611,107 613,106 615,106 617,107 618,108 620,110 622,112 623,115 625,118 627,121 628,125 630,128 632,132 633,136 635,139 637,143 639,146 640,149 642,152 644,155 645,158 647,161 649,163 650,165 652,167 654,168 655,170 657,171 659,172 661,173 662,173 664,173 666,174 667,174 669,175 671,175 672,176 674,177 676,178 677,180 679,181 681,183 683,186 684,188 686,191 688,194 689,197 691,201 693,204 694,208 696,211 698,214 700,217 701,221 703,224 705,226 706,229 708,231 710,234 711,236 713,238 715,240 716,241 718,243 720,244 722,245 723,247 725,248 727,249 728,250 730,252 732,253 733,255 735,257 737,259 738,261 740,263 742,266 744,268 745,271 747,274 749,277 750,280 752,282 754,285 755,288 757,291 759,294 760,297 762,299 764,302 766,304 767,306 769,309 771,311 772,313 774,315 776,316 777,318 779,320 781,322 782,324 784,325 786,327 788,329 789,331 791,333 793,335 794,337 796,339 798,341 799,344 801,346 803,348 804,350 806,352 808,355 810,357 811,359 813,361 815,363 816,365 818,367 820,369 821,371 823,373 825,375 827,377 828,378 830,380 832,382 833,384 835,386 837,388 838,390 840,392 842,394 843,395 845,397 847,399 849,401 850,403 852,404 854,406 855,408 857,409 859,411 860,412 862,413 864,415 865,416 867,417 869,418 871,419 872,420 874,421 876,422 877,424 879,425 881,426 882,427 884,428 886,429 887,430 889,431 891,432 893,433 894,434 896,435 898,436 899,437 901,438 903,439 904,440 906,441 908,442 909,443 911,444 913,444 915,445 916,446 918,447 920,447 921,448 923,449 925,450 926,450 928,451 930,452 932,452 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,334 166,335 168,338 169,341 171,344 173,349 175,354 176,360 178,366 180,372 181,379 183,385 185,392 186,399 188,406 190,412 191,418 193,424 195,430 197,435 198,439 200,444 202,448 203,451 205,454 207,457 208,459 210,461 212,463 214,464 215,465 217,465 219,466 220,466 222,465 224,464 225,463 227,462 229,460 230,457 232,454 234,451 236,447 237,442 239,437 241,432 242,425 244,419 246,411 247,404 249,396 251,387 252,379 254,371 256,362 258,354 259,347 261,340 263,333 264,328 266,324 268,320 269,318 271,317 273,318 274,319 276,322 278,326 280,331 281,337 283,343 285,350 286,358 288,366 290,374 291,381 293,389 295,396 296,403 298,409 300,414 302,419 303,422 305,424 307,426 308,426 310,425 312,423 313,419 315,414 317,409 318,402 320,394 322,385 324,375 325,364 327,353 329,341 330,329 332,317 334,305 335,294 337,283 339,273 341,264 342,256 344,249 346,244 347,241 349,239 351,239 352,241 354,244 356,249 357,254 359,261 361,269 363,278 364,286 366,295 368,304 369,312 371,319 373,326 374,331 376,336 378,338 379,340 381,340 383,339 385,336 386,333 388,328 390,322 391,316 393,310 395,303 396,297 398,291 400,285 401,280 403,276 405,272 407,270 408,268 410,267 412,267 413,268 415,269 417,270 418,271 420,271 422,272 423,271 425,270 427,267 429,263 430,258 432,252 434,244 435,235 437,225 439,215 440,204 442,192 444,181 445,170 447,160 449,150 451,142 452,135 454,130 456,126 457,123 459,123 461,123 462,125 464,128 466,131 468,136 469,140 471,144 473,149 474,152 476,156 478,158 479,160 481,160 483,160 484,160 486,158 488,157 490,155 491,153 493,151 495,149 496,148 498,147 500,147 501,147 503,148 505,149 506,150 508,152 510,153 512,154 513,154 515,154 517,153 518,151 520,149 522,145 523,141 525,136 527,131 528,125 530,119 532,114 534,108 535,103 537,99 539,96 540,93 542,92 544,92 545,92 547,94 549,96 550,99 552,103 554,107 556,111 557,115 559,118 561,122 562,125 564,128 566,131 567,133 569,135 571,136 573,137 574,138 576,139 578,139 579,139 581,139 583,139 584,139 586,138 588,137 589,136 591,134 593,132 595,130 596,128 598,125 600,122 601,119 603,117 605,114 606,111 608,109 610,108 611,107 613,106 615,106 617,107 618,108 620,110 622,112 623,115 625,118 627,121 628,125 630,128 632,132 633,136 635,139 637,143 639,146 640,149 642,152 644,155 645,158 647,161 649,163 650,165 652,167 654,168 655,170 657,171 659,172 661,173 662,173 664,173 666,174 667,174 669,175 671,175 672,176 674,177 676,178 677,180 679,181 681,183 683,186 684,188 686,191 688,194 689,197 691,201 693,204 694,208 696,211 698,214 700,217 701,221 703,224 705,226 706,229 708,231 710,234 711,236 713,238 715,240 716,241 718,243 720,244 722,245 723,247 725,248 727,249 728,250 730,252 732,253 733,255 735,257 737,259 738,261 740,263 742,266 744,268 745,271 747,274 749,277 750,280 752,282 754,285 755,288 757,291 759,294 760,297 762,299 764,302 766,304 767,306 769,309 771,311 772,313 774,315 776,316 777,318 779,320 781,322 782,324 784,325 786,327 788,329 789,331 791,333 793,335 794,337 796,339 798,341 799,344 801,346 803,348 804,350 806,352 808,355 810,357 811,359 813,361 815,363 816,365 818,367 820,369 821,371 823,373 825,375 827,377 828,378 830,380 832,382 833,384 835,386 837,388 838,390 840,392 842,394 843,395 845,397 847,399 849,401 850,403 852,404 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="577,473 577,139 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

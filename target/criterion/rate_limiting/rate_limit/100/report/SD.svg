<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="456" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,456 86,456 "/>
<text x="77" y="416" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,416 86,416 "/>
<text x="77" y="375" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,375 86,375 "/>
<text x="77" y="335" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,335 86,335 "/>
<text x="77" y="295" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,295 86,295 "/>
<text x="77" y="254" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,254 86,254 "/>
<text x="77" y="214" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,214 86,214 "/>
<text x="77" y="174" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,174 86,174 "/>
<text x="77" y="133" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,133 86,133 "/>
<text x="77" y="93" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,93 86,93 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="161" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="161,473 161,478 "/>
<text x="275" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="275,473 275,478 "/>
<text x="390" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="390,473 390,478 "/>
<text x="504" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="504,473 504,478 "/>
<text x="619" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="619,473 619,478 "/>
<text x="733" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="733,473 733,478 "/>
<text x="847" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="847,473 847,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,470 95,469 97,468 98,468 100,467 102,466 103,465 105,464 107,463 109,463 110,462 112,461 114,460 115,459 117,458 119,457 120,456 122,455 124,454 125,452 127,451 129,450 131,449 132,448 134,447 136,446 137,444 139,443 141,442 142,441 144,440 146,439 147,437 149,436 151,435 153,434 154,432 156,431 158,430 159,429 161,427 163,426 164,424 166,423 168,421 169,420 171,418 173,417 175,415 176,414 178,412 180,410 181,409 183,407 185,405 186,403 188,402 190,400 191,398 193,396 195,394 197,392 198,390 200,388 202,387 203,385 205,383 207,381 208,379 210,377 212,375 214,373 215,371 217,369 219,367 220,365 222,363 224,361 225,359 227,358 229,356 230,354 232,352 234,350 236,348 237,346 239,344 241,341 242,339 244,337 246,335 247,333 249,331 251,328 252,326 254,324 256,321 258,319 259,317 261,314 263,312 264,309 266,307 268,305 269,302 271,300 273,297 274,295 276,293 278,290 280,288 281,286 283,284 285,281 286,279 288,277 290,275 291,273 293,270 295,268 296,266 298,264 300,262 302,260 303,258 305,256 307,254 308,252 310,249 312,247 313,245 315,243 317,241 318,239 320,237 322,235 324,234 325,232 327,230 329,228 330,226 332,224 334,222 335,220 337,219 339,217 341,215 342,213 344,212 346,210 347,208 349,207 351,205 352,203 354,202 356,200 357,199 359,197 361,196 363,194 364,193 366,191 368,190 369,188 371,187 373,185 374,184 376,182 378,181 379,179 381,178 383,176 385,174 386,173 388,171 390,170 391,168 393,166 395,165 396,163 398,161 400,160 401,158 403,157 405,155 407,154 408,152 410,151 412,149 413,148 415,147 417,146 418,144 420,143 422,142 423,141 425,140 427,139 429,138 430,137 432,135 434,134 435,133 437,132 439,131 440,130 442,129 444,128 445,126 447,125 449,124 451,123 452,122 454,121 456,120 457,119 459,117 461,116 462,115 464,114 466,113 468,112 469,111 471,110 473,109 474,108 476,107 478,106 479,105 481,104 483,104 484,103 486,102 488,101 490,100 491,99 493,99 495,98 496,97 498,97 500,96 501,95 503,95 505,95 506,94 508,94 510,94 512,94 513,94 515,94 517,94 518,94 520,95 522,95 523,95 525,96 527,97 528,97 530,98 532,99 534,100 535,101 537,102 539,103 540,104 542,105 544,106 545,107 547,108 549,109 550,110 552,111 554,113 556,114 557,115 559,116 561,118 562,119 564,120 566,122 567,123 569,124 571,126 573,127 574,129 576,130 578,132 579,133 581,135 583,137 584,138 586,140 588,142 589,143 591,145 593,147 595,148 596,150 598,151 600,153 601,155 603,156 605,158 606,159 608,161 610,162 611,163 613,165 615,166 617,168 618,169 620,171 622,172 623,174 625,175 627,177 628,178 630,180 632,182 633,183 635,185 637,187 639,189 640,190 642,192 644,194 645,196 647,198 649,200 650,202 652,204 654,206 655,208 657,210 659,212 661,215 662,217 664,219 666,221 667,223 669,225 671,228 672,230 674,232 676,234 677,237 679,239 681,241 683,243 684,246 686,248 688,250 689,253 691,255 693,257 694,260 696,262 698,264 700,266 701,269 703,271 705,273 706,276 708,278 710,280 711,282 713,285 715,287 716,289 718,291 720,293 722,296 723,298 725,300 727,302 728,304 730,306 732,308 733,310 735,312 737,314 738,316 740,318 742,320 744,322 745,324 747,326 749,328 750,330 752,332 754,334 755,336 757,338 759,339 760,341 762,343 764,345 766,347 767,349 769,350 771,352 772,354 774,356 776,358 777,360 779,361 781,363 782,365 784,367 786,369 788,370 789,372 791,374 793,376 794,377 796,379 798,381 799,383 801,384 803,386 804,388 806,389 808,391 810,392 811,394 813,396 815,397 816,399 818,400 820,402 821,403 823,405 825,407 827,408 828,410 830,411 832,413 833,414 835,416 837,417 838,419 840,420 842,422 843,423 845,425 847,426 849,428 850,429 852,430 854,432 855,433 857,434 859,436 860,437 862,438 864,439 865,440 867,441 869,442 871,443 872,444 874,445 876,446 877,447 879,448 881,449 882,450 884,450 886,451 887,452 889,453 891,454 893,454 894,455 896,456 898,457 899,457 901,458 903,459 904,460 906,460 908,461 909,462 911,463 913,463 915,464 916,465 918,466 920,466 921,467 923,468 925,468 926,469 928,470 930,470 932,471 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,424 166,423 168,421 169,420 171,418 173,417 175,415 176,414 178,412 180,410 181,409 183,407 185,405 186,403 188,402 190,400 191,398 193,396 195,394 197,392 198,390 200,388 202,387 203,385 205,383 207,381 208,379 210,377 212,375 214,373 215,371 217,369 219,367 220,365 222,363 224,361 225,359 227,358 229,356 230,354 232,352 234,350 236,348 237,346 239,344 241,341 242,339 244,337 246,335 247,333 249,331 251,328 252,326 254,324 256,321 258,319 259,317 261,314 263,312 264,309 266,307 268,305 269,302 271,300 273,297 274,295 276,293 278,290 280,288 281,286 283,284 285,281 286,279 288,277 290,275 291,273 293,270 295,268 296,266 298,264 300,262 302,260 303,258 305,256 307,254 308,252 310,249 312,247 313,245 315,243 317,241 318,239 320,237 322,235 324,234 325,232 327,230 329,228 330,226 332,224 334,222 335,220 337,219 339,217 341,215 342,213 344,212 346,210 347,208 349,207 351,205 352,203 354,202 356,200 357,199 359,197 361,196 363,194 364,193 366,191 368,190 369,188 371,187 373,185 374,184 376,182 378,181 379,179 381,178 383,176 385,174 386,173 388,171 390,170 391,168 393,166 395,165 396,163 398,161 400,160 401,158 403,157 405,155 407,154 408,152 410,151 412,149 413,148 415,147 417,146 418,144 420,143 422,142 423,141 425,140 427,139 429,138 430,137 432,135 434,134 435,133 437,132 439,131 440,130 442,129 444,128 445,126 447,125 449,124 451,123 452,122 454,121 456,120 457,119 459,117 461,116 462,115 464,114 466,113 468,112 469,111 471,110 473,109 474,108 476,107 478,106 479,105 481,104 483,104 484,103 486,102 488,101 490,100 491,99 493,99 495,98 496,97 498,97 500,96 501,95 503,95 505,95 506,94 508,94 510,94 512,94 513,94 515,94 517,94 518,94 520,95 522,95 523,95 525,96 527,97 528,97 530,98 532,99 534,100 535,101 537,102 539,103 540,104 542,105 544,106 545,107 547,108 549,109 550,110 552,111 554,113 556,114 557,115 559,116 561,118 562,119 564,120 566,122 567,123 569,124 571,126 573,127 574,129 576,130 578,132 579,133 581,135 583,137 584,138 586,140 588,142 589,143 591,145 593,147 595,148 596,150 598,151 600,153 601,155 603,156 605,158 606,159 608,161 610,162 611,163 613,165 615,166 617,168 618,169 620,171 622,172 623,174 625,175 627,177 628,178 630,180 632,182 633,183 635,185 637,187 639,189 640,190 642,192 644,194 645,196 647,198 649,200 650,202 652,204 654,206 655,208 657,210 659,212 661,215 662,217 664,219 666,221 667,223 669,225 671,228 672,230 674,232 676,234 677,237 679,239 681,241 683,243 684,246 686,248 688,250 689,253 691,255 693,257 694,260 696,262 698,264 700,266 701,269 703,271 705,273 706,276 708,278 710,280 711,282 713,285 715,287 716,289 718,291 720,293 722,296 723,298 725,300 727,302 728,304 730,306 732,308 733,310 735,312 737,314 738,316 740,318 742,320 744,322 745,324 747,326 749,328 750,330 752,332 754,334 755,336 757,338 759,339 760,341 762,343 764,345 766,347 767,349 769,350 771,352 772,354 774,356 776,358 777,360 779,361 781,363 782,365 784,367 786,369 788,370 789,372 791,374 793,376 794,377 796,379 798,381 799,383 801,384 803,386 804,388 806,389 808,391 810,392 811,394 813,396 815,397 816,399 818,400 820,402 821,403 823,405 825,407 827,408 828,410 830,411 832,413 833,414 835,416 837,417 838,419 840,420 842,422 843,423 845,425 847,426 849,428 850,429 852,430 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="538,473 538,102 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

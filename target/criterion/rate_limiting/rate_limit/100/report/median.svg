<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="436" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,436 86,436 "/>
<text x="77" y="399" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,399 86,399 "/>
<text x="77" y="362" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,362 86,362 "/>
<text x="77" y="325" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,325 86,325 "/>
<text x="77" y="287" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,287 86,287 "/>
<text x="77" y="250" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,250 86,250 "/>
<text x="77" y="213" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,213 86,213 "/>
<text x="77" y="176" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,176 86,176 "/>
<text x="77" y="139" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,139 86,139 "/>
<text x="77" y="101" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,101 86,101 "/>
<text x="77" y="64" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,64 86,64 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="142" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="142,473 142,478 "/>
<text x="235" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="235,473 235,478 "/>
<text x="328" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="328,473 328,478 "/>
<text x="420" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="420,473 420,478 "/>
<text x="513" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="513,473 513,478 "/>
<text x="605" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="605,473 605,478 "/>
<text x="698" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="698,473 698,478 "/>
<text x="790" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="790,473 790,478 "/>
<text x="883" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="883,473 883,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,460 88,460 90,459 92,459 93,458 95,458 97,458 98,458 100,458 102,458 103,458 105,459 107,459 109,459 110,460 112,461 114,461 115,462 117,463 119,463 120,464 122,464 124,465 125,465 127,466 129,466 131,466 132,466 134,465 136,465 137,464 139,464 141,463 142,462 144,461 146,460 147,459 149,458 151,456 153,455 154,454 156,453 158,452 159,451 161,450 163,449 164,449 166,448 168,448 169,448 171,448 173,449 175,449 176,449 178,450 180,450 181,451 183,451 185,452 186,452 188,453 190,453 191,453 193,452 195,452 197,451 198,450 200,448 202,446 203,444 205,442 207,439 208,435 210,432 212,428 214,423 215,419 217,414 219,409 220,403 222,398 224,392 225,387 227,381 229,375 230,369 232,364 234,358 236,352 237,346 239,341 241,335 242,329 244,323 246,317 247,311 249,305 251,298 252,291 254,284 256,276 258,267 259,258 261,249 263,239 264,229 266,218 268,206 269,195 271,183 273,172 274,160 276,149 278,138 280,128 281,119 283,111 285,104 286,98 288,94 290,92 291,92 293,93 295,96 296,101 298,107 300,116 302,125 303,136 305,148 307,161 308,174 310,188 312,201 313,215 315,227 317,240 318,251 320,261 322,270 324,278 325,284 327,289 329,292 330,294 332,295 334,295 335,293 337,292 339,289 341,287 342,284 344,281 346,279 347,278 349,277 351,276 352,277 354,278 356,281 357,283 359,287 361,290 363,294 364,297 366,301 368,304 369,306 371,307 373,307 374,306 376,304 378,301 379,296 381,290 383,284 385,276 386,269 388,260 390,253 391,245 393,238 395,232 396,228 398,224 400,223 401,223 403,225 405,228 407,234 408,241 410,249 412,259 413,270 415,281 417,294 418,306 420,319 422,331 423,343 425,355 427,366 429,375 430,384 432,392 434,399 435,404 437,408 439,411 440,413 442,414 444,413 445,412 447,410 449,407 451,404 452,400 454,395 456,390 457,386 459,381 461,376 462,371 464,367 466,364 468,361 469,358 471,357 473,356 474,356 476,356 478,358 479,360 481,362 483,365 484,369 486,373 488,378 490,382 491,387 493,392 495,397 496,401 498,406 500,410 501,414 503,417 505,420 506,423 508,425 510,427 512,428 513,428 515,428 517,427 518,426 520,425 522,423 523,420 525,418 527,414 528,411 530,408 532,404 534,401 535,397 537,394 539,390 540,387 542,384 544,382 545,380 547,377 549,376 550,374 552,373 554,372 556,372 557,372 559,372 561,373 562,373 564,374 566,376 567,377 569,379 571,382 573,384 574,386 576,389 578,392 579,395 581,399 583,402 584,405 586,409 588,413 589,416 591,420 593,423 595,427 596,430 598,433 600,436 601,439 603,442 605,444 606,446 608,448 610,449 611,450 613,451 615,452 617,452 618,452 620,452 622,451 623,450 625,449 627,448 628,447 630,446 632,445 633,443 635,442 637,441 639,440 640,438 642,437 644,437 645,436 647,436 649,435 650,435 652,436 654,436 655,437 657,438 659,439 661,440 662,442 664,443 666,445 667,447 669,448 671,450 672,452 674,454 676,455 677,457 679,458 681,460 683,461 684,462 686,462 688,463 689,463 691,463 693,463 694,463 696,463 698,463 700,462 701,461 703,460 705,459 706,458 708,457 710,456 711,455 713,455 715,454 716,453 718,452 720,452 722,451 723,451 725,451 727,451 728,451 730,452 732,452 733,453 735,454 737,455 738,455 740,456 742,457 744,458 745,459 747,460 749,461 750,462 752,462 754,463 755,463 757,464 759,464 760,464 762,464 764,464 766,464 767,464 769,464 771,464 772,464 774,464 776,464 777,463 779,463 781,463 782,463 784,464 786,464 788,464 789,464 791,464 793,465 794,465 796,465 798,466 799,466 801,467 803,467 804,467 806,468 808,468 810,468 811,468 813,468 815,468 816,468 818,468 820,468 821,468 823,468 825,467 827,467 828,466 830,465 832,465 833,464 835,463 837,462 838,462 840,461 842,460 843,459 845,459 847,458 849,458 850,457 852,457 854,457 855,457 857,457 859,457 860,457 862,458 864,458 865,459 867,459 869,460 871,461 872,462 874,462 876,463 877,464 879,464 881,465 882,466 884,466 886,467 887,467 889,467 891,468 893,468 894,469 896,469 898,469 899,469 901,470 903,470 904,470 906,470 908,471 909,471 911,471 913,471 915,471 916,472 918,472 920,472 921,472 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,449 166,448 168,448 169,448 171,448 173,449 175,449 176,449 178,450 180,450 181,451 183,451 185,452 186,452 188,453 190,453 191,453 193,452 195,452 197,451 198,450 200,448 202,446 203,444 205,442 207,439 208,435 210,432 212,428 214,423 215,419 217,414 219,409 220,403 222,398 224,392 225,387 227,381 229,375 230,369 232,364 234,358 236,352 237,346 239,341 241,335 242,329 244,323 246,317 247,311 249,305 251,298 252,291 254,284 256,276 258,267 259,258 261,249 263,239 264,229 266,218 268,206 269,195 271,183 273,172 274,160 276,149 278,138 280,128 281,119 283,111 285,104 286,98 288,94 290,92 291,92 293,93 295,96 296,101 298,107 300,116 302,125 303,136 305,148 307,161 308,174 310,188 312,201 313,215 315,227 317,240 318,251 320,261 322,270 324,278 325,284 327,289 329,292 330,294 332,295 334,295 335,293 337,292 339,289 341,287 342,284 344,281 346,279 347,278 349,277 351,276 352,277 354,278 356,281 357,283 359,287 361,290 363,294 364,297 366,301 368,304 369,306 371,307 373,307 374,306 376,304 378,301 379,296 381,290 383,284 385,276 386,269 388,260 390,253 391,245 393,238 395,232 396,228 398,224 400,223 401,223 403,225 405,228 407,234 408,241 410,249 412,259 413,270 415,281 417,294 418,306 420,319 422,331 423,343 425,355 427,366 429,375 430,384 432,392 434,399 435,404 437,408 439,411 440,413 442,414 444,413 445,412 447,410 449,407 451,404 452,400 454,395 456,390 457,386 459,381 461,376 462,371 464,367 466,364 468,361 469,358 471,357 473,356 474,356 476,356 478,358 479,360 481,362 483,365 484,369 486,373 488,378 490,382 491,387 493,392 495,397 496,401 498,406 500,410 501,414 503,417 505,420 506,423 508,425 510,427 512,428 513,428 515,428 517,427 518,426 520,425 522,423 523,420 525,418 527,414 528,411 530,408 532,404 534,401 535,397 537,394 539,390 540,387 542,384 544,382 545,380 547,377 549,376 550,374 552,373 554,372 556,372 557,372 559,372 561,373 562,373 564,374 566,376 567,377 569,379 571,382 573,384 574,386 576,389 578,392 579,395 581,399 583,402 584,405 586,409 588,413 589,416 591,420 593,423 595,427 596,430 598,433 600,436 601,439 603,442 605,444 606,446 608,448 610,449 611,450 613,451 615,452 617,452 618,452 620,452 622,451 623,450 625,449 627,448 628,447 630,446 632,445 633,443 635,442 637,441 639,440 640,438 642,437 644,437 645,436 647,436 649,435 650,435 652,436 654,436 655,437 657,438 659,439 661,440 662,442 664,443 666,445 667,447 669,448 671,450 672,452 674,454 676,455 677,457 679,458 681,460 683,461 684,462 686,462 688,463 689,463 691,463 693,463 694,463 696,463 698,463 700,462 701,461 703,460 705,459 706,458 708,457 710,456 711,455 713,455 715,454 716,453 718,452 720,452 722,451 723,451 725,451 727,451 728,451 730,452 732,452 733,453 735,454 737,455 738,455 740,456 742,457 744,458 745,459 747,460 749,461 750,462 752,462 754,463 755,463 757,464 759,464 760,464 762,464 764,464 766,464 767,464 769,464 771,464 772,464 774,464 776,464 777,463 779,463 781,463 782,463 784,464 786,464 788,464 789,464 791,464 793,465 794,465 796,465 798,466 799,466 801,467 803,467 804,467 806,468 808,468 810,468 811,468 813,468 815,468 816,468 818,468 820,468 821,468 823,468 825,467 827,467 828,466 830,465 832,465 833,464 835,463 837,462 838,462 840,461 842,460 843,459 845,459 847,458 849,458 850,457 852,457 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="354,473 354,278 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

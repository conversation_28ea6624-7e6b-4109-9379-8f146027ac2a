<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="440" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,440 86,440 "/>
<text x="77" y="400" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,400 86,400 "/>
<text x="77" y="359" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,359 86,359 "/>
<text x="77" y="319" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,319 86,319 "/>
<text x="77" y="278" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,278 86,278 "/>
<text x="77" y="238" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,238 86,238 "/>
<text x="77" y="197" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,197 86,197 "/>
<text x="77" y="157" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,157 86,157 "/>
<text x="77" y="116" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,116 86,116 "/>
<text x="77" y="75" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,75 86,75 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="122" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="122,473 122,478 "/>
<text x="235" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="235,473 235,478 "/>
<text x="348" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="348,473 348,478 "/>
<text x="461" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="461,473 461,478 "/>
<text x="574" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="574,473 574,478 "/>
<text x="687" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="687,473 687,478 "/>
<text x="800" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="800,473 800,478 "/>
<text x="913" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="913,473 913,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,471 95,471 97,470 98,470 100,470 102,469 103,469 105,469 107,468 109,468 110,468 112,467 114,467 115,466 117,465 119,465 120,464 122,463 124,462 125,461 127,459 129,458 131,456 132,454 134,452 136,450 137,447 139,445 141,442 142,439 144,436 146,433 147,430 149,428 151,425 153,422 154,420 156,418 158,416 159,414 161,413 163,413 164,413 166,413 168,414 169,415 171,416 173,418 175,421 176,423 178,426 180,429 181,432 183,435 185,438 186,441 188,443 190,445 191,447 193,449 195,450 197,451 198,451 200,452 202,451 203,451 205,450 207,449 208,448 210,447 212,446 214,445 215,444 217,443 219,442 220,441 222,441 224,441 225,440 227,441 229,441 230,441 232,442 234,442 236,443 237,443 239,444 241,444 242,444 244,443 246,443 247,442 249,440 251,438 252,436 254,433 256,430 258,426 259,422 261,418 263,414 264,410 266,405 268,401 269,396 271,392 273,388 274,384 276,380 278,377 280,375 281,372 283,370 285,369 286,367 288,367 290,366 291,365 293,365 295,365 296,365 298,366 300,366 302,366 303,366 305,367 307,367 308,367 310,367 312,367 313,367 315,367 317,366 318,366 320,365 322,365 324,364 325,364 327,363 329,362 330,362 332,361 334,360 335,359 337,358 339,356 341,355 342,354 344,353 346,351 347,350 349,349 351,348 352,347 354,346 356,345 357,344 359,344 361,344 363,344 364,344 366,345 368,346 369,347 371,349 373,352 374,354 376,357 378,361 379,364 381,368 383,372 385,376 386,381 388,385 390,389 391,393 393,397 395,401 396,404 398,407 400,409 401,411 403,412 405,412 407,412 408,412 410,410 412,409 413,406 415,403 417,400 418,397 420,393 422,389 423,386 425,382 427,379 429,376 430,373 432,371 434,370 435,369 437,369 439,369 440,371 442,372 444,375 445,377 447,380 449,384 451,387 452,390 454,393 456,395 457,396 459,397 461,397 462,395 464,392 466,388 468,382 469,375 471,366 473,355 474,343 476,330 478,315 479,299 481,282 483,264 484,246 486,227 488,209 490,192 491,175 493,159 495,144 496,131 498,120 500,110 501,103 503,97 505,94 506,92 508,93 510,95 512,99 513,105 515,112 517,120 518,129 520,138 522,149 523,159 525,169 527,180 528,190 530,199 532,209 534,218 535,226 537,233 539,240 540,247 542,252 544,257 545,262 547,266 549,269 550,272 552,274 554,276 556,278 557,279 559,280 561,280 562,280 564,280 566,279 567,278 569,277 571,275 573,273 574,271 576,269 578,267 579,265 581,263 583,261 584,259 586,257 588,256 589,256 591,255 593,255 595,256 596,257 598,258 600,260 601,262 603,265 605,267 606,270 608,273 610,275 611,278 613,280 615,282 617,284 618,286 620,287 622,288 623,289 625,289 627,289 628,288 630,288 632,287 633,286 635,285 637,283 639,282 640,281 642,280 644,279 645,279 647,279 649,279 650,280 652,282 654,283 655,286 657,289 659,292 661,296 662,300 664,304 666,309 667,313 669,318 671,323 672,328 674,332 676,337 677,341 679,345 681,348 683,352 684,355 686,358 688,360 689,363 691,365 693,367 694,369 696,370 698,372 700,373 701,374 703,375 705,376 706,376 708,377 710,377 711,378 713,378 715,378 716,378 718,377 720,377 722,376 723,376 725,375 727,375 728,374 730,374 732,374 733,373 735,373 737,374 738,374 740,375 742,376 744,378 745,379 747,381 749,384 750,386 752,389 754,392 755,395 757,398 759,402 760,405 762,408 764,411 766,414 767,417 769,420 771,422 772,424 774,426 776,428 777,430 779,432 781,433 782,434 784,435 786,436 788,437 789,438 791,439 793,439 794,440 796,441 798,441 799,442 801,442 803,443 804,444 806,444 808,445 810,445 811,446 813,446 815,447 816,448 818,448 820,448 821,449 823,449 825,450 827,450 828,450 830,450 832,450 833,450 835,450 837,450 838,450 840,450 842,450 843,449 845,449 847,449 849,449 850,449 852,449 854,449 855,449 857,450 859,450 860,450 862,451 864,452 865,452 867,453 869,454 871,455 872,456 874,457 876,458 877,459 879,460 881,461 882,462 884,463 886,463 887,464 889,465 891,465 893,466 894,466 896,466 898,467 899,467 901,467 903,467 904,468 906,468 908,468 909,468 911,468 913,469 915,469 916,469 918,469 920,469 921,469 923,469 925,469 926,469 928,469 930,469 932,469 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,413 166,413 168,414 169,415 171,416 173,418 175,421 176,423 178,426 180,429 181,432 183,435 185,438 186,441 188,443 190,445 191,447 193,449 195,450 197,451 198,451 200,452 202,451 203,451 205,450 207,449 208,448 210,447 212,446 214,445 215,444 217,443 219,442 220,441 222,441 224,441 225,440 227,441 229,441 230,441 232,442 234,442 236,443 237,443 239,444 241,444 242,444 244,443 246,443 247,442 249,440 251,438 252,436 254,433 256,430 258,426 259,422 261,418 263,414 264,410 266,405 268,401 269,396 271,392 273,388 274,384 276,380 278,377 280,375 281,372 283,370 285,369 286,367 288,367 290,366 291,365 293,365 295,365 296,365 298,366 300,366 302,366 303,366 305,367 307,367 308,367 310,367 312,367 313,367 315,367 317,366 318,366 320,365 322,365 324,364 325,364 327,363 329,362 330,362 332,361 334,360 335,359 337,358 339,356 341,355 342,354 344,353 346,351 347,350 349,349 351,348 352,347 354,346 356,345 357,344 359,344 361,344 363,344 364,344 366,345 368,346 369,347 371,349 373,352 374,354 376,357 378,361 379,364 381,368 383,372 385,376 386,381 388,385 390,389 391,393 393,397 395,401 396,404 398,407 400,409 401,411 403,412 405,412 407,412 408,412 410,410 412,409 413,406 415,403 417,400 418,397 420,393 422,389 423,386 425,382 427,379 429,376 430,373 432,371 434,370 435,369 437,369 439,369 440,371 442,372 444,375 445,377 447,380 449,384 451,387 452,390 454,393 456,395 457,396 459,397 461,397 462,395 464,392 466,388 468,382 469,375 471,366 473,355 474,343 476,330 478,315 479,299 481,282 483,264 484,246 486,227 488,209 490,192 491,175 493,159 495,144 496,131 498,120 500,110 501,103 503,97 505,94 506,92 508,93 510,95 512,99 513,105 515,112 517,120 518,129 520,138 522,149 523,159 525,169 527,180 528,190 530,199 532,209 534,218 535,226 537,233 539,240 540,247 542,252 544,257 545,262 547,266 549,269 550,272 552,274 554,276 556,278 557,279 559,280 561,280 562,280 564,280 566,279 567,278 569,277 571,275 573,273 574,271 576,269 578,267 579,265 581,263 583,261 584,259 586,257 588,256 589,256 591,255 593,255 595,256 596,257 598,258 600,260 601,262 603,265 605,267 606,270 608,273 610,275 611,278 613,280 615,282 617,284 618,286 620,287 622,288 623,289 625,289 627,289 628,288 630,288 632,287 633,286 635,285 637,283 639,282 640,281 642,280 644,279 645,279 647,279 649,279 650,280 652,282 654,283 655,286 657,289 659,292 661,296 662,300 664,304 666,309 667,313 669,318 671,323 672,328 674,332 676,337 677,341 679,345 681,348 683,352 684,355 686,358 688,360 689,363 691,365 693,367 694,369 696,370 698,372 700,373 701,374 703,375 705,376 706,376 708,377 710,377 711,378 713,378 715,378 716,378 718,377 720,377 722,376 723,376 725,375 727,375 728,374 730,374 732,374 733,373 735,373 737,374 738,374 740,375 742,376 744,378 745,379 747,381 749,384 750,386 752,389 754,392 755,395 757,398 759,402 760,405 762,408 764,411 766,414 767,417 769,420 771,422 772,424 774,426 776,428 777,430 779,432 781,433 782,434 784,435 786,436 788,437 789,438 791,439 793,439 794,440 796,441 798,441 799,442 801,442 803,443 804,444 806,444 808,445 810,445 811,446 813,446 815,447 816,448 818,448 820,448 821,449 823,449 825,450 827,450 828,450 830,450 832,450 833,450 835,450 837,450 838,450 840,450 842,450 843,449 845,449 847,449 849,449 850,449 852,449 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="522,473 522,151 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

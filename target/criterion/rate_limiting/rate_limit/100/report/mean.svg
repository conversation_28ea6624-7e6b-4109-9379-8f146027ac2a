<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/100:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="449" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,449 86,449 "/>
<text x="77" y="402" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,402 86,402 "/>
<text x="77" y="355" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,355 86,355 "/>
<text x="77" y="308" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,308 86,308 "/>
<text x="77" y="261" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,261 86,261 "/>
<text x="77" y="214" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,214 86,214 "/>
<text x="77" y="167" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,167 86,167 "/>
<text x="77" y="120" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,120 86,120 "/>
<text x="77" y="73" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,73 86,73 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="116" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="116,473 116,478 "/>
<text x="212" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="212,473 212,478 "/>
<text x="307" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="307,473 307,478 "/>
<text x="402" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="402,473 402,478 "/>
<text x="498" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="498,473 498,478 "/>
<text x="593" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="593,473 593,478 "/>
<text x="688" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="688,473 688,478 "/>
<text x="784" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="784,473 784,478 "/>
<text x="879" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
192
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="879,473 879,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,471 92,470 93,469 95,469 97,468 98,467 100,466 102,466 103,465 105,464 107,463 109,463 110,462 112,461 114,460 115,459 117,459 119,458 120,457 122,456 124,455 125,454 127,453 129,452 131,451 132,450 134,449 136,448 137,447 139,445 141,444 142,443 144,442 146,440 147,439 149,437 151,436 153,434 154,433 156,431 158,430 159,428 161,427 163,425 164,423 166,422 168,420 169,418 171,417 173,415 175,413 176,412 178,410 180,408 181,407 183,405 185,403 186,402 188,400 190,398 191,397 193,395 195,393 197,392 198,390 200,388 202,386 203,385 205,383 207,381 208,379 210,378 212,376 214,374 215,372 217,370 219,368 220,366 222,364 224,362 225,360 227,358 229,355 230,353 232,351 234,349 236,346 237,344 239,342 241,340 242,337 244,335 246,333 247,330 249,328 251,326 252,324 254,322 256,319 258,317 259,315 261,313 263,311 264,309 266,306 268,304 269,302 271,300 273,298 274,296 276,293 278,291 280,289 281,286 283,284 285,282 286,279 288,277 290,275 291,272 293,270 295,267 296,265 298,262 300,260 302,257 303,255 305,253 307,250 308,248 310,245 312,243 313,241 315,238 317,236 318,234 320,231 322,229 324,227 325,225 327,222 329,220 330,218 332,216 334,214 335,212 337,209 339,207 341,205 342,203 344,200 346,198 347,196 349,194 351,191 352,189 354,187 356,185 357,182 359,180 361,178 363,175 364,173 366,171 368,168 369,166 371,164 373,161 374,159 376,157 378,155 379,152 381,150 383,148 385,146 386,144 388,142 390,140 391,138 393,136 395,134 396,132 398,130 400,129 401,127 403,125 405,124 407,122 408,120 410,119 412,117 413,116 415,114 417,113 418,112 420,110 422,109 423,108 425,107 427,105 429,104 430,103 432,102 434,101 435,100 437,99 439,99 440,98 442,97 444,97 445,96 447,96 449,95 451,95 452,95 454,94 456,94 457,94 459,94 461,94 462,94 464,94 466,94 468,94 469,94 471,94 473,94 474,94 476,94 478,94 479,94 481,94 483,94 484,94 486,94 488,94 490,94 491,95 493,95 495,95 496,96 498,96 500,97 501,97 503,98 505,99 506,99 508,100 510,101 512,102 513,103 515,104 517,105 518,106 520,107 522,108 523,109 525,110 527,111 528,112 530,113 532,114 534,115 535,116 537,117 539,118 540,119 542,120 544,121 545,122 547,123 549,124 550,125 552,126 554,127 556,128 557,129 559,130 561,131 562,132 564,134 566,135 567,136 569,138 571,139 573,141 574,143 576,144 578,146 579,148 581,150 583,152 584,154 586,156 588,158 589,160 591,162 593,164 595,167 596,169 598,171 600,173 601,175 603,178 605,180 606,182 608,184 610,186 611,189 613,191 615,193 617,196 618,198 620,200 622,202 623,205 625,207 627,209 628,212 630,214 632,216 633,218 635,221 637,223 639,225 640,228 642,230 644,232 645,234 647,237 649,239 650,241 652,244 654,246 655,249 657,251 659,253 661,256 662,258 664,261 666,263 667,266 669,268 671,270 672,273 674,275 676,278 677,280 679,282 681,285 683,287 684,289 686,291 688,293 689,295 691,297 693,299 694,301 696,303 698,305 700,307 701,309 703,311 705,313 706,315 708,316 710,318 711,320 713,322 715,323 716,325 718,327 720,328 722,330 723,332 725,334 727,335 728,337 730,339 732,341 733,343 735,344 737,346 738,348 740,350 742,352 744,354 745,356 747,358 749,360 750,362 752,364 754,366 755,367 757,369 759,371 760,373 762,375 764,376 766,378 767,380 769,381 771,383 772,384 774,386 776,387 777,389 779,390 781,392 782,393 784,394 786,396 788,397 789,398 791,400 793,401 794,402 796,404 798,405 799,406 801,408 803,409 804,410 806,412 808,413 810,414 811,416 813,417 815,418 816,419 818,420 820,422 821,423 823,424 825,425 827,426 828,427 830,428 832,430 833,431 835,432 837,433 838,434 840,435 842,436 843,437 845,438 847,439 849,440 850,441 852,442 854,443 855,444 857,444 859,445 860,446 862,447 864,448 865,448 867,449 869,450 871,451 872,451 874,452 876,453 877,453 879,454 881,455 882,455 884,456 886,456 887,457 889,458 891,458 893,459 894,459 896,460 898,461 899,461 901,462 903,462 904,463 906,464 908,464 909,465 911,465 913,466 915,467 916,467 918,468 920,468 921,469 923,470 925,470 926,471 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,423 166,422 168,420 169,418 171,417 173,415 175,413 176,412 178,410 180,408 181,407 183,405 185,403 186,402 188,400 190,398 191,397 193,395 195,393 197,392 198,390 200,388 202,386 203,385 205,383 207,381 208,379 210,378 212,376 214,374 215,372 217,370 219,368 220,366 222,364 224,362 225,360 227,358 229,355 230,353 232,351 234,349 236,346 237,344 239,342 241,340 242,337 244,335 246,333 247,330 249,328 251,326 252,324 254,322 256,319 258,317 259,315 261,313 263,311 264,309 266,306 268,304 269,302 271,300 273,298 274,296 276,293 278,291 280,289 281,286 283,284 285,282 286,279 288,277 290,275 291,272 293,270 295,267 296,265 298,262 300,260 302,257 303,255 305,253 307,250 308,248 310,245 312,243 313,241 315,238 317,236 318,234 320,231 322,229 324,227 325,225 327,222 329,220 330,218 332,216 334,214 335,212 337,209 339,207 341,205 342,203 344,200 346,198 347,196 349,194 351,191 352,189 354,187 356,185 357,182 359,180 361,178 363,175 364,173 366,171 368,168 369,166 371,164 373,161 374,159 376,157 378,155 379,152 381,150 383,148 385,146 386,144 388,142 390,140 391,138 393,136 395,134 396,132 398,130 400,129 401,127 403,125 405,124 407,122 408,120 410,119 412,117 413,116 415,114 417,113 418,112 420,110 422,109 423,108 425,107 427,105 429,104 430,103 432,102 434,101 435,100 437,99 439,99 440,98 442,97 444,97 445,96 447,96 449,95 451,95 452,95 454,94 456,94 457,94 459,94 461,94 462,94 464,94 466,94 468,94 469,94 471,94 473,94 474,94 476,94 478,94 479,94 481,94 483,94 484,94 486,94 488,94 490,94 491,95 493,95 495,95 496,96 498,96 500,97 501,97 503,98 505,99 506,99 508,100 510,101 512,102 513,103 515,104 517,105 518,106 520,107 522,108 523,109 525,110 527,111 528,112 530,113 532,114 534,115 535,116 537,117 539,118 540,119 542,120 544,121 545,122 547,123 549,124 550,125 552,126 554,127 556,128 557,129 559,130 561,131 562,132 564,134 566,135 567,136 569,138 571,139 573,141 574,143 576,144 578,146 579,148 581,150 583,152 584,154 586,156 588,158 589,160 591,162 593,164 595,167 596,169 598,171 600,173 601,175 603,178 605,180 606,182 608,184 610,186 611,189 613,191 615,193 617,196 618,198 620,200 622,202 623,205 625,207 627,209 628,212 630,214 632,216 633,218 635,221 637,223 639,225 640,228 642,230 644,232 645,234 647,237 649,239 650,241 652,244 654,246 655,249 657,251 659,253 661,256 662,258 664,261 666,263 667,266 669,268 671,270 672,273 674,275 676,278 677,280 679,282 681,285 683,287 684,289 686,291 688,293 689,295 691,297 693,299 694,301 696,303 698,305 700,307 701,309 703,311 705,313 706,315 708,316 710,318 711,320 713,322 715,323 716,325 718,327 720,328 722,330 723,332 725,334 727,335 728,337 730,339 732,341 733,343 735,344 737,346 738,348 740,350 742,352 744,354 745,356 747,358 749,360 750,362 752,364 754,366 755,367 757,369 759,371 760,373 762,375 764,376 766,378 767,380 769,381 771,383 772,384 774,386 776,387 777,389 779,390 781,392 782,393 784,394 786,396 788,397 789,398 791,400 793,401 794,402 796,404 798,405 799,406 801,408 803,409 804,410 806,412 808,413 810,414 811,416 813,417 815,418 816,419 818,420 820,422 821,423 823,424 825,425 827,426 828,427 830,428 832,430 833,431 835,432 837,433 838,434 840,435 842,436 843,437 845,438 847,439 849,440 850,441 852,442 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="493,473 493,95 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

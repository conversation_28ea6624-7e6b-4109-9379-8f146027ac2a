<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/10:slope
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="428" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,428 86,428 "/>
<text x="77" y="364" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,364 86,364 "/>
<text x="77" y="299" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,299 86,299 "/>
<text x="77" y="235" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,235 86,235 "/>
<text x="77" y="171" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,171 86,171 "/>
<text x="77" y="107" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,107 86,107 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="171" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
186
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="171,473 171,478 "/>
<text x="309" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="309,473 309,478 "/>
<text x="446" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="446,473 446,478 "/>
<text x="584" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="584,473 584,478 "/>
<text x="721" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="721,473 721,478 "/>
<text x="859" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
191
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="859,473 859,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,470 92,469 93,468 95,467 97,466 98,465 100,464 102,462 103,461 105,460 107,458 109,457 110,455 112,454 114,452 115,451 117,449 119,448 120,446 122,445 124,443 125,441 127,439 129,438 131,436 132,434 134,432 136,431 137,429 139,427 141,425 142,423 144,421 146,419 147,417 149,415 151,413 153,411 154,409 156,407 158,404 159,402 161,400 163,398 164,396 166,393 168,391 169,389 171,386 173,384 175,382 176,379 178,377 180,374 181,372 183,369 185,367 186,364 188,361 190,359 191,356 193,353 195,350 197,348 198,345 200,342 202,339 203,336 205,334 207,331 208,328 210,325 212,322 214,319 215,316 217,313 219,311 220,308 222,305 224,302 225,299 227,296 229,293 230,290 232,287 234,284 236,282 237,279 239,276 241,273 242,270 244,268 246,265 247,262 249,260 251,257 252,254 254,252 256,249 258,247 259,244 261,242 263,239 264,237 266,234 268,232 269,229 271,227 273,224 274,222 276,219 278,217 280,214 281,211 283,209 285,206 286,203 288,201 290,198 291,195 293,193 295,190 296,187 298,184 300,182 302,179 303,176 305,173 307,171 308,168 310,166 312,163 313,161 315,158 317,156 318,154 320,152 322,150 324,148 325,146 327,144 329,142 330,141 332,139 334,137 335,136 337,134 339,133 341,131 342,130 344,128 346,127 347,125 349,124 351,123 352,121 354,120 356,118 357,117 359,116 361,114 363,113 364,112 366,110 368,109 369,108 371,107 373,106 374,105 376,104 378,103 379,102 381,101 383,100 385,99 386,99 388,98 390,98 391,97 393,97 395,96 396,96 398,95 400,95 401,95 403,94 405,94 407,94 408,94 410,94 412,94 413,93 415,93 417,93 418,93 420,93 422,93 423,94 425,94 427,94 429,94 430,94 432,95 434,95 435,95 437,95 439,96 440,96 442,97 444,97 445,98 447,98 449,99 451,99 452,100 454,101 456,102 457,103 459,104 461,105 462,106 464,107 466,108 468,110 469,111 471,112 473,114 474,115 476,117 478,118 479,119 481,121 483,122 484,123 486,124 488,125 490,127 491,128 493,129 495,130 496,131 498,131 500,132 501,133 503,134 505,135 506,136 508,137 510,138 512,139 513,141 515,142 517,143 518,145 520,146 522,148 523,150 525,151 527,153 528,155 530,157 532,158 534,160 535,162 537,164 539,166 540,168 542,170 544,172 545,174 547,176 549,178 550,180 552,182 554,184 556,185 557,187 559,189 561,191 562,193 564,195 566,197 567,198 569,200 571,202 573,204 574,206 576,208 578,210 579,211 581,213 583,215 584,217 586,219 588,221 589,223 591,225 593,227 595,229 596,231 598,233 600,235 601,237 603,239 605,241 606,243 608,245 610,247 611,249 613,251 615,253 617,255 618,256 620,258 622,260 623,262 625,264 627,266 628,268 630,270 632,272 633,273 635,275 637,277 639,279 640,281 642,283 644,285 645,287 647,289 649,291 650,293 652,295 654,297 655,299 657,301 659,304 661,306 662,308 664,310 666,312 667,314 669,316 671,318 672,320 674,322 676,324 677,326 679,328 681,330 683,332 684,334 686,335 688,337 689,339 691,341 693,342 694,344 696,346 698,347 700,349 701,350 703,352 705,353 706,355 708,356 710,358 711,359 713,361 715,362 716,363 718,365 720,366 722,367 723,369 725,370 727,371 728,373 730,374 732,376 733,377 735,378 737,380 738,381 740,382 742,384 744,385 745,386 747,388 749,389 750,390 752,391 754,393 755,394 757,395 759,396 760,397 762,399 764,400 766,401 767,402 769,403 771,404 772,405 774,406 776,407 777,408 779,410 781,411 782,412 784,413 786,414 788,415 789,416 791,417 793,418 794,419 796,420 798,421 799,422 801,423 803,424 804,425 806,426 808,427 810,428 811,429 813,430 815,430 816,431 818,432 820,433 821,434 823,435 825,435 827,436 828,437 830,438 832,438 833,439 835,440 837,440 838,441 840,442 842,442 843,443 845,444 847,444 849,445 850,446 852,446 854,447 855,448 857,448 859,449 860,450 862,450 864,451 865,452 867,452 869,453 871,453 872,454 874,455 876,455 877,456 879,456 881,457 882,458 884,458 886,459 887,459 889,460 891,460 893,461 894,462 896,462 898,463 899,463 901,464 903,464 904,465 906,465 908,466 909,466 911,467 913,467 915,467 916,468 918,468 920,469 921,469 923,470 925,470 926,470 928,471 930,471 932,471 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,396 166,393 168,391 169,389 171,386 173,384 175,382 176,379 178,377 180,374 181,372 183,369 185,367 186,364 188,361 190,359 191,356 193,353 195,350 197,348 198,345 200,342 202,339 203,336 205,334 207,331 208,328 210,325 212,322 214,319 215,316 217,313 219,311 220,308 222,305 224,302 225,299 227,296 229,293 230,290 232,287 234,284 236,282 237,279 239,276 241,273 242,270 244,268 246,265 247,262 249,260 251,257 252,254 254,252 256,249 258,247 259,244 261,242 263,239 264,237 266,234 268,232 269,229 271,227 273,224 274,222 276,219 278,217 280,214 281,211 283,209 285,206 286,203 288,201 290,198 291,195 293,193 295,190 296,187 298,184 300,182 302,179 303,176 305,173 307,171 308,168 310,166 312,163 313,161 315,158 317,156 318,154 320,152 322,150 324,148 325,146 327,144 329,142 330,141 332,139 334,137 335,136 337,134 339,133 341,131 342,130 344,128 346,127 347,125 349,124 351,123 352,121 354,120 356,118 357,117 359,116 361,114 363,113 364,112 366,110 368,109 369,108 371,107 373,106 374,105 376,104 378,103 379,102 381,101 383,100 385,99 386,99 388,98 390,98 391,97 393,97 395,96 396,96 398,95 400,95 401,95 403,94 405,94 407,94 408,94 410,94 412,94 413,93 415,93 417,93 418,93 420,93 422,93 423,94 425,94 427,94 429,94 430,94 432,95 434,95 435,95 437,95 439,96 440,96 442,97 444,97 445,98 447,98 449,99 451,99 452,100 454,101 456,102 457,103 459,104 461,105 462,106 464,107 466,108 468,110 469,111 471,112 473,114 474,115 476,117 478,118 479,119 481,121 483,122 484,123 486,124 488,125 490,127 491,128 493,129 495,130 496,131 498,131 500,132 501,133 503,134 505,135 506,136 508,137 510,138 512,139 513,141 515,142 517,143 518,145 520,146 522,148 523,150 525,151 527,153 528,155 530,157 532,158 534,160 535,162 537,164 539,166 540,168 542,170 544,172 545,174 547,176 549,178 550,180 552,182 554,184 556,185 557,187 559,189 561,191 562,193 564,195 566,197 567,198 569,200 571,202 573,204 574,206 576,208 578,210 579,211 581,213 583,215 584,217 586,219 588,221 589,223 591,225 593,227 595,229 596,231 598,233 600,235 601,237 603,239 605,241 606,243 608,245 610,247 611,249 613,251 615,253 617,255 618,256 620,258 622,260 623,262 625,264 627,266 628,268 630,270 632,272 633,273 635,275 637,277 639,279 640,281 642,283 644,285 645,287 647,289 649,291 650,293 652,295 654,297 655,299 657,301 659,304 661,306 662,308 664,310 666,312 667,314 669,316 671,318 672,320 674,322 676,324 677,326 679,328 681,330 683,332 684,334 686,335 688,337 689,339 691,341 693,342 694,344 696,346 698,347 700,349 701,350 703,352 705,353 706,355 708,356 710,358 711,359 713,361 715,362 716,363 718,365 720,366 722,367 723,369 725,370 727,371 728,373 730,374 732,376 733,377 735,378 737,380 738,381 740,382 742,384 744,385 745,386 747,388 749,389 750,390 752,391 754,393 755,394 757,395 759,396 760,397 762,399 764,400 766,401 767,402 769,403 771,404 772,405 774,406 776,407 777,408 779,410 781,411 782,412 784,413 786,414 788,415 789,416 791,417 793,418 794,419 796,420 798,421 799,422 801,423 803,424 804,425 806,426 808,427 810,428 811,429 813,430 815,430 816,431 818,432 820,433 821,434 823,435 825,435 827,436 828,437 830,438 832,438 833,439 835,440 837,440 838,441 840,442 842,442 843,443 845,444 847,444 849,445 850,446 852,446 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="465,473 465,108 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

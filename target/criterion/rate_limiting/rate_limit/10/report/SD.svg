<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/10:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="407" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,407 86,407 "/>
<text x="77" y="327" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,327 86,327 "/>
<text x="77" y="247" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,247 86,247 "/>
<text x="77" y="167" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,167 86,167 "/>
<text x="77" y="87" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,87 86,87 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="126" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="126,473 126,478 "/>
<text x="223" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="223,473 223,478 "/>
<text x="319" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="319,473 319,478 "/>
<text x="416" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="416,473 416,478 "/>
<text x="512" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="512,473 512,478 "/>
<text x="609" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="609,473 609,478 "/>
<text x="706" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
11
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="706,473 706,478 "/>
<text x="802" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="802,473 802,478 "/>
<text x="899" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
13
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="899,473 899,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,469 95,468 97,466 98,465 100,464 102,463 103,461 105,460 107,459 109,457 110,456 112,454 114,453 115,451 117,449 119,448 120,446 122,444 124,442 125,441 127,439 129,437 131,435 132,433 134,431 136,429 137,427 139,425 141,423 142,421 144,420 146,418 147,416 149,414 151,412 153,410 154,408 156,407 158,405 159,403 161,401 163,400 164,398 166,396 168,395 169,393 171,392 173,390 175,389 176,387 178,386 180,385 181,384 183,382 185,381 186,380 188,379 190,378 191,377 193,376 195,376 197,375 198,374 200,374 202,373 203,373 205,372 207,372 208,372 210,372 212,371 214,371 215,371 217,372 219,372 220,372 222,372 224,373 225,373 227,373 229,374 230,375 232,375 234,376 236,377 237,377 239,378 241,379 242,380 244,380 246,381 247,382 249,383 251,384 252,384 254,385 256,386 258,386 259,387 261,387 263,388 264,388 266,389 268,389 269,389 271,389 273,388 274,388 276,387 278,387 280,386 281,385 283,384 285,382 286,381 288,379 290,377 291,375 293,372 295,370 296,367 298,364 300,361 302,357 303,354 305,350 307,346 308,342 310,338 312,334 313,329 315,325 317,320 318,315 320,310 322,305 324,300 325,295 327,290 329,284 330,279 332,274 334,269 335,263 337,258 339,253 341,248 342,243 344,237 346,233 347,228 349,223 351,219 352,214 354,210 356,206 357,202 359,198 361,195 363,192 364,189 366,186 368,184 369,181 371,179 373,177 374,176 376,175 378,173 379,173 381,172 383,172 385,172 386,172 388,172 390,173 391,174 393,175 395,176 396,177 398,179 400,181 401,183 403,185 405,188 407,190 408,193 410,196 412,198 413,201 415,204 417,207 418,210 420,213 422,216 423,219 425,222 427,224 429,227 430,229 432,231 434,233 435,235 437,236 439,238 440,238 442,239 444,239 445,239 447,239 449,238 451,237 452,236 454,234 456,232 457,230 459,227 461,224 462,221 464,217 466,213 468,209 469,205 471,200 473,196 474,191 476,186 478,180 479,175 481,170 483,164 484,159 486,154 488,148 490,143 491,138 493,133 495,128 496,124 498,120 500,115 501,112 503,108 505,105 506,102 508,100 510,98 512,96 513,94 515,94 517,93 518,93 520,93 522,94 523,95 525,97 527,99 528,101 530,104 532,107 534,110 535,114 537,118 539,122 540,126 542,131 544,135 545,140 547,145 549,150 550,155 552,160 554,165 556,170 557,174 559,179 561,183 562,187 564,191 566,195 567,199 569,202 571,205 573,208 574,210 576,212 578,214 579,215 581,217 583,217 584,218 586,218 588,218 589,218 591,217 593,216 595,215 596,213 598,212 600,210 601,208 603,206 605,204 606,202 608,200 610,198 611,196 613,194 615,192 617,190 618,189 620,188 622,186 623,185 625,185 627,184 628,184 630,184 632,184 633,185 635,185 637,186 639,188 640,189 642,191 644,194 645,196 647,199 649,202 650,205 652,208 654,212 655,216 657,220 659,224 661,228 662,232 664,237 666,241 667,246 669,250 671,254 672,259 674,263 676,267 677,271 679,275 681,279 683,282 684,285 686,288 688,291 689,294 691,296 693,298 694,300 696,302 698,303 700,305 701,306 703,307 705,307 706,308 708,308 710,308 711,309 713,309 715,309 716,309 718,308 720,308 722,308 723,308 725,308 727,308 728,309 730,309 732,309 733,310 735,310 737,311 738,312 740,313 742,315 744,316 745,318 747,320 749,322 750,324 752,326 754,329 755,332 757,334 759,337 760,340 762,343 764,347 766,350 767,353 769,356 771,360 772,363 774,366 776,369 777,372 779,375 781,378 782,380 784,383 786,385 788,387 789,390 791,391 793,393 794,395 796,396 798,397 799,399 801,400 803,401 804,401 806,402 808,403 810,404 811,404 813,405 815,405 816,406 818,406 820,407 821,407 823,408 825,409 827,410 828,410 830,411 832,412 833,413 835,414 837,415 838,416 840,417 842,419 843,420 845,421 847,422 849,424 850,425 852,427 854,428 855,429 857,431 859,432 860,434 862,435 864,436 865,438 867,439 869,440 871,441 872,442 874,444 876,445 877,446 879,447 881,447 882,448 884,449 886,450 887,451 889,451 891,452 893,452 894,453 896,454 898,454 899,455 901,455 903,455 904,456 906,456 908,457 909,457 911,458 913,458 915,459 916,459 918,460 920,461 921,461 923,462 925,462 926,463 928,464 930,464 932,465 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,398 166,396 168,395 169,393 171,392 173,390 175,389 176,387 178,386 180,385 181,384 183,382 185,381 186,380 188,379 190,378 191,377 193,376 195,376 197,375 198,374 200,374 202,373 203,373 205,372 207,372 208,372 210,372 212,371 214,371 215,371 217,372 219,372 220,372 222,372 224,373 225,373 227,373 229,374 230,375 232,375 234,376 236,377 237,377 239,378 241,379 242,380 244,380 246,381 247,382 249,383 251,384 252,384 254,385 256,386 258,386 259,387 261,387 263,388 264,388 266,389 268,389 269,389 271,389 273,388 274,388 276,387 278,387 280,386 281,385 283,384 285,382 286,381 288,379 290,377 291,375 293,372 295,370 296,367 298,364 300,361 302,357 303,354 305,350 307,346 308,342 310,338 312,334 313,329 315,325 317,320 318,315 320,310 322,305 324,300 325,295 327,290 329,284 330,279 332,274 334,269 335,263 337,258 339,253 341,248 342,243 344,237 346,233 347,228 349,223 351,219 352,214 354,210 356,206 357,202 359,198 361,195 363,192 364,189 366,186 368,184 369,181 371,179 373,177 374,176 376,175 378,173 379,173 381,172 383,172 385,172 386,172 388,172 390,173 391,174 393,175 395,176 396,177 398,179 400,181 401,183 403,185 405,188 407,190 408,193 410,196 412,198 413,201 415,204 417,207 418,210 420,213 422,216 423,219 425,222 427,224 429,227 430,229 432,231 434,233 435,235 437,236 439,238 440,238 442,239 444,239 445,239 447,239 449,238 451,237 452,236 454,234 456,232 457,230 459,227 461,224 462,221 464,217 466,213 468,209 469,205 471,200 473,196 474,191 476,186 478,180 479,175 481,170 483,164 484,159 486,154 488,148 490,143 491,138 493,133 495,128 496,124 498,120 500,115 501,112 503,108 505,105 506,102 508,100 510,98 512,96 513,94 515,94 517,93 518,93 520,93 522,94 523,95 525,97 527,99 528,101 530,104 532,107 534,110 535,114 537,118 539,122 540,126 542,131 544,135 545,140 547,145 549,150 550,155 552,160 554,165 556,170 557,174 559,179 561,183 562,187 564,191 566,195 567,199 569,202 571,205 573,208 574,210 576,212 578,214 579,215 581,217 583,217 584,218 586,218 588,218 589,218 591,217 593,216 595,215 596,213 598,212 600,210 601,208 603,206 605,204 606,202 608,200 610,198 611,196 613,194 615,192 617,190 618,189 620,188 622,186 623,185 625,185 627,184 628,184 630,184 632,184 633,185 635,185 637,186 639,188 640,189 642,191 644,194 645,196 647,199 649,202 650,205 652,208 654,212 655,216 657,220 659,224 661,228 662,232 664,237 666,241 667,246 669,250 671,254 672,259 674,263 676,267 677,271 679,275 681,279 683,282 684,285 686,288 688,291 689,294 691,296 693,298 694,300 696,302 698,303 700,305 701,306 703,307 705,307 706,308 708,308 710,308 711,309 713,309 715,309 716,309 718,308 720,308 722,308 723,308 725,308 727,308 728,309 730,309 732,309 733,310 735,310 737,311 738,312 740,313 742,315 744,316 745,318 747,320 749,322 750,324 752,326 754,329 755,332 757,334 759,337 760,340 762,343 764,347 766,350 767,353 769,356 771,360 772,363 774,366 776,369 777,372 779,375 781,378 782,380 784,383 786,385 788,387 789,390 791,391 793,393 794,395 796,396 798,397 799,399 801,400 803,401 804,401 806,402 808,403 810,404 811,404 813,405 815,405 816,406 818,406 820,407 821,407 823,408 825,409 827,410 828,410 830,411 832,412 833,413 835,414 837,415 838,416 840,417 842,419 843,420 845,421 847,422 849,424 850,425 852,427 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="532,473 532,107 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

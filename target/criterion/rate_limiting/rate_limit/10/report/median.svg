<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/10:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="425" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,425 86,425 "/>
<text x="77" y="369" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,369 86,369 "/>
<text x="77" y="313" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,313 86,313 "/>
<text x="77" y="257" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,257 86,257 "/>
<text x="77" y="201" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,201 86,201 "/>
<text x="77" y="144" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,144 86,144 "/>
<text x="77" y="88" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,88 86,88 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="164" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
184.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="164,473 164,478 "/>
<text x="290" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
185
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="290,473 290,478 "/>
<text x="417" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
185.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="417,473 417,478 "/>
<text x="544" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
186
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="544,473 544,478 "/>
<text x="671" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
186.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="671,473 671,478 "/>
<text x="797" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="797,473 797,478 "/>
<text x="924" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="924,473 924,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,469 97,468 98,467 100,466 102,464 103,463 105,461 107,459 109,457 110,455 112,452 114,449 115,445 117,442 119,437 120,433 122,427 124,422 125,415 127,408 129,401 131,393 132,384 134,375 136,365 137,354 139,343 141,331 142,319 144,306 146,293 147,280 149,266 151,252 153,238 154,224 156,210 158,197 159,184 161,171 163,159 164,148 166,137 168,128 169,119 171,112 173,105 175,100 176,96 178,94 180,92 181,92 183,94 185,96 186,100 188,104 190,110 191,116 193,123 195,131 197,140 198,148 200,158 202,167 203,177 205,187 207,196 208,206 210,215 212,224 214,232 215,241 217,248 219,256 220,262 222,269 224,275 225,280 227,285 229,289 230,293 232,297 234,300 236,303 237,305 239,307 241,309 242,311 244,312 246,314 247,315 249,315 251,316 252,317 254,317 256,318 258,318 259,319 261,319 263,319 264,320 266,320 268,320 269,320 271,320 273,320 274,320 276,320 278,320 280,320 281,320 283,320 285,320 286,319 288,319 290,319 291,318 293,318 295,317 296,317 298,316 300,316 302,315 303,314 305,314 307,313 308,313 310,312 312,312 313,311 315,311 317,310 318,310 320,309 322,309 324,309 325,308 327,308 329,307 330,307 332,306 334,306 335,305 337,304 339,303 341,302 342,301 344,300 346,299 347,298 349,296 351,294 352,292 354,290 356,288 357,286 359,283 361,281 363,278 364,275 366,272 368,269 369,267 371,264 373,261 374,258 376,256 378,253 379,251 381,249 383,248 385,246 386,246 388,245 390,246 391,246 393,247 395,249 396,252 398,254 400,258 401,261 403,266 405,271 407,276 408,281 410,287 412,293 413,299 415,305 417,311 418,317 420,323 422,329 423,334 425,339 427,344 429,349 430,353 432,356 434,359 435,361 437,363 439,365 440,366 442,367 444,367 445,367 447,366 449,365 451,364 452,363 454,362 456,361 457,359 459,358 461,357 462,356 464,355 466,354 468,353 469,353 471,353 473,353 474,353 476,354 478,355 479,355 481,356 483,358 484,359 486,360 488,361 490,362 491,363 493,364 495,365 496,366 498,367 500,367 501,367 503,368 505,367 506,367 508,367 510,367 512,366 513,365 515,365 517,364 518,363 520,362 522,362 523,361 525,361 527,360 528,360 530,359 532,359 534,359 535,359 537,359 539,358 540,358 542,358 544,358 545,358 547,358 549,358 550,358 552,357 554,357 556,356 557,355 559,354 561,354 562,352 564,351 566,350 567,349 569,347 571,346 573,344 574,343 576,341 578,340 579,338 581,336 583,335 584,333 586,331 588,330 589,328 591,326 593,324 595,322 596,320 598,317 600,315 601,312 603,310 605,307 606,304 608,301 610,298 611,295 613,292 615,290 617,287 618,284 620,282 622,280 623,278 625,276 627,275 628,274 630,274 632,273 633,274 635,275 637,276 639,277 640,279 642,282 644,284 645,287 647,291 649,294 650,297 652,301 654,305 655,308 657,311 659,315 661,318 662,321 664,323 666,326 667,328 669,329 671,331 672,332 674,333 676,333 677,334 679,334 681,333 683,333 684,333 686,332 688,332 689,331 691,331 693,330 694,330 696,329 698,328 700,328 701,327 703,326 705,325 706,324 708,323 710,321 711,319 713,317 715,314 716,310 718,306 720,302 722,296 723,290 725,284 727,277 728,269 730,260 732,251 733,241 735,231 737,221 738,211 740,200 742,189 744,179 745,169 747,159 749,150 750,141 752,133 754,126 755,119 757,114 759,110 760,107 762,105 764,104 766,104 767,105 769,108 771,111 772,116 774,121 776,127 777,134 779,142 781,150 782,159 784,168 786,178 788,187 789,197 791,207 793,217 794,226 796,236 798,245 799,254 801,263 803,272 804,280 806,288 808,296 810,303 811,310 813,317 815,324 816,330 818,336 820,342 821,347 823,352 825,358 827,362 828,367 830,372 832,376 833,380 835,384 837,388 838,391 840,395 842,398 843,401 845,404 847,407 849,410 850,412 852,415 854,417 855,419 857,421 859,423 860,424 862,426 864,427 865,429 867,430 869,431 871,432 872,433 874,434 876,435 877,436 879,437 881,438 882,439 884,440 886,441 887,442 889,443 891,444 893,445 894,446 896,447 898,448 899,449 901,450 903,452 904,453 906,454 908,455 909,457 911,458 913,459 915,460 916,461 918,462 920,464 921,465 923,465 925,466 926,467 928,468 930,469 932,470 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,148 166,137 168,128 169,119 171,112 173,105 175,100 176,96 178,94 180,92 181,92 183,94 185,96 186,100 188,104 190,110 191,116 193,123 195,131 197,140 198,148 200,158 202,167 203,177 205,187 207,196 208,206 210,215 212,224 214,232 215,241 217,248 219,256 220,262 222,269 224,275 225,280 227,285 229,289 230,293 232,297 234,300 236,303 237,305 239,307 241,309 242,311 244,312 246,314 247,315 249,315 251,316 252,317 254,317 256,318 258,318 259,319 261,319 263,319 264,320 266,320 268,320 269,320 271,320 273,320 274,320 276,320 278,320 280,320 281,320 283,320 285,320 286,319 288,319 290,319 291,318 293,318 295,317 296,317 298,316 300,316 302,315 303,314 305,314 307,313 308,313 310,312 312,312 313,311 315,311 317,310 318,310 320,309 322,309 324,309 325,308 327,308 329,307 330,307 332,306 334,306 335,305 337,304 339,303 341,302 342,301 344,300 346,299 347,298 349,296 351,294 352,292 354,290 356,288 357,286 359,283 361,281 363,278 364,275 366,272 368,269 369,267 371,264 373,261 374,258 376,256 378,253 379,251 381,249 383,248 385,246 386,246 388,245 390,246 391,246 393,247 395,249 396,252 398,254 400,258 401,261 403,266 405,271 407,276 408,281 410,287 412,293 413,299 415,305 417,311 418,317 420,323 422,329 423,334 425,339 427,344 429,349 430,353 432,356 434,359 435,361 437,363 439,365 440,366 442,367 444,367 445,367 447,366 449,365 451,364 452,363 454,362 456,361 457,359 459,358 461,357 462,356 464,355 466,354 468,353 469,353 471,353 473,353 474,353 476,354 478,355 479,355 481,356 483,358 484,359 486,360 488,361 490,362 491,363 493,364 495,365 496,366 498,367 500,367 501,367 503,368 505,367 506,367 508,367 510,367 512,366 513,365 515,365 517,364 518,363 520,362 522,362 523,361 525,361 527,360 528,360 530,359 532,359 534,359 535,359 537,359 539,358 540,358 542,358 544,358 545,358 547,358 549,358 550,358 552,357 554,357 556,356 557,355 559,354 561,354 562,352 564,351 566,350 567,349 569,347 571,346 573,344 574,343 576,341 578,340 579,338 581,336 583,335 584,333 586,331 588,330 589,328 591,326 593,324 595,322 596,320 598,317 600,315 601,312 603,310 605,307 606,304 608,301 610,298 611,295 613,292 615,290 617,287 618,284 620,282 622,280 623,278 625,276 627,275 628,274 630,274 632,273 633,274 635,275 637,276 639,277 640,279 642,282 644,284 645,287 647,291 649,294 650,297 652,301 654,305 655,308 657,311 659,315 661,318 662,321 664,323 666,326 667,328 669,329 671,331 672,332 674,333 676,333 677,334 679,334 681,333 683,333 684,333 686,332 688,332 689,331 691,331 693,330 694,330 696,329 698,328 700,328 701,327 703,326 705,325 706,324 708,323 710,321 711,319 713,317 715,314 716,310 718,306 720,302 722,296 723,290 725,284 727,277 728,269 730,260 732,251 733,241 735,231 737,221 738,211 740,200 742,189 744,179 745,169 747,159 749,150 750,141 752,133 754,126 755,119 757,114 759,110 760,107 762,105 764,104 766,104 767,105 769,108 771,111 772,116 774,121 776,127 777,134 779,142 781,150 782,159 784,168 786,178 788,187 789,197 791,207 793,217 794,226 796,236 798,245 799,254 801,263 803,272 804,280 806,288 808,296 810,303 811,310 813,317 815,324 816,330 818,336 820,342 821,347 823,352 825,358 827,362 828,367 830,372 832,376 833,380 835,384 837,388 838,391 840,395 842,398 843,401 845,404 847,407 849,410 850,412 852,415 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="471,473 471,353 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/10:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="449" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,449 86,449 "/>
<text x="77" y="403" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,403 86,403 "/>
<text x="77" y="358" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,358 86,358 "/>
<text x="77" y="312" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,312 86,312 "/>
<text x="77" y="266" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,266 86,266 "/>
<text x="77" y="221" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,221 86,221 "/>
<text x="77" y="175" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,175 86,175 "/>
<text x="77" y="129" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,129 86,129 "/>
<text x="77" y="84" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,84 86,84 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="94" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
185.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="94,473 94,478 "/>
<text x="191" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
186
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="191,473 191,478 "/>
<text x="288" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
186.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="288,473 288,478 "/>
<text x="385" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="385,473 385,478 "/>
<text x="481" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="481,473 481,478 "/>
<text x="578" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="578,473 578,478 "/>
<text x="675" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="675,473 675,478 "/>
<text x="772" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="772,473 772,478 "/>
<text x="869" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="869,473 869,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,470 88,469 90,469 92,468 93,467 95,466 97,466 98,465 100,464 102,463 103,463 105,462 107,461 109,460 110,459 112,459 114,458 115,457 117,456 119,455 120,454 122,453 124,452 125,451 127,450 129,448 131,447 132,446 134,445 136,443 137,442 139,441 141,439 142,438 144,436 146,435 147,433 149,432 151,430 153,428 154,427 156,425 158,423 159,421 161,420 163,418 164,416 166,414 168,412 169,410 171,408 173,406 175,404 176,402 178,400 180,398 181,396 183,394 185,392 186,390 188,388 190,386 191,384 193,382 195,380 197,377 198,375 200,373 202,371 203,369 205,367 207,364 208,362 210,360 212,358 214,356 215,354 217,351 219,349 220,347 222,345 224,343 225,341 227,339 229,336 230,334 232,332 234,330 236,328 237,325 239,323 241,321 242,318 244,316 246,314 247,311 249,309 251,306 252,304 254,301 256,298 258,296 259,293 261,290 263,287 264,285 266,282 268,279 269,276 271,274 273,271 274,268 276,265 278,262 280,259 281,257 283,254 285,251 286,248 288,245 290,243 291,240 293,237 295,234 296,232 298,229 300,226 302,224 303,221 305,219 307,216 308,214 310,211 312,209 313,207 315,205 317,203 318,200 320,198 322,196 324,194 325,192 327,190 329,188 330,186 332,184 334,182 335,180 337,177 339,175 341,173 342,171 344,169 346,167 347,165 349,163 351,160 352,158 354,156 356,154 357,152 359,150 361,148 363,147 364,145 366,143 368,141 369,140 371,138 373,137 374,135 376,134 378,132 379,131 381,130 383,128 385,127 386,126 388,125 390,124 391,122 393,121 395,120 396,119 398,118 400,117 401,116 403,115 405,114 407,113 408,112 410,111 412,110 413,109 415,108 417,107 418,107 420,106 422,105 423,104 425,103 427,102 429,101 430,101 432,100 434,99 435,99 437,98 439,97 440,97 442,96 444,96 445,95 447,95 449,95 451,94 452,94 454,94 456,94 457,94 459,94 461,94 462,94 464,94 466,94 468,94 469,94 471,94 473,94 474,95 476,95 478,95 479,96 481,96 483,96 484,97 486,97 488,98 490,99 491,99 493,100 495,101 496,101 498,102 500,103 501,104 503,105 505,106 506,108 508,109 510,110 512,111 513,113 515,114 517,116 518,117 520,119 522,121 523,122 525,124 527,126 528,127 530,129 532,131 534,133 535,135 537,136 539,138 540,140 542,142 544,144 545,146 547,148 549,150 550,152 552,154 554,156 556,158 557,159 559,161 561,163 562,165 564,167 566,169 567,171 569,172 571,174 573,176 574,178 576,179 578,181 579,183 581,185 583,187 584,189 586,191 588,192 589,194 591,196 593,198 595,201 596,203 598,205 600,207 601,209 603,211 605,214 606,216 608,218 610,220 611,222 613,225 615,227 617,229 618,231 620,233 622,235 623,238 625,240 627,242 628,244 630,246 632,248 633,250 635,251 637,253 639,255 640,257 642,259 644,261 645,263 647,265 649,267 650,269 652,271 654,273 655,275 657,277 659,279 661,281 662,283 664,285 666,286 667,288 669,290 671,292 672,294 674,296 676,298 677,300 679,302 681,304 683,306 684,308 686,310 688,311 689,313 691,315 693,317 694,319 696,321 698,323 700,325 701,327 703,329 705,331 706,333 708,335 710,337 711,339 713,340 715,342 716,344 718,346 720,347 722,349 723,351 725,352 727,354 728,355 730,357 732,359 733,360 735,361 737,363 738,364 740,366 742,367 744,369 745,370 747,371 749,373 750,374 752,376 754,377 755,379 757,380 759,382 760,383 762,385 764,386 766,388 767,389 769,391 771,392 772,394 774,395 776,397 777,398 779,400 781,401 782,403 784,404 786,406 788,407 789,408 791,410 793,411 794,412 796,414 798,415 799,416 801,417 803,418 804,420 806,421 808,422 810,423 811,424 813,425 815,426 816,427 818,428 820,429 821,430 823,431 825,432 827,433 828,434 830,435 832,436 833,437 835,438 837,439 838,439 840,440 842,441 843,442 845,443 847,443 849,444 850,445 852,446 854,447 855,447 857,448 859,449 860,450 862,450 864,451 865,452 867,453 869,453 871,454 872,455 874,455 876,456 877,457 879,457 881,458 882,458 884,459 886,460 887,460 889,461 891,461 893,462 894,462 896,463 898,463 899,464 901,464 903,465 904,465 906,466 908,466 909,467 911,467 913,468 915,468 916,469 918,469 920,470 921,470 923,471 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,416 166,414 168,412 169,410 171,408 173,406 175,404 176,402 178,400 180,398 181,396 183,394 185,392 186,390 188,388 190,386 191,384 193,382 195,380 197,377 198,375 200,373 202,371 203,369 205,367 207,364 208,362 210,360 212,358 214,356 215,354 217,351 219,349 220,347 222,345 224,343 225,341 227,339 229,336 230,334 232,332 234,330 236,328 237,325 239,323 241,321 242,318 244,316 246,314 247,311 249,309 251,306 252,304 254,301 256,298 258,296 259,293 261,290 263,287 264,285 266,282 268,279 269,276 271,274 273,271 274,268 276,265 278,262 280,259 281,257 283,254 285,251 286,248 288,245 290,243 291,240 293,237 295,234 296,232 298,229 300,226 302,224 303,221 305,219 307,216 308,214 310,211 312,209 313,207 315,205 317,203 318,200 320,198 322,196 324,194 325,192 327,190 329,188 330,186 332,184 334,182 335,180 337,177 339,175 341,173 342,171 344,169 346,167 347,165 349,163 351,160 352,158 354,156 356,154 357,152 359,150 361,148 363,147 364,145 366,143 368,141 369,140 371,138 373,137 374,135 376,134 378,132 379,131 381,130 383,128 385,127 386,126 388,125 390,124 391,122 393,121 395,120 396,119 398,118 400,117 401,116 403,115 405,114 407,113 408,112 410,111 412,110 413,109 415,108 417,107 418,107 420,106 422,105 423,104 425,103 427,102 429,101 430,101 432,100 434,99 435,99 437,98 439,97 440,97 442,96 444,96 445,95 447,95 449,95 451,94 452,94 454,94 456,94 457,94 459,94 461,94 462,94 464,94 466,94 468,94 469,94 471,94 473,94 474,95 476,95 478,95 479,96 481,96 483,96 484,97 486,97 488,98 490,99 491,99 493,100 495,101 496,101 498,102 500,103 501,104 503,105 505,106 506,108 508,109 510,110 512,111 513,113 515,114 517,116 518,117 520,119 522,121 523,122 525,124 527,126 528,127 530,129 532,131 534,133 535,135 537,136 539,138 540,140 542,142 544,144 545,146 547,148 549,150 550,152 552,154 554,156 556,158 557,159 559,161 561,163 562,165 564,167 566,169 567,171 569,172 571,174 573,176 574,178 576,179 578,181 579,183 581,185 583,187 584,189 586,191 588,192 589,194 591,196 593,198 595,201 596,203 598,205 600,207 601,209 603,211 605,214 606,216 608,218 610,220 611,222 613,225 615,227 617,229 618,231 620,233 622,235 623,238 625,240 627,242 628,244 630,246 632,248 633,250 635,251 637,253 639,255 640,257 642,259 644,261 645,263 647,265 649,267 650,269 652,271 654,273 655,275 657,277 659,279 661,281 662,283 664,285 666,286 667,288 669,290 671,292 672,294 674,296 676,298 677,300 679,302 681,304 683,306 684,308 686,310 688,311 689,313 691,315 693,317 694,319 696,321 698,323 700,325 701,327 703,329 705,331 706,333 708,335 710,337 711,339 713,340 715,342 716,344 718,346 720,347 722,349 723,351 725,352 727,354 728,355 730,357 732,359 733,360 735,361 737,363 738,364 740,366 742,367 744,369 745,370 747,371 749,373 750,374 752,376 754,377 755,379 757,380 759,382 760,383 762,385 764,386 766,388 767,389 769,391 771,392 772,394 774,395 776,397 777,398 779,400 781,401 782,403 784,404 786,406 788,407 789,408 791,410 793,411 794,412 796,414 798,415 799,416 801,417 803,418 804,420 806,421 808,422 810,423 811,424 813,425 815,426 816,427 818,428 820,429 821,430 823,431 825,432 827,433 828,434 830,435 832,436 833,437 835,438 837,439 838,439 840,440 842,441 843,442 845,443 847,443 849,444 850,445 852,446 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="480,473 480,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

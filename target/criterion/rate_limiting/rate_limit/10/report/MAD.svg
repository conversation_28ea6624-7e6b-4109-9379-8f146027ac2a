<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/10:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="439" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,439 86,439 "/>
<text x="77" y="392" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,392 86,392 "/>
<text x="77" y="345" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,345 86,345 "/>
<text x="77" y="299" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,299 86,299 "/>
<text x="77" y="252" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,252 86,252 "/>
<text x="77" y="205" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,205 86,205 "/>
<text x="77" y="158" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,158 86,158 "/>
<text x="77" y="111" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,111 86,111 "/>
<text x="77" y="65" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,65 86,65 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="183" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="183,473 183,478 "/>
<text x="300" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="300,473 300,478 "/>
<text x="417" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="417,473 417,478 "/>
<text x="534" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="534,473 534,478 "/>
<text x="652" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="652,473 652,478 "/>
<text x="769" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="769,473 769,478 "/>
<text x="886" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="886,473 886,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,468 88,468 90,467 92,467 93,467 95,467 97,466 98,466 100,466 102,465 103,465 105,465 107,465 109,464 110,464 112,464 114,464 115,463 117,463 119,463 120,462 122,462 124,462 125,461 127,461 129,461 131,460 132,460 134,459 136,459 137,458 139,458 141,457 142,456 144,456 146,455 147,454 149,453 151,452 153,451 154,450 156,449 158,448 159,448 161,447 163,446 164,445 166,444 168,443 169,442 171,441 173,440 175,439 176,438 178,438 180,437 181,436 183,435 185,434 186,434 188,433 190,432 191,431 193,430 195,429 197,429 198,428 200,427 202,426 203,425 205,423 207,422 208,421 210,420 212,419 214,418 215,417 217,415 219,414 220,413 222,412 224,410 225,409 227,408 229,407 230,406 232,405 234,404 236,403 237,402 239,401 241,401 242,400 244,399 246,399 247,398 249,398 251,397 252,397 254,396 256,396 258,396 259,395 261,395 263,394 264,394 266,394 268,393 269,392 271,392 273,391 274,390 276,389 278,389 280,388 281,386 283,385 285,384 286,383 288,381 290,380 291,378 293,377 295,375 296,374 298,372 300,370 302,368 303,367 305,365 307,364 308,362 310,361 312,359 313,358 315,356 317,355 318,354 320,352 322,351 324,350 325,349 327,347 329,346 330,345 332,343 334,342 335,340 337,339 339,337 341,335 342,333 344,331 346,328 347,326 349,323 351,321 352,318 354,315 356,313 357,310 359,307 361,305 363,302 364,300 366,298 368,296 369,294 371,292 373,290 374,289 376,288 378,287 379,286 381,286 383,285 385,285 386,285 388,285 390,285 391,285 393,285 395,286 396,286 398,287 400,287 401,287 403,288 405,288 407,288 408,287 410,287 412,287 413,286 415,285 417,284 418,283 420,281 422,280 423,278 425,276 427,274 429,272 430,270 432,268 434,266 435,263 437,261 439,259 440,256 442,254 444,251 445,248 447,245 449,242 451,239 452,235 454,232 456,227 457,223 459,218 461,213 462,207 464,202 466,195 468,189 469,182 471,175 473,168 474,161 476,154 478,147 479,140 481,133 483,127 484,121 486,115 488,110 490,105 491,101 493,98 495,96 496,94 498,93 500,93 501,93 503,95 505,97 506,100 508,103 510,107 512,112 513,117 515,123 517,129 518,135 520,141 522,148 523,154 525,161 527,167 528,173 530,180 532,186 534,191 535,197 537,202 539,207 540,211 542,216 544,220 545,223 547,227 549,230 550,233 552,236 554,239 556,242 557,244 559,247 561,249 562,251 564,254 566,256 567,258 569,260 571,262 573,265 574,267 576,269 578,271 579,274 581,276 583,279 584,281 586,284 588,287 589,290 591,292 593,295 595,298 596,301 598,304 600,307 601,310 603,312 605,315 606,317 608,320 610,322 611,324 613,326 615,328 617,330 618,331 620,333 622,334 623,335 625,336 627,337 628,338 630,339 632,340 633,341 635,341 637,342 639,343 640,343 642,344 644,344 645,345 647,346 649,346 650,347 652,347 654,348 655,349 657,350 659,350 661,351 662,353 664,354 666,355 667,357 669,358 671,360 672,362 674,364 676,366 677,368 679,371 681,373 683,375 684,378 686,380 688,382 689,384 691,386 693,388 694,390 696,392 698,393 700,394 701,396 703,397 705,397 706,398 708,399 710,399 711,400 713,400 715,401 716,401 718,402 720,402 722,403 723,403 725,404 727,405 728,406 730,406 732,407 733,408 735,409 737,410 738,411 740,412 742,413 744,413 745,414 747,415 749,415 750,416 752,416 754,416 755,417 757,417 759,417 760,417 762,417 764,417 766,417 767,417 769,417 771,418 772,418 774,418 776,418 777,419 779,419 781,420 782,420 784,421 786,421 788,422 789,423 791,424 793,424 794,425 796,426 798,427 799,428 801,429 803,430 804,431 806,433 808,434 810,435 811,436 813,437 815,439 816,440 818,441 820,443 821,444 823,445 825,446 827,448 828,449 830,450 832,451 833,451 835,452 837,453 838,454 840,454 842,455 843,455 845,455 847,456 849,456 850,456 852,456 854,456 855,456 857,457 859,457 860,457 862,457 864,457 865,457 867,457 869,458 871,458 872,458 874,459 876,459 877,459 879,460 881,460 882,461 884,461 886,462 887,463 889,463 891,464 893,464 894,465 896,465 898,466 899,466 901,467 903,467 904,468 906,468 908,469 909,469 911,469 913,470 915,470 916,470 918,471 920,471 921,471 923,471 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,445 166,444 168,443 169,442 171,441 173,440 175,439 176,438 178,438 180,437 181,436 183,435 185,434 186,434 188,433 190,432 191,431 193,430 195,429 197,429 198,428 200,427 202,426 203,425 205,423 207,422 208,421 210,420 212,419 214,418 215,417 217,415 219,414 220,413 222,412 224,410 225,409 227,408 229,407 230,406 232,405 234,404 236,403 237,402 239,401 241,401 242,400 244,399 246,399 247,398 249,398 251,397 252,397 254,396 256,396 258,396 259,395 261,395 263,394 264,394 266,394 268,393 269,392 271,392 273,391 274,390 276,389 278,389 280,388 281,386 283,385 285,384 286,383 288,381 290,380 291,378 293,377 295,375 296,374 298,372 300,370 302,368 303,367 305,365 307,364 308,362 310,361 312,359 313,358 315,356 317,355 318,354 320,352 322,351 324,350 325,349 327,347 329,346 330,345 332,343 334,342 335,340 337,339 339,337 341,335 342,333 344,331 346,328 347,326 349,323 351,321 352,318 354,315 356,313 357,310 359,307 361,305 363,302 364,300 366,298 368,296 369,294 371,292 373,290 374,289 376,288 378,287 379,286 381,286 383,285 385,285 386,285 388,285 390,285 391,285 393,285 395,286 396,286 398,287 400,287 401,287 403,288 405,288 407,288 408,287 410,287 412,287 413,286 415,285 417,284 418,283 420,281 422,280 423,278 425,276 427,274 429,272 430,270 432,268 434,266 435,263 437,261 439,259 440,256 442,254 444,251 445,248 447,245 449,242 451,239 452,235 454,232 456,227 457,223 459,218 461,213 462,207 464,202 466,195 468,189 469,182 471,175 473,168 474,161 476,154 478,147 479,140 481,133 483,127 484,121 486,115 488,110 490,105 491,101 493,98 495,96 496,94 498,93 500,93 501,93 503,95 505,97 506,100 508,103 510,107 512,112 513,117 515,123 517,129 518,135 520,141 522,148 523,154 525,161 527,167 528,173 530,180 532,186 534,191 535,197 537,202 539,207 540,211 542,216 544,220 545,223 547,227 549,230 550,233 552,236 554,239 556,242 557,244 559,247 561,249 562,251 564,254 566,256 567,258 569,260 571,262 573,265 574,267 576,269 578,271 579,274 581,276 583,279 584,281 586,284 588,287 589,290 591,292 593,295 595,298 596,301 598,304 600,307 601,310 603,312 605,315 606,317 608,320 610,322 611,324 613,326 615,328 617,330 618,331 620,333 622,334 623,335 625,336 627,337 628,338 630,339 632,340 633,341 635,341 637,342 639,343 640,343 642,344 644,344 645,345 647,346 649,346 650,347 652,347 654,348 655,349 657,350 659,350 661,351 662,353 664,354 666,355 667,357 669,358 671,360 672,362 674,364 676,366 677,368 679,371 681,373 683,375 684,378 686,380 688,382 689,384 691,386 693,388 694,390 696,392 698,393 700,394 701,396 703,397 705,397 706,398 708,399 710,399 711,400 713,400 715,401 716,401 718,402 720,402 722,403 723,403 725,404 727,405 728,406 730,406 732,407 733,408 735,409 737,410 738,411 740,412 742,413 744,413 745,414 747,415 749,415 750,416 752,416 754,416 755,417 757,417 759,417 760,417 762,417 764,417 766,417 767,417 769,417 771,418 772,418 774,418 776,418 777,419 779,419 781,420 782,420 784,421 786,421 788,422 789,423 791,424 793,424 794,425 796,426 798,427 799,428 801,429 803,430 804,431 806,433 808,434 810,435 811,436 813,437 815,439 816,440 818,441 820,443 821,444 823,445 825,446 827,448 828,449 830,450 832,451 833,451 835,452 837,453 838,454 840,454 842,455 843,455 845,455 847,456 849,456 850,456 852,456 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="439,473 439,258 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

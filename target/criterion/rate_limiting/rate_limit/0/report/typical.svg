<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/0:typical
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="404" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,404 86,404 "/>
<text x="77" y="315" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,315 86,315 "/>
<text x="77" y="226" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,226 86,226 "/>
<text x="77" y="137" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,137 86,137 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="138" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
977
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="138,473 138,478 "/>
<text x="234" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
978
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="234,473 234,478 "/>
<text x="331" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
979
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="331,473 331,478 "/>
<text x="427" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
980
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="427,473 427,478 "/>
<text x="523" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
981
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="523,473 523,478 "/>
<text x="620" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
982
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="620,473 620,478 "/>
<text x="716" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
983
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="716,473 716,478 "/>
<text x="812" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
984
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="812,473 812,478 "/>
<text x="908" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
985
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="908,473 908,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,470 93,469 95,469 97,468 98,467 100,467 102,466 103,466 105,465 107,464 109,464 110,463 112,462 114,461 115,461 117,460 119,459 120,458 122,457 124,456 125,455 127,455 129,454 131,453 132,452 134,451 136,450 137,449 139,447 141,446 142,445 144,444 146,443 147,442 149,440 151,439 153,438 154,437 156,435 158,434 159,433 161,431 163,430 164,429 166,427 168,426 169,424 171,423 173,422 175,420 176,419 178,417 180,416 181,414 183,412 185,411 186,409 188,407 190,406 191,404 193,402 195,401 197,399 198,397 200,395 202,393 203,392 205,390 207,388 208,386 210,384 212,382 214,381 215,379 217,377 219,375 220,373 222,371 224,369 225,368 227,366 229,364 230,362 232,360 234,358 236,356 237,354 239,352 241,350 242,348 244,346 246,344 247,341 249,339 251,337 252,335 254,332 256,330 258,328 259,325 261,323 263,320 264,318 266,315 268,313 269,311 271,308 273,306 274,303 276,301 278,298 280,296 281,293 283,291 285,288 286,286 288,283 290,281 291,278 293,276 295,273 296,271 298,268 300,266 302,263 303,261 305,258 307,256 308,253 310,251 312,248 313,246 315,243 317,240 318,238 320,235 322,232 324,230 325,227 327,224 329,222 330,219 332,216 334,213 335,210 337,208 339,205 341,202 342,199 344,196 346,194 347,191 349,188 351,186 352,183 354,181 356,178 357,176 359,173 361,171 363,168 364,166 366,164 368,162 369,160 371,158 373,156 374,154 376,152 378,150 379,149 381,147 383,145 385,144 386,142 388,140 390,139 391,137 393,136 395,134 396,133 398,131 400,130 401,128 403,127 405,125 407,124 408,123 410,121 412,120 413,119 415,117 417,116 418,115 420,113 422,112 423,111 425,110 427,108 429,107 430,106 432,105 434,104 435,103 437,102 439,101 440,100 442,99 444,99 445,98 447,97 449,96 451,96 452,95 454,95 456,94 457,94 459,94 461,94 462,94 464,94 466,94 468,94 469,94 471,94 473,94 474,94 476,95 478,95 479,95 481,96 483,96 484,97 486,97 488,98 490,98 491,99 493,99 495,100 496,101 498,101 500,102 501,103 503,103 505,104 506,105 508,106 510,106 512,107 513,108 515,109 517,110 518,111 520,112 522,113 523,114 525,115 527,116 528,118 530,119 532,120 534,121 535,123 537,124 539,126 540,127 542,129 544,131 545,132 547,134 549,136 550,138 552,140 554,141 556,143 557,145 559,147 561,149 562,150 564,152 566,154 567,156 569,157 571,159 573,161 574,162 576,164 578,165 579,167 581,168 583,170 584,171 586,173 588,174 589,176 591,177 593,179 595,181 596,182 598,184 600,186 601,188 603,190 605,192 606,194 608,196 610,198 611,201 613,203 615,205 617,208 618,210 620,213 622,215 623,218 625,220 627,223 628,225 630,228 632,230 633,233 635,235 637,238 639,240 640,243 642,245 644,248 645,250 647,252 649,255 650,257 652,259 654,261 655,263 657,265 659,268 661,270 662,272 664,274 666,276 667,278 669,280 671,282 672,284 674,286 676,289 677,291 679,293 681,295 683,297 684,300 686,302 688,304 689,307 691,309 693,311 694,314 696,316 698,318 700,321 701,323 703,325 705,327 706,329 708,331 710,333 711,335 713,337 715,339 716,341 718,343 720,344 722,346 723,348 725,349 727,351 728,353 730,354 732,356 733,358 735,359 737,361 738,363 740,364 742,366 744,368 745,370 747,371 749,373 750,375 752,376 754,378 755,380 757,381 759,383 760,385 762,386 764,388 766,389 767,391 769,392 771,394 772,395 774,396 776,398 777,399 779,400 781,401 782,403 784,404 786,405 788,406 789,408 791,409 793,410 794,411 796,413 798,414 799,415 801,417 803,418 804,419 806,420 808,422 810,423 811,424 813,425 815,426 816,427 818,429 820,430 821,431 823,432 825,433 827,434 828,435 830,436 832,437 833,438 835,438 837,439 838,440 840,441 842,442 843,443 845,443 847,444 849,445 850,446 852,446 854,447 855,448 857,448 859,449 860,450 862,450 864,451 865,452 867,452 869,453 871,454 872,454 874,455 876,456 877,456 879,457 881,458 882,458 884,459 886,460 887,461 889,461 891,462 893,463 894,463 896,464 898,464 899,465 901,466 903,466 904,467 906,467 908,468 909,468 911,468 913,469 915,469 916,470 918,470 920,470 921,471 923,471 925,471 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,429 166,427 168,426 169,424 171,423 173,422 175,420 176,419 178,417 180,416 181,414 183,412 185,411 186,409 188,407 190,406 191,404 193,402 195,401 197,399 198,397 200,395 202,393 203,392 205,390 207,388 208,386 210,384 212,382 214,381 215,379 217,377 219,375 220,373 222,371 224,369 225,368 227,366 229,364 230,362 232,360 234,358 236,356 237,354 239,352 241,350 242,348 244,346 246,344 247,341 249,339 251,337 252,335 254,332 256,330 258,328 259,325 261,323 263,320 264,318 266,315 268,313 269,311 271,308 273,306 274,303 276,301 278,298 280,296 281,293 283,291 285,288 286,286 288,283 290,281 291,278 293,276 295,273 296,271 298,268 300,266 302,263 303,261 305,258 307,256 308,253 310,251 312,248 313,246 315,243 317,240 318,238 320,235 322,232 324,230 325,227 327,224 329,222 330,219 332,216 334,213 335,210 337,208 339,205 341,202 342,199 344,196 346,194 347,191 349,188 351,186 352,183 354,181 356,178 357,176 359,173 361,171 363,168 364,166 366,164 368,162 369,160 371,158 373,156 374,154 376,152 378,150 379,149 381,147 383,145 385,144 386,142 388,140 390,139 391,137 393,136 395,134 396,133 398,131 400,130 401,128 403,127 405,125 407,124 408,123 410,121 412,120 413,119 415,117 417,116 418,115 420,113 422,112 423,111 425,110 427,108 429,107 430,106 432,105 434,104 435,103 437,102 439,101 440,100 442,99 444,99 445,98 447,97 449,96 451,96 452,95 454,95 456,94 457,94 459,94 461,94 462,94 464,94 466,94 468,94 469,94 471,94 473,94 474,94 476,95 478,95 479,95 481,96 483,96 484,97 486,97 488,98 490,98 491,99 493,99 495,100 496,101 498,101 500,102 501,103 503,103 505,104 506,105 508,106 510,106 512,107 513,108 515,109 517,110 518,111 520,112 522,113 523,114 525,115 527,116 528,118 530,119 532,120 534,121 535,123 537,124 539,126 540,127 542,129 544,131 545,132 547,134 549,136 550,138 552,140 554,141 556,143 557,145 559,147 561,149 562,150 564,152 566,154 567,156 569,157 571,159 573,161 574,162 576,164 578,165 579,167 581,168 583,170 584,171 586,173 588,174 589,176 591,177 593,179 595,181 596,182 598,184 600,186 601,188 603,190 605,192 606,194 608,196 610,198 611,201 613,203 615,205 617,208 618,210 620,213 622,215 623,218 625,220 627,223 628,225 630,228 632,230 633,233 635,235 637,238 639,240 640,243 642,245 644,248 645,250 647,252 649,255 650,257 652,259 654,261 655,263 657,265 659,268 661,270 662,272 664,274 666,276 667,278 669,280 671,282 672,284 674,286 676,289 677,291 679,293 681,295 683,297 684,300 686,302 688,304 689,307 691,309 693,311 694,314 696,316 698,318 700,321 701,323 703,325 705,327 706,329 708,331 710,333 711,335 713,337 715,339 716,341 718,343 720,344 722,346 723,348 725,349 727,351 728,353 730,354 732,356 733,358 735,359 737,361 738,363 740,364 742,366 744,368 745,370 747,371 749,373 750,375 752,376 754,378 755,380 757,381 759,383 760,385 762,386 764,388 766,389 767,391 769,392 771,394 772,395 774,396 776,398 777,399 779,400 781,401 782,403 784,404 786,405 788,406 789,408 791,409 793,410 794,411 796,413 798,414 799,415 801,417 803,418 804,419 806,420 808,422 810,423 811,424 813,425 815,426 816,427 818,429 820,430 821,431 823,432 825,433 827,434 828,435 830,436 832,437 833,438 835,438 837,439 838,440 840,441 842,442 843,443 845,443 847,444 849,445 850,446 852,446 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="483,473 483,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

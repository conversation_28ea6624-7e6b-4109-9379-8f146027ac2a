<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/0:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="443" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,443 86,443 "/>
<text x="77" y="392" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,392 86,392 "/>
<text x="77" y="340" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,340 86,340 "/>
<text x="77" y="289" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,289 86,289 "/>
<text x="77" y="238" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,238 86,238 "/>
<text x="77" y="187" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,187 86,187 "/>
<text x="77" y="135" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,135 86,135 "/>
<text x="77" y="84" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,84 86,84 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="106" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
976
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="106,473 106,478 "/>
<text x="237" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
978
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="237,473 237,478 "/>
<text x="367" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
980
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="367,473 367,478 "/>
<text x="498" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
982
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="498,473 498,478 "/>
<text x="629" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
984
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="629,473 629,478 "/>
<text x="759" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
986
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="759,473 759,478 "/>
<text x="890" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
988
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="890,473 890,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,470 95,469 97,468 98,467 100,467 102,466 103,465 105,465 107,464 109,463 110,463 112,462 114,461 115,461 117,460 119,459 120,457 122,456 124,455 125,453 127,451 129,448 131,446 132,443 134,440 136,437 137,433 139,429 141,425 142,420 144,415 146,411 147,405 149,400 151,395 153,390 154,385 156,379 158,374 159,369 161,364 163,360 164,355 166,351 168,347 169,343 171,340 173,336 175,333 176,330 178,327 180,325 181,322 183,320 185,318 186,316 188,314 190,312 191,311 193,309 195,307 197,305 198,304 200,302 202,301 203,299 205,298 207,296 208,295 210,293 212,292 214,290 215,289 217,288 219,287 220,285 222,284 224,283 225,282 227,281 229,279 230,278 232,277 234,276 236,275 237,273 239,272 241,271 242,269 244,267 246,266 247,264 249,262 251,260 252,259 254,257 256,255 258,253 259,251 261,249 263,247 264,245 266,243 268,241 269,239 271,238 273,236 274,235 276,234 278,232 280,231 281,231 283,230 285,229 286,229 288,228 290,228 291,228 293,227 295,227 296,227 298,227 300,226 302,226 303,226 305,225 307,225 308,224 310,224 312,223 313,223 315,223 317,222 318,222 320,221 322,221 324,221 325,221 327,222 329,222 330,223 332,224 334,225 335,226 337,228 339,230 341,232 342,234 344,237 346,240 347,242 349,245 351,248 352,251 354,253 356,255 357,258 359,259 361,261 363,261 364,262 366,262 368,261 369,260 371,258 373,256 374,254 376,251 378,248 379,245 381,242 383,239 385,236 386,233 388,230 390,228 391,226 393,225 395,224 396,223 398,223 400,224 401,225 403,226 405,227 407,229 408,231 410,233 412,235 413,236 415,238 417,239 418,239 420,240 422,239 423,239 425,237 427,235 429,233 430,230 432,226 434,222 435,218 437,213 439,208 440,203 442,198 444,193 445,188 447,182 449,177 451,172 452,167 454,163 456,158 457,154 459,150 461,147 462,143 464,140 466,137 468,134 469,131 471,129 473,126 474,124 476,121 478,119 479,117 481,114 483,112 484,110 486,107 488,105 490,103 491,101 493,100 495,98 496,97 498,95 500,95 501,94 503,94 505,94 506,94 508,95 510,96 512,97 513,99 515,101 517,103 518,106 520,109 522,112 523,115 525,119 527,123 528,127 530,131 532,136 534,140 535,145 537,149 539,154 540,159 542,164 544,169 545,175 547,180 549,186 550,191 552,197 554,203 556,209 557,215 559,222 561,229 562,236 564,243 566,250 567,257 569,264 571,272 573,279 574,287 576,294 578,301 579,308 581,315 583,321 584,327 586,332 588,337 589,342 591,345 593,348 595,351 596,353 598,354 600,354 601,354 603,353 605,351 606,349 608,346 610,342 611,339 613,335 615,330 617,325 618,321 620,316 622,311 623,306 625,301 627,297 628,293 630,289 632,286 633,283 635,281 637,279 639,279 640,278 642,279 644,280 645,282 647,285 649,288 650,292 652,296 654,300 655,305 657,311 659,316 661,322 662,328 664,334 666,339 667,345 669,350 671,355 672,360 674,365 676,369 677,372 679,376 681,378 683,380 684,382 686,383 688,384 689,384 691,384 693,383 694,382 696,380 698,378 700,375 701,372 703,369 705,366 706,362 708,359 710,355 711,351 713,347 715,343 716,340 718,336 720,333 722,329 723,326 725,323 727,320 728,317 730,315 732,312 733,310 735,308 737,306 738,303 740,302 742,300 744,298 745,296 747,295 749,293 750,291 752,290 754,288 755,287 757,286 759,284 760,283 762,282 764,280 766,279 767,278 769,277 771,276 772,275 774,274 776,273 777,273 779,273 781,273 782,274 784,275 786,277 788,279 789,281 791,284 793,288 794,292 796,297 798,302 799,308 801,314 803,321 804,328 806,335 808,342 810,350 811,357 813,364 815,371 816,378 818,385 820,391 821,397 823,402 825,407 827,411 828,416 830,419 832,422 833,425 835,427 837,429 838,431 840,432 842,434 843,435 845,436 847,437 849,438 850,439 852,440 854,441 855,442 857,443 859,445 860,446 862,448 864,449 865,451 867,452 869,454 871,456 872,457 874,459 876,460 877,461 879,462 881,463 882,464 884,465 886,466 887,466 889,466 891,467 893,467 894,467 896,467 898,467 899,467 901,467 903,466 904,466 906,466 908,466 909,466 911,467 913,467 915,467 916,467 918,468 920,468 921,469 923,469 925,470 926,470 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,355 166,351 168,347 169,343 171,340 173,336 175,333 176,330 178,327 180,325 181,322 183,320 185,318 186,316 188,314 190,312 191,311 193,309 195,307 197,305 198,304 200,302 202,301 203,299 205,298 207,296 208,295 210,293 212,292 214,290 215,289 217,288 219,287 220,285 222,284 224,283 225,282 227,281 229,279 230,278 232,277 234,276 236,275 237,273 239,272 241,271 242,269 244,267 246,266 247,264 249,262 251,260 252,259 254,257 256,255 258,253 259,251 261,249 263,247 264,245 266,243 268,241 269,239 271,238 273,236 274,235 276,234 278,232 280,231 281,231 283,230 285,229 286,229 288,228 290,228 291,228 293,227 295,227 296,227 298,227 300,226 302,226 303,226 305,225 307,225 308,224 310,224 312,223 313,223 315,223 317,222 318,222 320,221 322,221 324,221 325,221 327,222 329,222 330,223 332,224 334,225 335,226 337,228 339,230 341,232 342,234 344,237 346,240 347,242 349,245 351,248 352,251 354,253 356,255 357,258 359,259 361,261 363,261 364,262 366,262 368,261 369,260 371,258 373,256 374,254 376,251 378,248 379,245 381,242 383,239 385,236 386,233 388,230 390,228 391,226 393,225 395,224 396,223 398,223 400,224 401,225 403,226 405,227 407,229 408,231 410,233 412,235 413,236 415,238 417,239 418,239 420,240 422,239 423,239 425,237 427,235 429,233 430,230 432,226 434,222 435,218 437,213 439,208 440,203 442,198 444,193 445,188 447,182 449,177 451,172 452,167 454,163 456,158 457,154 459,150 461,147 462,143 464,140 466,137 468,134 469,131 471,129 473,126 474,124 476,121 478,119 479,117 481,114 483,112 484,110 486,107 488,105 490,103 491,101 493,100 495,98 496,97 498,95 500,95 501,94 503,94 505,94 506,94 508,95 510,96 512,97 513,99 515,101 517,103 518,106 520,109 522,112 523,115 525,119 527,123 528,127 530,131 532,136 534,140 535,145 537,149 539,154 540,159 542,164 544,169 545,175 547,180 549,186 550,191 552,197 554,203 556,209 557,215 559,222 561,229 562,236 564,243 566,250 567,257 569,264 571,272 573,279 574,287 576,294 578,301 579,308 581,315 583,321 584,327 586,332 588,337 589,342 591,345 593,348 595,351 596,353 598,354 600,354 601,354 603,353 605,351 606,349 608,346 610,342 611,339 613,335 615,330 617,325 618,321 620,316 622,311 623,306 625,301 627,297 628,293 630,289 632,286 633,283 635,281 637,279 639,279 640,278 642,279 644,280 645,282 647,285 649,288 650,292 652,296 654,300 655,305 657,311 659,316 661,322 662,328 664,334 666,339 667,345 669,350 671,355 672,360 674,365 676,369 677,372 679,376 681,378 683,380 684,382 686,383 688,384 689,384 691,384 693,383 694,382 696,380 698,378 700,375 701,372 703,369 705,366 706,362 708,359 710,355 711,351 713,347 715,343 716,340 718,336 720,333 722,329 723,326 725,323 727,320 728,317 730,315 732,312 733,310 735,308 737,306 738,303 740,302 742,300 744,298 745,296 747,295 749,293 750,291 752,290 754,288 755,287 757,286 759,284 760,283 762,282 764,280 766,279 767,278 769,277 771,276 772,275 774,274 776,273 777,273 779,273 781,273 782,274 784,275 786,277 788,279 789,281 791,284 793,288 794,292 796,297 798,302 799,308 801,314 803,321 804,328 806,335 808,342 810,350 811,357 813,364 815,371 816,378 818,385 820,391 821,397 823,402 825,407 827,411 828,416 830,419 832,422 833,425 835,427 837,429 838,431 840,432 842,434 843,435 845,436 847,437 849,438 850,439 852,440 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="478,473 478,118 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/0:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="422" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,422 86,422 "/>
<text x="77" y="353" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,353 86,353 "/>
<text x="77" y="285" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,285 86,285 "/>
<text x="77" y="216" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,216 86,216 "/>
<text x="77" y="147" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,147 86,147 "/>
<text x="77" y="79" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,79 86,79 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="103" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="103,473 103,478 "/>
<text x="199" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="199,473 199,478 "/>
<text x="295" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="295,473 295,478 "/>
<text x="392" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="392,473 392,478 "/>
<text x="488" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="488,473 488,478 "/>
<text x="584" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="584,473 584,478 "/>
<text x="680" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="680,473 680,478 "/>
<text x="776" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
180
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="776,473 776,478 "/>
<text x="873" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="873,473 873,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,469 92,468 93,466 95,464 97,463 98,461 100,459 102,458 103,456 105,455 107,454 109,453 110,452 112,451 114,450 115,450 117,450 119,449 120,449 122,449 124,449 125,449 127,449 129,449 131,449 132,449 134,448 136,448 137,447 139,446 141,445 142,444 144,442 146,441 147,439 149,438 151,436 153,434 154,432 156,430 158,429 159,427 161,426 163,424 164,423 166,422 168,421 169,420 171,420 173,419 175,419 176,419 178,418 180,418 181,418 183,417 185,417 186,417 188,416 190,415 191,414 193,413 195,412 197,411 198,410 200,409 202,407 203,406 205,405 207,404 208,403 210,402 212,401 214,400 215,399 217,399 219,398 220,398 222,397 224,397 225,397 227,396 229,396 230,396 232,395 234,395 236,394 237,394 239,393 241,393 242,392 244,391 246,391 247,391 249,390 251,390 252,390 254,390 256,389 258,389 259,389 261,389 263,389 264,389 266,389 268,389 269,388 271,388 273,388 274,387 276,387 278,386 280,385 281,385 283,384 285,383 286,383 288,382 290,381 291,380 293,380 295,379 296,378 298,377 300,377 302,376 303,375 305,374 307,373 308,372 310,371 312,370 313,369 315,368 317,366 318,365 320,363 322,361 324,359 325,357 327,355 329,352 330,349 332,347 334,344 335,341 337,338 339,334 341,331 342,327 344,324 346,320 347,316 349,312 351,309 352,305 354,301 356,297 357,293 359,289 361,285 363,281 364,277 366,273 368,269 369,265 371,262 373,258 374,254 376,250 378,246 379,242 381,238 383,234 385,231 386,227 388,223 390,220 391,216 393,213 395,210 396,207 398,204 400,201 401,198 403,195 405,193 407,190 408,188 410,186 412,184 413,182 415,180 417,179 418,177 420,176 422,175 423,174 425,173 427,173 429,172 430,172 432,172 434,172 435,172 437,172 439,172 440,173 442,173 444,174 445,175 447,176 449,177 451,177 452,178 454,179 456,180 457,181 459,182 461,182 462,183 464,183 466,184 468,184 469,184 471,184 473,184 474,183 476,183 478,182 479,181 481,180 483,179 484,178 486,177 488,175 490,174 491,172 493,170 495,168 496,166 498,164 500,161 501,159 503,156 505,154 506,151 508,148 510,145 512,142 513,139 515,136 517,133 518,130 520,127 522,124 523,121 525,118 527,116 528,113 530,111 532,108 534,106 535,104 537,102 539,100 540,99 542,97 544,96 545,95 547,94 549,94 550,93 552,93 554,93 556,94 557,94 559,95 561,96 562,97 564,98 566,99 567,100 569,102 571,104 573,105 574,107 576,109 578,111 579,113 581,115 583,117 584,119 586,122 588,124 589,126 591,128 593,130 595,132 596,134 598,136 600,138 601,140 603,141 605,143 606,145 608,146 610,148 611,149 613,150 615,151 617,152 618,153 620,154 622,155 623,156 625,156 627,157 628,158 630,158 632,158 633,159 635,159 637,159 639,160 640,160 642,160 644,161 645,161 647,162 649,162 650,163 652,164 654,165 655,166 657,167 659,168 661,169 662,171 664,173 666,174 667,176 669,178 671,181 672,183 674,186 676,188 677,191 679,194 681,197 683,200 684,203 686,206 688,209 689,212 691,215 693,218 694,221 696,224 698,227 700,230 701,232 703,235 705,238 706,240 708,243 710,245 711,247 713,250 715,252 716,254 718,256 720,258 722,260 723,262 725,264 727,266 728,268 730,270 732,272 733,274 735,276 737,278 738,280 740,282 742,284 744,286 745,288 747,290 749,292 750,294 752,297 754,299 755,301 757,303 759,306 760,308 762,310 764,313 766,315 767,318 769,320 771,323 772,325 774,328 776,330 777,333 779,335 781,338 782,341 784,343 786,346 788,348 789,351 791,354 793,356 794,359 796,361 798,364 799,366 801,368 803,371 804,373 806,375 808,377 810,379 811,381 813,383 815,385 816,387 818,389 820,391 821,393 823,395 825,396 827,398 828,400 830,401 832,403 833,404 835,406 837,408 838,409 840,411 842,412 843,414 845,415 847,417 849,418 850,420 852,421 854,422 855,424 857,425 859,426 860,428 862,429 864,430 865,432 867,433 869,434 871,435 872,436 874,438 876,439 877,440 879,441 881,442 882,443 884,444 886,445 887,446 889,447 891,449 893,450 894,451 896,452 898,453 899,454 901,455 903,456 904,456 906,457 908,458 909,459 911,460 913,461 915,462 916,463 918,463 920,464 921,465 923,466 925,466 926,467 928,468 930,468 932,469 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,423 166,422 168,421 169,420 171,420 173,419 175,419 176,419 178,418 180,418 181,418 183,417 185,417 186,417 188,416 190,415 191,414 193,413 195,412 197,411 198,410 200,409 202,407 203,406 205,405 207,404 208,403 210,402 212,401 214,400 215,399 217,399 219,398 220,398 222,397 224,397 225,397 227,396 229,396 230,396 232,395 234,395 236,394 237,394 239,393 241,393 242,392 244,391 246,391 247,391 249,390 251,390 252,390 254,390 256,389 258,389 259,389 261,389 263,389 264,389 266,389 268,389 269,388 271,388 273,388 274,387 276,387 278,386 280,385 281,385 283,384 285,383 286,383 288,382 290,381 291,380 293,380 295,379 296,378 298,377 300,377 302,376 303,375 305,374 307,373 308,372 310,371 312,370 313,369 315,368 317,366 318,365 320,363 322,361 324,359 325,357 327,355 329,352 330,349 332,347 334,344 335,341 337,338 339,334 341,331 342,327 344,324 346,320 347,316 349,312 351,309 352,305 354,301 356,297 357,293 359,289 361,285 363,281 364,277 366,273 368,269 369,265 371,262 373,258 374,254 376,250 378,246 379,242 381,238 383,234 385,231 386,227 388,223 390,220 391,216 393,213 395,210 396,207 398,204 400,201 401,198 403,195 405,193 407,190 408,188 410,186 412,184 413,182 415,180 417,179 418,177 420,176 422,175 423,174 425,173 427,173 429,172 430,172 432,172 434,172 435,172 437,172 439,172 440,173 442,173 444,174 445,175 447,176 449,177 451,177 452,178 454,179 456,180 457,181 459,182 461,182 462,183 464,183 466,184 468,184 469,184 471,184 473,184 474,183 476,183 478,182 479,181 481,180 483,179 484,178 486,177 488,175 490,174 491,172 493,170 495,168 496,166 498,164 500,161 501,159 503,156 505,154 506,151 508,148 510,145 512,142 513,139 515,136 517,133 518,130 520,127 522,124 523,121 525,118 527,116 528,113 530,111 532,108 534,106 535,104 537,102 539,100 540,99 542,97 544,96 545,95 547,94 549,94 550,93 552,93 554,93 556,94 557,94 559,95 561,96 562,97 564,98 566,99 567,100 569,102 571,104 573,105 574,107 576,109 578,111 579,113 581,115 583,117 584,119 586,122 588,124 589,126 591,128 593,130 595,132 596,134 598,136 600,138 601,140 603,141 605,143 606,145 608,146 610,148 611,149 613,150 615,151 617,152 618,153 620,154 622,155 623,156 625,156 627,157 628,158 630,158 632,158 633,159 635,159 637,159 639,160 640,160 642,160 644,161 645,161 647,162 649,162 650,163 652,164 654,165 655,166 657,167 659,168 661,169 662,171 664,173 666,174 667,176 669,178 671,181 672,183 674,186 676,188 677,191 679,194 681,197 683,200 684,203 686,206 688,209 689,212 691,215 693,218 694,221 696,224 698,227 700,230 701,232 703,235 705,238 706,240 708,243 710,245 711,247 713,250 715,252 716,254 718,256 720,258 722,260 723,262 725,264 727,266 728,268 730,270 732,272 733,274 735,276 737,278 738,280 740,282 742,284 744,286 745,288 747,290 749,292 750,294 752,297 754,299 755,301 757,303 759,306 760,308 762,310 764,313 766,315 767,318 769,320 771,323 772,325 774,328 776,330 777,333 779,335 781,338 782,341 784,343 786,346 788,348 789,351 791,354 793,356 794,359 796,361 798,364 799,366 801,368 803,371 804,373 806,375 808,377 810,379 811,381 813,383 815,385 816,387 818,389 820,391 821,393 823,395 825,396 827,398 828,400 830,401 832,403 833,404 835,406 837,408 838,409 840,411 842,412 843,414 845,415 847,417 849,418 850,420 852,421 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="561,473 561,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

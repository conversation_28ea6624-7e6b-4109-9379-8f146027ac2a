<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/0:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="426" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,426 86,426 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="310" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,310 86,310 "/>
<text x="77" y="252" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,252 86,252 "/>
<text x="77" y="194" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,194 86,194 "/>
<text x="77" y="136" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,136 86,136 "/>
<text x="77" y="78" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,78 86,78 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="121" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="121,473 121,478 "/>
<text x="210" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="210,473 210,478 "/>
<text x="300" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="300,473 300,478 "/>
<text x="389" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="389,473 389,478 "/>
<text x="479" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="479,473 479,478 "/>
<text x="569" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="569,473 569,478 "/>
<text x="658" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="658,473 658,478 "/>
<text x="748" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="748,473 748,478 "/>
<text x="837" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="837,473 837,478 "/>
<text x="927" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="927,473 927,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,470 88,469 90,469 92,468 93,467 95,466 97,466 98,465 100,464 102,463 103,462 105,461 107,460 109,459 110,458 112,457 114,456 115,455 117,454 119,452 120,451 122,450 124,448 125,447 127,446 129,444 131,443 132,442 134,440 136,439 137,437 139,436 141,434 142,433 144,432 146,430 147,429 149,427 151,426 153,425 154,423 156,422 158,421 159,419 161,418 163,417 164,415 166,414 168,412 169,411 171,409 173,407 175,405 176,403 178,401 180,399 181,397 183,394 185,392 186,389 188,386 190,383 191,379 193,376 195,372 197,368 198,364 200,359 202,355 203,350 205,345 207,339 208,334 210,328 212,323 214,317 215,311 217,305 219,300 220,294 222,289 224,284 225,279 227,275 229,271 230,267 232,264 234,261 236,259 237,257 239,255 241,254 242,253 244,252 246,251 247,251 249,250 251,249 252,248 254,247 256,246 258,245 259,244 261,242 263,240 264,238 266,236 268,234 269,231 271,228 273,226 274,223 276,219 278,216 280,213 281,209 283,205 285,201 286,197 288,192 290,188 291,183 293,178 295,174 296,169 298,164 300,159 302,154 303,149 305,144 307,139 308,135 310,131 312,127 313,123 315,120 317,117 318,115 320,113 322,111 324,110 325,108 327,108 329,107 330,107 332,107 334,108 335,108 337,109 339,110 341,111 342,111 344,112 346,113 347,114 349,115 351,116 352,117 354,118 356,118 357,119 359,119 361,120 363,120 364,120 366,120 368,120 369,119 371,119 373,118 374,117 376,116 378,115 379,114 381,113 383,111 385,109 386,108 388,106 390,104 391,102 393,100 395,98 396,96 398,95 400,94 401,93 403,93 405,93 407,93 408,94 410,96 412,98 413,101 415,105 417,109 418,113 420,118 422,123 423,128 425,134 427,139 429,145 430,150 432,156 434,161 435,165 437,170 439,173 440,177 442,180 444,183 445,186 447,188 449,190 451,192 452,193 454,195 456,196 457,197 459,199 461,200 462,202 464,203 466,205 468,206 469,208 471,209 473,211 474,212 476,214 478,216 479,217 481,219 483,221 484,222 486,224 488,226 490,228 491,230 493,233 495,236 496,238 498,242 500,245 501,249 503,253 505,257 506,262 508,266 510,271 512,276 513,281 515,286 517,290 518,295 520,299 522,303 523,306 525,309 527,311 528,313 530,314 532,315 534,315 535,315 537,314 539,313 540,312 542,310 544,308 545,306 547,305 549,303 550,302 552,300 554,300 556,299 557,299 559,300 561,301 562,302 564,304 566,307 567,310 569,313 571,317 573,321 574,326 576,330 578,335 579,339 581,343 583,348 584,352 586,356 588,360 589,363 591,366 593,369 595,371 596,373 598,375 600,377 601,378 603,380 605,381 606,382 608,383 610,384 611,385 613,386 615,387 617,388 618,390 620,391 622,393 623,394 625,396 627,398 628,400 630,402 632,404 633,406 635,408 637,410 639,412 640,414 642,416 644,417 645,419 647,420 649,422 650,423 652,424 654,425 655,426 657,427 659,428 661,428 662,429 664,429 666,430 667,430 669,430 671,431 672,431 674,431 676,431 677,432 679,432 681,432 683,433 684,433 686,434 688,434 689,435 691,436 693,436 694,437 696,438 698,439 700,439 701,440 703,441 705,442 706,443 708,443 710,444 711,445 713,445 715,446 716,446 718,446 720,447 722,447 723,447 725,448 727,448 728,448 730,448 732,448 733,449 735,449 737,449 738,449 740,449 742,449 744,449 745,449 747,449 749,449 750,450 752,450 754,450 755,450 757,450 759,450 760,450 762,451 764,451 766,451 767,451 769,451 771,452 772,452 774,452 776,452 777,453 779,453 781,453 782,453 784,454 786,454 788,454 789,454 791,454 793,455 794,455 796,455 798,455 799,455 801,455 803,455 804,456 806,456 808,456 810,456 811,456 813,456 815,456 816,456 818,456 820,456 821,456 823,456 825,456 827,455 828,455 830,455 832,455 833,455 835,455 837,455 838,455 840,455 842,455 843,455 845,455 847,455 849,456 850,456 852,456 854,457 855,457 857,458 859,458 860,459 862,459 864,460 865,461 867,461 869,462 871,462 872,463 874,463 876,464 877,464 879,464 881,464 882,465 884,465 886,465 887,465 889,465 891,465 893,465 894,465 896,465 898,465 899,465 901,465 903,466 904,466 906,466 908,466 909,467 911,467 913,468 915,468 916,468 918,469 920,469 921,470 923,470 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,415 166,414 168,412 169,411 171,409 173,407 175,405 176,403 178,401 180,399 181,397 183,394 185,392 186,389 188,386 190,383 191,379 193,376 195,372 197,368 198,364 200,359 202,355 203,350 205,345 207,339 208,334 210,328 212,323 214,317 215,311 217,305 219,300 220,294 222,289 224,284 225,279 227,275 229,271 230,267 232,264 234,261 236,259 237,257 239,255 241,254 242,253 244,252 246,251 247,251 249,250 251,249 252,248 254,247 256,246 258,245 259,244 261,242 263,240 264,238 266,236 268,234 269,231 271,228 273,226 274,223 276,219 278,216 280,213 281,209 283,205 285,201 286,197 288,192 290,188 291,183 293,178 295,174 296,169 298,164 300,159 302,154 303,149 305,144 307,139 308,135 310,131 312,127 313,123 315,120 317,117 318,115 320,113 322,111 324,110 325,108 327,108 329,107 330,107 332,107 334,108 335,108 337,109 339,110 341,111 342,111 344,112 346,113 347,114 349,115 351,116 352,117 354,118 356,118 357,119 359,119 361,120 363,120 364,120 366,120 368,120 369,119 371,119 373,118 374,117 376,116 378,115 379,114 381,113 383,111 385,109 386,108 388,106 390,104 391,102 393,100 395,98 396,96 398,95 400,94 401,93 403,93 405,93 407,93 408,94 410,96 412,98 413,101 415,105 417,109 418,113 420,118 422,123 423,128 425,134 427,139 429,145 430,150 432,156 434,161 435,165 437,170 439,173 440,177 442,180 444,183 445,186 447,188 449,190 451,192 452,193 454,195 456,196 457,197 459,199 461,200 462,202 464,203 466,205 468,206 469,208 471,209 473,211 474,212 476,214 478,216 479,217 481,219 483,221 484,222 486,224 488,226 490,228 491,230 493,233 495,236 496,238 498,242 500,245 501,249 503,253 505,257 506,262 508,266 510,271 512,276 513,281 515,286 517,290 518,295 520,299 522,303 523,306 525,309 527,311 528,313 530,314 532,315 534,315 535,315 537,314 539,313 540,312 542,310 544,308 545,306 547,305 549,303 550,302 552,300 554,300 556,299 557,299 559,300 561,301 562,302 564,304 566,307 567,310 569,313 571,317 573,321 574,326 576,330 578,335 579,339 581,343 583,348 584,352 586,356 588,360 589,363 591,366 593,369 595,371 596,373 598,375 600,377 601,378 603,380 605,381 606,382 608,383 610,384 611,385 613,386 615,387 617,388 618,390 620,391 622,393 623,394 625,396 627,398 628,400 630,402 632,404 633,406 635,408 637,410 639,412 640,414 642,416 644,417 645,419 647,420 649,422 650,423 652,424 654,425 655,426 657,427 659,428 661,428 662,429 664,429 666,430 667,430 669,430 671,431 672,431 674,431 676,431 677,432 679,432 681,432 683,433 684,433 686,434 688,434 689,435 691,436 693,436 694,437 696,438 698,439 700,439 701,440 703,441 705,442 706,443 708,443 710,444 711,445 713,445 715,446 716,446 718,446 720,447 722,447 723,447 725,448 727,448 728,448 730,448 732,448 733,449 735,449 737,449 738,449 740,449 742,449 744,449 745,449 747,449 749,449 750,450 752,450 754,450 755,450 757,450 759,450 760,450 762,451 764,451 766,451 767,451 769,451 771,452 772,452 774,452 776,452 777,453 779,453 781,453 782,453 784,454 786,454 788,454 789,454 791,454 793,455 794,455 796,455 798,455 799,455 801,455 803,455 804,456 806,456 808,456 810,456 811,456 813,456 815,456 816,456 818,456 820,456 821,456 823,456 825,456 827,455 828,455 830,455 832,455 833,455 835,455 837,455 838,455 840,455 842,455 843,455 845,455 847,455 849,456 850,456 852,456 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="418,473 418,112 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

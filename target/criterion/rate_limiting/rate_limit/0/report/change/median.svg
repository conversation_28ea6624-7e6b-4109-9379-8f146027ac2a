<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/0:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="441" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,441 86,441 "/>
<text x="77" y="384" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,384 86,384 "/>
<text x="77" y="328" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
30
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,328 86,328 "/>
<text x="77" y="272" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,272 86,272 "/>
<text x="77" y="215" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,215 86,215 "/>
<text x="77" y="159" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,159 86,159 "/>
<text x="77" y="103" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
70
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,103 86,103 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="200" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="200,473 200,478 "/>
<text x="374" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.035
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="374,473 374,478 "/>
<text x="548" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="548,473 548,478 "/>
<text x="723" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.045
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="723,473 723,478 "/>
<text x="897" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="897,473 897,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,469 93,468 95,467 97,466 98,464 100,463 102,462 103,461 105,460 107,459 109,457 110,456 112,455 114,454 115,452 117,451 119,450 120,448 122,447 124,446 125,444 127,443 129,441 131,439 132,438 134,436 136,434 137,432 139,430 141,428 142,426 144,424 146,421 147,419 149,416 151,414 153,411 154,409 156,406 158,403 159,401 161,398 163,395 164,392 166,389 168,386 169,383 171,380 173,377 175,374 176,370 178,367 180,364 181,361 183,357 185,354 186,350 188,347 190,343 191,339 193,336 195,332 197,328 198,324 200,320 202,316 203,311 205,307 207,303 208,298 210,293 212,289 214,284 215,279 217,274 219,270 220,265 222,260 224,255 225,250 227,245 229,241 230,236 232,232 234,227 236,223 237,219 239,215 241,211 242,207 244,204 246,200 247,197 249,194 251,191 252,188 254,185 256,182 258,179 259,176 261,174 263,171 264,168 266,166 268,163 269,160 271,158 273,155 274,152 276,150 278,147 280,145 281,143 283,140 285,138 286,137 288,135 290,133 291,132 293,131 295,130 296,129 298,128 300,127 302,127 303,126 305,126 307,125 308,125 310,125 312,124 313,124 315,123 317,122 318,121 320,120 322,119 324,118 325,117 327,115 329,114 330,112 332,110 334,109 335,107 337,105 339,104 341,102 342,100 344,99 346,97 347,96 349,94 351,93 352,91 354,90 356,88 357,86 359,85 361,83 363,81 364,79 366,77 368,75 369,73 371,71 373,69 374,67 376,65 378,63 379,61 381,59 383,58 385,56 386,55 388,54 390,54 391,53 393,54 395,54 396,55 398,56 400,57 401,58 403,60 405,62 407,64 408,67 410,69 412,72 413,75 415,78 417,81 418,85 420,88 422,92 423,95 425,99 427,103 429,107 430,110 432,114 434,118 435,122 437,127 439,131 440,135 442,139 444,142 445,146 447,150 449,153 451,156 452,159 454,161 456,164 457,166 459,167 461,168 462,169 464,170 466,170 468,170 469,170 471,169 473,168 474,167 476,166 478,165 479,163 481,162 483,160 484,159 486,157 488,156 490,154 491,153 493,151 495,150 496,148 498,147 500,145 501,144 503,143 505,141 506,140 508,139 510,137 512,136 513,135 515,133 517,132 518,131 520,130 522,129 523,128 525,127 527,127 528,126 530,126 532,126 534,126 535,126 537,127 539,127 540,128 542,129 544,131 545,132 547,134 549,136 550,139 552,141 554,144 556,147 557,150 559,153 561,157 562,160 564,164 566,168 567,172 569,176 571,179 573,183 574,187 576,191 578,195 579,199 581,203 583,206 584,210 586,214 588,217 589,220 591,223 593,226 595,229 596,232 598,234 600,236 601,239 603,241 605,242 606,244 608,246 610,247 611,248 613,250 615,251 617,251 618,252 620,253 622,253 623,253 625,254 627,254 628,254 630,254 632,253 633,253 635,253 637,253 639,253 640,252 642,252 644,252 645,252 647,252 649,252 650,252 652,253 654,253 655,254 657,254 659,255 661,256 662,257 664,258 666,260 667,261 669,263 671,264 672,266 674,268 676,271 677,273 679,275 681,278 683,281 684,284 686,286 688,289 689,292 691,295 693,298 694,302 696,305 698,308 700,311 701,314 703,317 705,320 706,323 708,326 710,329 711,332 713,335 715,338 716,341 718,344 720,346 722,349 723,352 725,354 727,357 728,359 730,361 732,364 733,366 735,368 737,370 738,372 740,373 742,375 744,377 745,378 747,379 749,380 750,382 752,383 754,383 755,384 757,385 759,386 760,386 762,387 764,387 766,388 767,388 769,388 771,389 772,389 774,389 776,390 777,390 779,390 781,391 782,391 784,391 786,392 788,392 789,393 791,393 793,394 794,394 796,395 798,395 799,396 801,396 803,397 804,397 806,398 808,398 810,399 811,400 813,401 815,401 816,402 818,403 820,404 821,405 823,406 825,407 827,409 828,410 830,411 832,413 833,414 835,416 837,417 838,419 840,421 842,423 843,424 845,426 847,428 849,430 850,432 852,434 854,436 855,437 857,439 859,441 860,443 862,444 864,446 865,448 867,449 869,451 871,452 872,453 874,454 876,455 877,457 879,457 881,458 882,459 884,460 886,461 887,461 889,462 891,462 893,463 894,463 896,464 898,464 899,465 901,465 903,465 904,466 906,466 908,466 909,466 911,467 913,467 915,467 916,467 918,467 920,468 921,468 923,468 925,468 926,468 928,469 930,469 932,469 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,392 166,389 168,386 169,383 171,380 173,377 175,374 176,370 178,367 180,364 181,361 183,357 185,354 186,350 188,347 190,343 191,339 193,336 195,332 197,328 198,324 200,320 202,316 203,311 205,307 207,303 208,298 210,293 212,289 214,284 215,279 217,274 219,270 220,265 222,260 224,255 225,250 227,245 229,241 230,236 232,232 234,227 236,223 237,219 239,215 241,211 242,207 244,204 246,200 247,197 249,194 251,191 252,188 254,185 256,182 258,179 259,176 261,174 263,171 264,168 266,166 268,163 269,160 271,158 273,155 274,152 276,150 278,147 280,145 281,143 283,140 285,138 286,137 288,135 290,133 291,132 293,131 295,130 296,129 298,128 300,127 302,127 303,126 305,126 307,125 308,125 310,125 312,124 313,124 315,123 317,122 318,121 320,120 322,119 324,118 325,117 327,115 329,114 330,112 332,110 334,109 335,107 337,105 339,104 341,102 342,100 344,99 346,97 347,96 349,94 351,93 352,91 354,90 356,88 357,86 359,85 361,83 363,81 364,79 366,77 368,75 369,73 371,71 373,69 374,67 376,65 378,63 379,61 381,59 383,58 385,56 386,55 388,54 390,54 391,53 393,54 395,54 396,55 398,56 400,57 401,58 403,60 405,62 407,64 408,67 410,69 412,72 413,75 415,78 417,81 418,85 420,88 422,92 423,95 425,99 427,103 429,107 430,110 432,114 434,118 435,122 437,127 439,131 440,135 442,139 444,142 445,146 447,150 449,153 451,156 452,159 454,161 456,164 457,166 459,167 461,168 462,169 464,170 466,170 468,170 469,170 471,169 473,168 474,167 476,166 478,165 479,163 481,162 483,160 484,159 486,157 488,156 490,154 491,153 493,151 495,150 496,148 498,147 500,145 501,144 503,143 505,141 506,140 508,139 510,137 512,136 513,135 515,133 517,132 518,131 520,130 522,129 523,128 525,127 527,127 528,126 530,126 532,126 534,126 535,126 537,127 539,127 540,128 542,129 544,131 545,132 547,134 549,136 550,139 552,141 554,144 556,147 557,150 559,153 561,157 562,160 564,164 566,168 567,172 569,176 571,179 573,183 574,187 576,191 578,195 579,199 581,203 583,206 584,210 586,214 588,217 589,220 591,223 593,226 595,229 596,232 598,234 600,236 601,239 603,241 605,242 606,244 608,246 610,247 611,248 613,250 615,251 617,251 618,252 620,253 622,253 623,253 625,254 627,254 628,254 630,254 632,253 633,253 635,253 637,253 639,253 640,252 642,252 644,252 645,252 647,252 649,252 650,252 652,253 654,253 655,254 657,254 659,255 661,256 662,257 664,258 666,260 667,261 669,263 671,264 672,266 674,268 676,271 677,273 679,275 681,278 683,281 684,284 686,286 688,289 689,292 691,295 693,298 694,302 696,305 698,308 700,311 701,314 703,317 705,320 706,323 708,326 710,329 711,332 713,335 715,338 716,341 718,344 720,346 722,349 723,352 725,354 727,357 728,359 730,361 732,364 733,366 735,368 737,370 738,372 740,373 742,375 744,377 745,378 747,379 749,380 750,382 752,383 754,383 755,384 757,385 759,386 760,386 762,387 764,387 766,388 767,388 769,388 771,389 772,389 774,389 776,390 777,390 779,390 781,391 782,391 784,391 786,392 788,392 789,393 791,393 793,394 794,394 796,395 798,395 799,396 801,396 803,397 804,397 806,398 808,398 810,399 811,400 813,401 815,401 816,402 818,403 820,404 821,405 823,406 825,407 827,409 828,410 830,411 832,413 833,414 835,416 837,417 838,419 840,421 842,423 843,424 845,426 847,428 849,430 850,432 852,434 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="392,473 392,54 "/>
<rect x="509" y="53" width="0" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

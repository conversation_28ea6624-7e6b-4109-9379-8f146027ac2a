<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/0:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="418" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,418 86,418 "/>
<text x="77" y="339" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,339 86,339 "/>
<text x="77" y="260" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,260 86,260 "/>
<text x="77" y="181" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,181 86,181 "/>
<text x="77" y="102" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,102 86,102 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="156" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="156,473 156,478 "/>
<text x="282" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="282,473 282,478 "/>
<text x="409" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.46945e-18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="409,473 409,478 "/>
<text x="535" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="535,473 535,478 "/>
<text x="662" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="662,473 662,478 "/>
<text x="788" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="788,473 788,478 "/>
<text x="914" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="914,473 914,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,471 88,470 90,470 92,469 93,469 95,468 97,467 98,467 100,466 102,466 103,465 105,464 107,464 109,463 110,463 112,462 114,461 115,461 117,460 119,460 120,459 122,459 124,458 125,458 127,457 129,457 131,456 132,456 134,455 136,454 137,454 139,453 141,453 142,452 144,451 146,450 147,450 149,449 151,448 153,447 154,447 156,446 158,445 159,444 161,443 163,442 164,441 166,440 168,439 169,438 171,437 173,436 175,435 176,434 178,433 180,432 181,431 183,430 185,429 186,428 188,427 190,426 191,425 193,424 195,423 197,422 198,420 200,419 202,418 203,417 205,416 207,415 208,413 210,412 212,411 214,410 215,408 217,407 219,406 220,404 222,403 224,402 225,400 227,399 229,397 230,396 232,395 234,393 236,392 237,390 239,389 241,387 242,386 244,384 246,382 247,381 249,379 251,378 252,376 254,375 256,373 258,371 259,370 261,368 263,367 264,365 266,363 268,362 269,360 271,359 273,357 274,356 276,354 278,352 280,351 281,349 283,348 285,346 286,345 288,343 290,341 291,340 293,338 295,337 296,335 298,333 300,332 302,330 303,328 305,327 307,325 308,323 310,322 312,320 313,318 315,316 317,314 318,312 320,310 322,308 324,306 325,304 327,302 329,300 330,298 332,296 334,294 335,292 337,289 339,287 341,285 342,283 344,280 346,278 347,276 349,273 351,271 352,269 354,266 356,264 357,262 359,259 361,257 363,255 364,252 366,250 368,247 369,245 371,243 373,240 374,238 376,236 378,233 379,231 381,229 383,227 385,224 386,222 388,220 390,218 391,215 393,213 395,211 396,209 398,207 400,205 401,203 403,201 405,199 407,196 408,194 410,192 412,190 413,188 415,186 417,184 418,182 420,179 422,177 423,175 425,173 427,170 429,168 430,166 432,163 434,161 435,159 437,156 439,154 440,151 442,149 444,147 445,144 447,142 449,140 451,137 452,135 454,133 456,131 457,129 459,126 461,124 462,122 464,120 466,118 468,116 469,114 471,112 473,110 474,108 476,106 478,105 479,103 481,101 483,99 484,98 486,96 488,95 490,94 491,92 493,91 495,90 496,89 498,88 500,87 501,86 503,85 505,84 506,83 508,82 510,81 512,81 513,80 515,79 517,79 518,78 520,77 522,76 523,76 525,75 527,74 528,74 530,73 532,72 534,71 535,71 537,70 539,69 540,68 542,68 544,67 545,66 547,65 549,65 550,64 552,63 554,63 556,62 557,61 559,60 561,60 562,59 564,58 566,58 567,57 569,57 571,56 573,55 574,55 576,55 578,54 579,54 581,54 583,54 584,53 586,54 588,54 589,54 591,54 593,55 595,55 596,56 598,56 600,57 601,58 603,59 605,59 606,60 608,61 610,62 611,63 613,64 615,65 617,65 618,66 620,67 622,68 623,69 625,70 627,71 628,72 630,73 632,74 633,75 635,76 637,77 639,78 640,79 642,81 644,82 645,83 647,85 649,87 650,88 652,90 654,92 655,94 657,96 659,98 661,100 662,102 664,104 666,106 667,109 669,111 671,113 672,115 674,118 676,120 677,122 679,125 681,127 683,130 684,132 686,134 688,137 689,140 691,142 693,145 694,147 696,150 698,152 700,155 701,158 703,160 705,163 706,165 708,168 710,171 711,173 713,176 715,178 716,181 718,183 720,186 722,189 723,191 725,194 727,197 728,199 730,202 732,205 733,207 735,210 737,213 738,216 740,219 742,222 744,224 745,227 747,230 749,233 750,236 752,239 754,242 755,245 757,248 759,251 760,254 762,257 764,260 766,263 767,266 769,269 771,272 772,275 774,278 776,281 777,284 779,287 781,290 782,293 784,296 786,299 788,302 789,305 791,307 793,310 794,313 796,316 798,319 799,322 801,325 803,327 804,330 806,333 808,336 810,338 811,341 813,344 815,346 816,349 818,351 820,354 821,356 823,359 825,362 827,364 828,367 830,369 832,372 833,374 835,376 837,379 838,381 840,384 842,386 843,388 845,391 847,393 849,395 850,397 852,400 854,402 855,404 857,406 859,408 860,410 862,412 864,414 865,415 867,417 869,419 871,421 872,423 874,424 876,426 877,428 879,430 881,431 882,433 884,435 886,436 887,438 889,440 891,441 893,443 894,444 896,446 898,447 899,449 901,450 903,452 904,453 906,455 908,456 909,458 911,459 913,460 915,462 916,463 918,464 920,465 921,466 923,467 925,469 926,470 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,441 166,440 168,439 169,438 171,437 173,436 175,435 176,434 178,433 180,432 181,431 183,430 185,429 186,428 188,427 190,426 191,425 193,424 195,423 197,422 198,420 200,419 202,418 203,417 205,416 207,415 208,413 210,412 212,411 214,410 215,408 217,407 219,406 220,404 222,403 224,402 225,400 227,399 229,397 230,396 232,395 234,393 236,392 237,390 239,389 241,387 242,386 244,384 246,382 247,381 249,379 251,378 252,376 254,375 256,373 258,371 259,370 261,368 263,367 264,365 266,363 268,362 269,360 271,359 273,357 274,356 276,354 278,352 280,351 281,349 283,348 285,346 286,345 288,343 290,341 291,340 293,338 295,337 296,335 298,333 300,332 302,330 303,328 305,327 307,325 308,323 310,322 312,320 313,318 315,316 317,314 318,312 320,310 322,308 324,306 325,304 327,302 329,300 330,298 332,296 334,294 335,292 337,289 339,287 341,285 342,283 344,280 346,278 347,276 349,273 351,271 352,269 354,266 356,264 357,262 359,259 361,257 363,255 364,252 366,250 368,247 369,245 371,243 373,240 374,238 376,236 378,233 379,231 381,229 383,227 385,224 386,222 388,220 390,218 391,215 393,213 395,211 396,209 398,207 400,205 401,203 403,201 405,199 407,196 408,194 410,192 412,190 413,188 415,186 417,184 418,182 420,179 422,177 423,175 425,173 427,170 429,168 430,166 432,163 434,161 435,159 437,156 439,154 440,151 442,149 444,147 445,144 447,142 449,140 451,137 452,135 454,133 456,131 457,129 459,126 461,124 462,122 464,120 466,118 468,116 469,114 471,112 473,110 474,108 476,106 478,105 479,103 481,101 483,99 484,98 486,96 488,95 490,94 491,92 493,91 495,90 496,89 498,88 500,87 501,86 503,85 505,84 506,83 508,82 510,81 512,81 513,80 515,79 517,79 518,78 520,77 522,76 523,76 525,75 527,74 528,74 530,73 532,72 534,71 535,71 537,70 539,69 540,68 542,68 544,67 545,66 547,65 549,65 550,64 552,63 554,63 556,62 557,61 559,60 561,60 562,59 564,58 566,58 567,57 569,57 571,56 573,55 574,55 576,55 578,54 579,54 581,54 583,54 584,53 586,54 588,54 589,54 591,54 593,55 595,55 596,56 598,56 600,57 601,58 603,59 605,59 606,60 608,61 610,62 611,63 613,64 615,65 617,65 618,66 620,67 622,68 623,69 625,70 627,71 628,72 630,73 632,74 633,75 635,76 637,77 639,78 640,79 642,81 644,82 645,83 647,85 649,87 650,88 652,90 654,92 655,94 657,96 659,98 661,100 662,102 664,104 666,106 667,109 669,111 671,113 672,115 674,118 676,120 677,122 679,125 681,127 683,130 684,132 686,134 688,137 689,140 691,142 693,145 694,147 696,150 698,152 700,155 701,158 703,160 705,163 706,165 708,168 710,171 711,173 713,176 715,178 716,181 718,183 720,186 722,189 723,191 725,194 727,197 728,199 730,202 732,205 733,207 735,210 737,213 738,216 740,219 742,222 744,224 745,227 747,230 749,233 750,236 752,239 754,242 755,245 757,248 759,251 760,254 762,257 764,260 766,263 767,266 769,269 771,272 772,275 774,278 776,281 777,284 779,287 781,290 782,293 784,296 786,299 788,302 789,305 791,307 793,310 794,313 796,316 798,319 799,322 801,325 803,327 804,330 806,333 808,336 810,338 811,341 813,344 815,346 816,349 818,351 820,354 821,356 823,359 825,362 827,364 828,367 830,369 832,372 833,374 835,376 837,379 838,381 840,384 842,386 843,388 845,391 847,393 849,395 850,397 852,400 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="535,473 535,71 "/>
<rect x="282" y="53" width="253" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

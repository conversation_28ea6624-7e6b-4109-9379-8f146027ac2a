<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/1000:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="452" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,452 86,452 "/>
<text x="77" y="410" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,410 86,410 "/>
<text x="77" y="367" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,367 86,367 "/>
<text x="77" y="324" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,324 86,324 "/>
<text x="77" y="282" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,282 86,282 "/>
<text x="77" y="239" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,239 86,239 "/>
<text x="77" y="197" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,197 86,197 "/>
<text x="77" y="154" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,154 86,154 "/>
<text x="77" y="111" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,111 86,111 "/>
<text x="77" y="69" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,69 86,69 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="184" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="184,473 184,478 "/>
<text x="285" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="285,473 285,478 "/>
<text x="385" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="385,473 385,478 "/>
<text x="486" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="486,473 486,478 "/>
<text x="587" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="587,473 587,478 "/>
<text x="688" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="688,473 688,478 "/>
<text x="788" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="788,473 788,478 "/>
<text x="889" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="889,473 889,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,468 88,467 90,466 92,465 93,465 95,464 97,463 98,462 100,462 102,461 103,460 105,460 107,459 109,458 110,458 112,457 114,456 115,456 117,455 119,454 120,454 122,453 124,453 125,452 127,452 129,451 131,451 132,450 134,449 136,449 137,448 139,447 141,447 142,446 144,445 146,444 147,443 149,441 151,440 153,438 154,437 156,435 158,433 159,431 161,429 163,426 164,424 166,421 168,419 169,416 171,413 173,410 175,407 176,404 178,401 180,398 181,395 183,393 185,390 186,387 188,384 190,382 191,379 193,377 195,374 197,372 198,370 200,368 202,366 203,364 205,362 207,360 208,358 210,356 212,354 214,352 215,350 217,348 219,346 220,344 222,342 224,340 225,338 227,336 229,333 230,331 232,328 234,325 236,322 237,319 239,316 241,313 242,309 244,305 246,301 247,297 249,292 251,287 252,282 254,277 256,272 258,267 259,261 261,256 263,251 264,245 266,240 268,235 269,230 271,226 273,222 274,218 276,214 278,211 280,208 281,205 283,203 285,202 286,200 288,199 290,198 291,198 293,197 295,197 296,197 298,197 300,198 302,198 303,198 305,198 307,199 308,199 310,199 312,199 313,199 315,199 317,199 318,199 320,199 322,198 324,198 325,197 327,197 329,196 330,195 332,194 334,193 335,191 337,190 339,188 341,186 342,184 344,182 346,180 347,177 349,174 351,171 352,168 354,165 356,162 357,159 359,156 361,153 363,150 364,147 366,144 368,142 369,140 371,137 373,136 374,134 376,133 378,131 379,131 381,130 383,129 385,129 386,129 388,129 390,129 391,129 393,130 395,130 396,131 398,131 400,132 401,132 403,133 405,134 407,135 408,135 410,136 412,136 413,137 415,137 417,137 418,137 420,137 422,137 423,137 425,136 427,135 429,134 430,133 432,132 434,130 435,129 437,127 439,126 440,124 442,123 444,121 445,120 447,119 449,118 451,117 452,116 454,116 456,116 457,116 459,116 461,117 462,118 464,119 466,120 468,122 469,124 471,126 473,128 474,130 476,133 478,135 479,138 481,140 483,143 484,145 486,147 488,149 490,151 491,153 493,155 495,156 496,158 498,159 500,160 501,161 503,161 505,162 506,162 508,163 510,163 512,163 513,163 515,162 517,162 518,161 520,160 522,159 523,158 525,156 527,154 528,152 530,150 532,147 534,144 535,141 537,138 539,134 540,131 542,127 544,123 545,119 547,116 549,112 550,108 552,105 554,102 556,100 557,98 559,96 561,95 562,94 564,94 566,94 567,95 569,97 571,99 573,102 574,105 576,109 578,113 579,118 581,123 583,129 584,135 586,141 588,148 589,155 591,161 593,168 595,175 596,182 598,188 600,195 601,201 603,207 605,212 606,218 608,223 610,227 611,232 613,236 615,239 617,243 618,246 620,249 622,251 623,254 625,256 627,258 628,260 630,263 632,265 633,267 635,269 637,271 639,273 640,276 642,278 644,280 645,283 647,286 649,288 650,291 652,294 654,297 655,299 657,302 659,305 661,308 662,311 664,314 666,317 667,320 669,322 671,325 672,328 674,331 676,333 677,336 679,338 681,340 683,343 684,345 686,347 688,348 689,350 691,352 693,353 694,355 696,356 698,357 700,358 701,359 703,360 705,361 706,362 708,363 710,364 711,365 713,366 715,367 716,368 718,370 720,371 722,373 723,375 725,376 727,378 728,380 730,382 732,384 733,386 735,388 737,390 738,392 740,394 742,396 744,398 745,399 747,401 749,403 750,404 752,406 754,407 755,409 757,410 759,411 760,413 762,414 764,415 766,416 767,417 769,418 771,419 772,420 774,421 776,422 777,423 779,424 781,424 782,425 784,426 786,426 788,427 789,427 791,428 793,428 794,429 796,429 798,430 799,430 801,431 803,431 804,432 806,433 808,433 810,434 811,435 813,436 815,436 816,437 818,438 820,439 821,440 823,442 825,443 827,444 828,445 830,447 832,448 833,449 835,450 837,452 838,453 840,454 842,455 843,456 845,457 847,458 849,459 850,460 852,461 854,461 855,462 857,462 859,463 860,463 862,464 864,464 865,464 867,464 869,465 871,465 872,465 874,465 876,465 877,465 879,465 881,466 882,466 884,466 886,466 887,466 889,466 891,466 893,466 894,466 896,467 898,467 899,467 901,467 903,467 904,467 906,468 908,468 909,468 911,468 913,469 915,469 916,469 918,470 920,470 921,471 923,471 925,471 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,424 166,421 168,419 169,416 171,413 173,410 175,407 176,404 178,401 180,398 181,395 183,393 185,390 186,387 188,384 190,382 191,379 193,377 195,374 197,372 198,370 200,368 202,366 203,364 205,362 207,360 208,358 210,356 212,354 214,352 215,350 217,348 219,346 220,344 222,342 224,340 225,338 227,336 229,333 230,331 232,328 234,325 236,322 237,319 239,316 241,313 242,309 244,305 246,301 247,297 249,292 251,287 252,282 254,277 256,272 258,267 259,261 261,256 263,251 264,245 266,240 268,235 269,230 271,226 273,222 274,218 276,214 278,211 280,208 281,205 283,203 285,202 286,200 288,199 290,198 291,198 293,197 295,197 296,197 298,197 300,198 302,198 303,198 305,198 307,199 308,199 310,199 312,199 313,199 315,199 317,199 318,199 320,199 322,198 324,198 325,197 327,197 329,196 330,195 332,194 334,193 335,191 337,190 339,188 341,186 342,184 344,182 346,180 347,177 349,174 351,171 352,168 354,165 356,162 357,159 359,156 361,153 363,150 364,147 366,144 368,142 369,140 371,137 373,136 374,134 376,133 378,131 379,131 381,130 383,129 385,129 386,129 388,129 390,129 391,129 393,130 395,130 396,131 398,131 400,132 401,132 403,133 405,134 407,135 408,135 410,136 412,136 413,137 415,137 417,137 418,137 420,137 422,137 423,137 425,136 427,135 429,134 430,133 432,132 434,130 435,129 437,127 439,126 440,124 442,123 444,121 445,120 447,119 449,118 451,117 452,116 454,116 456,116 457,116 459,116 461,117 462,118 464,119 466,120 468,122 469,124 471,126 473,128 474,130 476,133 478,135 479,138 481,140 483,143 484,145 486,147 488,149 490,151 491,153 493,155 495,156 496,158 498,159 500,160 501,161 503,161 505,162 506,162 508,163 510,163 512,163 513,163 515,162 517,162 518,161 520,160 522,159 523,158 525,156 527,154 528,152 530,150 532,147 534,144 535,141 537,138 539,134 540,131 542,127 544,123 545,119 547,116 549,112 550,108 552,105 554,102 556,100 557,98 559,96 561,95 562,94 564,94 566,94 567,95 569,97 571,99 573,102 574,105 576,109 578,113 579,118 581,123 583,129 584,135 586,141 588,148 589,155 591,161 593,168 595,175 596,182 598,188 600,195 601,201 603,207 605,212 606,218 608,223 610,227 611,232 613,236 615,239 617,243 618,246 620,249 622,251 623,254 625,256 627,258 628,260 630,263 632,265 633,267 635,269 637,271 639,273 640,276 642,278 644,280 645,283 647,286 649,288 650,291 652,294 654,297 655,299 657,302 659,305 661,308 662,311 664,314 666,317 667,320 669,322 671,325 672,328 674,331 676,333 677,336 679,338 681,340 683,343 684,345 686,347 688,348 689,350 691,352 693,353 694,355 696,356 698,357 700,358 701,359 703,360 705,361 706,362 708,363 710,364 711,365 713,366 715,367 716,368 718,370 720,371 722,373 723,375 725,376 727,378 728,380 730,382 732,384 733,386 735,388 737,390 738,392 740,394 742,396 744,398 745,399 747,401 749,403 750,404 752,406 754,407 755,409 757,410 759,411 760,413 762,414 764,415 766,416 767,417 769,418 771,419 772,420 774,421 776,422 777,423 779,424 781,424 782,425 784,426 786,426 788,427 789,427 791,428 793,428 794,429 796,429 798,430 799,430 801,431 803,431 804,432 806,433 808,433 810,434 811,435 813,436 815,436 816,437 818,438 820,439 821,440 823,442 825,443 827,444 828,445 830,447 832,448 833,449 835,450 837,452 838,453 840,454 842,455 843,456 845,457 847,458 849,459 850,460 852,461 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="459,473 459,116 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/1000:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="442" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,442 86,442 "/>
<text x="77" y="405" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,405 86,405 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="331" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,331 86,331 "/>
<text x="77" y="294" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,294 86,294 "/>
<text x="77" y="257" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,257 86,257 "/>
<text x="77" y="220" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,220 86,220 "/>
<text x="77" y="183" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,183 86,183 "/>
<text x="77" y="146" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,146 86,146 "/>
<text x="77" y="109" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,109 86,109 "/>
<text x="77" y="72" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,72 86,72 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="101" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
187.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="101,473 101,478 "/>
<text x="230" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="230,473 230,478 "/>
<text x="359" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
188.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="359,473 359,478 "/>
<text x="489" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="489,473 489,478 "/>
<text x="618" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
189.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="618,473 618,478 "/>
<text x="747" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="747,473 747,478 "/>
<text x="876" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
190.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="876,473 876,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,471 97,471 98,471 100,470 102,470 103,470 105,469 107,469 109,468 110,468 112,467 114,467 115,466 117,465 119,465 120,464 122,463 124,462 125,461 127,459 129,458 131,457 132,455 134,454 136,452 137,451 139,449 141,448 142,446 144,445 146,443 147,442 149,441 151,440 153,439 154,439 156,438 158,438 159,438 161,438 163,439 164,439 166,440 168,441 169,442 171,443 173,444 175,445 176,446 178,447 180,448 181,449 183,450 185,451 186,451 188,452 190,452 191,452 193,453 195,453 197,453 198,453 200,453 202,453 203,453 205,453 207,452 208,452 210,452 212,452 214,451 215,451 217,451 219,450 220,450 222,450 224,449 225,449 227,448 229,448 230,447 232,447 234,446 236,446 237,445 239,445 241,444 242,444 244,443 246,443 247,442 249,442 251,441 252,440 254,439 256,438 258,437 259,435 261,434 263,432 264,430 266,427 268,425 269,422 271,418 273,414 274,410 276,405 278,400 280,395 281,388 283,382 285,375 286,367 288,359 290,351 291,342 293,333 295,324 296,315 298,307 300,298 302,290 303,282 305,275 307,268 308,262 310,257 312,253 313,250 315,247 317,245 318,244 320,243 322,244 324,245 325,246 327,248 329,250 330,252 332,254 334,256 335,259 337,261 339,263 341,264 342,266 344,267 346,267 347,267 349,267 351,266 352,265 354,263 356,260 357,257 359,253 361,249 363,244 364,239 366,233 368,226 369,219 371,211 373,202 374,194 376,184 378,175 379,165 381,155 383,146 385,136 386,127 388,119 390,111 391,105 393,99 395,95 396,93 398,92 400,93 401,96 403,101 405,107 407,115 408,125 410,136 412,148 413,162 415,176 417,190 418,205 420,220 422,234 423,248 425,261 427,273 429,284 430,293 432,302 434,310 435,316 437,321 439,325 440,329 442,331 444,333 445,335 447,336 449,337 451,338 452,339 454,340 456,341 457,342 459,343 461,344 462,346 464,347 466,349 468,350 469,351 471,352 473,353 474,354 476,355 478,355 479,355 481,355 483,355 484,354 486,353 488,352 490,351 491,350 493,348 495,347 496,345 498,344 500,342 501,341 503,340 505,339 506,338 508,337 510,336 512,336 513,336 515,335 517,336 518,336 520,336 522,337 523,338 525,339 527,340 528,341 530,343 532,344 534,346 535,348 537,349 539,351 540,352 542,354 544,356 545,357 547,358 549,359 550,360 552,361 554,362 556,362 557,362 559,362 561,362 562,362 564,361 566,360 567,360 569,359 571,359 573,358 574,358 576,357 578,357 579,357 581,357 583,357 584,357 586,358 588,358 589,358 591,358 593,358 595,357 596,357 598,355 600,354 601,352 603,349 605,346 606,343 608,339 610,335 611,331 613,326 615,321 617,316 618,311 620,306 622,301 623,297 625,292 627,288 628,284 630,280 632,277 633,274 635,271 637,268 639,266 640,265 642,263 644,262 645,261 647,260 649,259 650,259 652,259 654,259 655,260 657,261 659,262 661,264 662,266 664,269 666,272 667,276 669,280 671,285 672,290 674,296 676,302 677,308 679,314 681,321 683,328 684,335 686,341 688,348 689,354 691,360 693,365 694,370 696,375 698,379 700,382 701,385 703,388 705,390 706,392 708,393 710,395 711,396 713,397 715,399 716,400 718,401 720,402 722,403 723,404 725,404 727,405 728,406 730,406 732,406 733,406 735,406 737,405 738,404 740,403 742,401 744,400 745,398 747,396 749,394 750,392 752,390 754,389 755,387 757,386 759,385 760,385 762,385 764,385 766,386 767,387 769,388 771,390 772,392 774,394 776,396 777,399 779,402 781,404 782,407 784,410 786,412 788,415 789,417 791,419 793,422 794,424 796,425 798,427 799,429 801,430 803,432 804,433 806,434 808,435 810,436 811,437 813,438 815,439 816,440 818,441 820,441 821,442 823,442 825,443 827,443 828,443 830,444 832,444 833,444 835,444 837,444 838,444 840,444 842,444 843,444 845,444 847,445 849,445 850,445 852,445 854,446 855,446 857,447 859,448 860,448 862,449 864,450 865,451 867,453 869,454 871,455 872,456 874,457 876,459 877,460 879,461 881,462 882,463 884,463 886,464 887,465 889,465 891,466 893,466 894,467 896,467 898,467 899,467 901,468 903,468 904,468 906,468 908,469 909,469 911,469 913,469 915,469 916,470 918,470 920,470 921,470 923,470 925,470 926,470 928,470 930,470 932,471 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,439 166,440 168,441 169,442 171,443 173,444 175,445 176,446 178,447 180,448 181,449 183,450 185,451 186,451 188,452 190,452 191,452 193,453 195,453 197,453 198,453 200,453 202,453 203,453 205,453 207,452 208,452 210,452 212,452 214,451 215,451 217,451 219,450 220,450 222,450 224,449 225,449 227,448 229,448 230,447 232,447 234,446 236,446 237,445 239,445 241,444 242,444 244,443 246,443 247,442 249,442 251,441 252,440 254,439 256,438 258,437 259,435 261,434 263,432 264,430 266,427 268,425 269,422 271,418 273,414 274,410 276,405 278,400 280,395 281,388 283,382 285,375 286,367 288,359 290,351 291,342 293,333 295,324 296,315 298,307 300,298 302,290 303,282 305,275 307,268 308,262 310,257 312,253 313,250 315,247 317,245 318,244 320,243 322,244 324,245 325,246 327,248 329,250 330,252 332,254 334,256 335,259 337,261 339,263 341,264 342,266 344,267 346,267 347,267 349,267 351,266 352,265 354,263 356,260 357,257 359,253 361,249 363,244 364,239 366,233 368,226 369,219 371,211 373,202 374,194 376,184 378,175 379,165 381,155 383,146 385,136 386,127 388,119 390,111 391,105 393,99 395,95 396,93 398,92 400,93 401,96 403,101 405,107 407,115 408,125 410,136 412,148 413,162 415,176 417,190 418,205 420,220 422,234 423,248 425,261 427,273 429,284 430,293 432,302 434,310 435,316 437,321 439,325 440,329 442,331 444,333 445,335 447,336 449,337 451,338 452,339 454,340 456,341 457,342 459,343 461,344 462,346 464,347 466,349 468,350 469,351 471,352 473,353 474,354 476,355 478,355 479,355 481,355 483,355 484,354 486,353 488,352 490,351 491,350 493,348 495,347 496,345 498,344 500,342 501,341 503,340 505,339 506,338 508,337 510,336 512,336 513,336 515,335 517,336 518,336 520,336 522,337 523,338 525,339 527,340 528,341 530,343 532,344 534,346 535,348 537,349 539,351 540,352 542,354 544,356 545,357 547,358 549,359 550,360 552,361 554,362 556,362 557,362 559,362 561,362 562,362 564,361 566,360 567,360 569,359 571,359 573,358 574,358 576,357 578,357 579,357 581,357 583,357 584,357 586,358 588,358 589,358 591,358 593,358 595,357 596,357 598,355 600,354 601,352 603,349 605,346 606,343 608,339 610,335 611,331 613,326 615,321 617,316 618,311 620,306 622,301 623,297 625,292 627,288 628,284 630,280 632,277 633,274 635,271 637,268 639,266 640,265 642,263 644,262 645,261 647,260 649,259 650,259 652,259 654,259 655,260 657,261 659,262 661,264 662,266 664,269 666,272 667,276 669,280 671,285 672,290 674,296 676,302 677,308 679,314 681,321 683,328 684,335 686,341 688,348 689,354 691,360 693,365 694,370 696,375 698,379 700,382 701,385 703,388 705,390 706,392 708,393 710,395 711,396 713,397 715,399 716,400 718,401 720,402 722,403 723,404 725,404 727,405 728,406 730,406 732,406 733,406 735,406 737,405 738,404 740,403 742,401 744,400 745,398 747,396 749,394 750,392 752,390 754,389 755,387 757,386 759,385 760,385 762,385 764,385 766,386 767,387 769,388 771,390 772,392 774,394 776,396 777,399 779,402 781,404 782,407 784,410 786,412 788,415 789,417 791,419 793,422 794,424 796,425 798,427 799,429 801,430 803,432 804,433 806,434 808,435 810,436 811,437 813,438 815,439 816,440 818,441 820,441 821,442 823,442 825,443 827,443 828,443 830,444 832,444 833,444 835,444 837,444 838,444 840,444 842,444 843,444 845,444 847,445 849,445 850,445 852,445 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="451,473 451,338 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
rate_limiting/rate_limit/1000:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="405" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,405 86,405 "/>
<text x="77" y="313" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,313 86,313 "/>
<text x="77" y="222" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,222 86,222 "/>
<text x="77" y="130" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,130 86,130 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="124" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="124,473 124,478 "/>
<text x="222" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="222,473 222,478 "/>
<text x="319" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="319,473 319,478 "/>
<text x="417" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="417,473 417,478 "/>
<text x="515" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
11
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="515,473 515,478 "/>
<text x="613" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="613,473 613,478 "/>
<text x="711" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
13
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="711,473 711,478 "/>
<text x="808" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="808,473 808,478 "/>
<text x="906" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="906,473 906,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,469 97,469 98,468 100,467 102,467 103,466 105,465 107,465 109,464 110,463 112,463 114,462 115,461 117,461 119,460 120,459 122,458 124,458 125,457 127,456 129,456 131,455 132,454 134,453 136,453 137,452 139,451 141,450 142,449 144,448 146,448 147,447 149,446 151,445 153,444 154,443 156,442 158,441 159,440 161,439 163,438 164,437 166,436 168,435 169,434 171,433 173,432 175,431 176,430 178,429 180,428 181,427 183,426 185,424 186,423 188,422 190,421 191,420 193,418 195,417 197,416 198,414 200,413 202,411 203,410 205,409 207,407 208,406 210,404 212,403 214,401 215,400 217,398 219,397 220,395 222,394 224,392 225,391 227,389 229,388 230,387 232,385 234,384 236,382 237,381 239,380 241,379 242,377 244,376 246,374 247,373 249,372 251,370 252,369 254,367 256,366 258,364 259,362 261,361 263,359 264,357 266,355 268,353 269,351 271,349 273,347 274,345 276,343 278,341 280,339 281,337 283,335 285,333 286,330 288,328 290,326 291,324 293,322 295,319 296,317 298,315 300,313 302,311 303,308 305,306 307,304 308,302 310,299 312,297 313,295 315,292 317,290 318,288 320,285 322,283 324,281 325,279 327,276 329,274 330,272 332,270 334,267 335,265 337,263 339,261 341,259 342,257 344,255 346,253 347,251 349,249 351,247 352,245 354,243 356,241 357,240 359,238 361,236 363,234 364,232 366,230 368,228 369,226 371,224 373,223 374,221 376,218 378,216 379,214 381,212 383,210 385,208 386,206 388,204 390,201 391,199 393,197 395,195 396,192 398,190 400,188 401,186 403,184 405,181 407,179 408,177 410,175 412,173 413,171 415,169 417,167 418,165 420,163 422,162 423,160 425,158 427,156 429,155 430,153 432,151 434,150 435,148 437,146 439,145 440,143 442,142 444,140 445,138 447,137 449,135 451,134 452,132 454,131 456,129 457,128 459,126 461,125 462,123 464,122 466,121 468,120 469,118 471,117 473,116 474,115 476,114 478,113 479,112 481,111 483,111 484,110 486,109 488,108 490,107 491,106 493,106 495,105 496,104 498,103 500,102 501,102 503,101 505,100 506,100 508,99 510,98 512,98 513,97 515,96 517,96 518,96 520,95 522,95 523,94 525,94 527,94 528,94 530,94 532,94 534,94 535,94 537,94 539,94 540,95 542,95 544,95 545,96 547,96 549,97 550,98 552,98 554,99 556,100 557,101 559,102 561,103 562,104 564,105 566,106 567,107 569,108 571,109 573,111 574,112 576,113 578,114 579,115 581,116 583,117 584,118 586,119 588,120 589,121 591,121 593,122 595,123 596,124 598,125 600,126 601,127 603,128 605,129 606,130 608,131 610,133 611,134 613,135 615,137 617,138 618,140 620,141 622,143 623,145 625,147 627,148 628,150 630,152 632,154 633,156 635,158 637,160 639,162 640,164 642,166 644,168 645,170 647,172 649,175 650,177 652,179 654,181 655,183 657,186 659,188 661,190 662,192 664,195 666,197 667,199 669,202 671,204 672,207 674,209 676,211 677,214 679,216 681,218 683,221 684,223 686,225 688,227 689,230 691,232 693,234 694,236 696,239 698,241 700,243 701,245 703,247 705,250 706,252 708,254 710,256 711,259 713,261 715,263 716,266 718,268 720,271 722,273 723,276 725,278 727,281 728,283 730,286 732,288 733,291 735,294 737,296 738,299 740,302 742,304 744,307 745,309 747,312 749,314 750,317 752,319 754,322 755,324 757,326 759,329 760,331 762,333 764,335 766,338 767,340 769,342 771,344 772,346 774,348 776,350 777,352 779,355 781,357 782,359 784,361 786,363 788,365 789,367 791,369 793,371 794,373 796,375 798,377 799,379 801,381 803,383 804,385 806,387 808,389 810,391 811,393 813,394 815,396 816,398 818,400 820,402 821,404 823,405 825,407 827,409 828,410 830,412 832,414 833,415 835,417 837,418 838,420 840,421 842,422 843,424 845,425 847,426 849,428 850,429 852,430 854,431 855,432 857,434 859,435 860,436 862,437 864,438 865,439 867,440 869,441 871,442 872,443 874,444 876,445 877,446 879,447 881,448 882,449 884,450 886,451 887,452 889,453 891,454 893,455 894,455 896,456 898,457 899,458 901,459 903,459 904,460 906,461 908,462 909,462 911,463 913,464 915,465 916,466 918,466 920,467 921,468 923,468 925,469 926,470 928,471 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,437 166,436 168,435 169,434 171,433 173,432 175,431 176,430 178,429 180,428 181,427 183,426 185,424 186,423 188,422 190,421 191,420 193,418 195,417 197,416 198,414 200,413 202,411 203,410 205,409 207,407 208,406 210,404 212,403 214,401 215,400 217,398 219,397 220,395 222,394 224,392 225,391 227,389 229,388 230,387 232,385 234,384 236,382 237,381 239,380 241,379 242,377 244,376 246,374 247,373 249,372 251,370 252,369 254,367 256,366 258,364 259,362 261,361 263,359 264,357 266,355 268,353 269,351 271,349 273,347 274,345 276,343 278,341 280,339 281,337 283,335 285,333 286,330 288,328 290,326 291,324 293,322 295,319 296,317 298,315 300,313 302,311 303,308 305,306 307,304 308,302 310,299 312,297 313,295 315,292 317,290 318,288 320,285 322,283 324,281 325,279 327,276 329,274 330,272 332,270 334,267 335,265 337,263 339,261 341,259 342,257 344,255 346,253 347,251 349,249 351,247 352,245 354,243 356,241 357,240 359,238 361,236 363,234 364,232 366,230 368,228 369,226 371,224 373,223 374,221 376,218 378,216 379,214 381,212 383,210 385,208 386,206 388,204 390,201 391,199 393,197 395,195 396,192 398,190 400,188 401,186 403,184 405,181 407,179 408,177 410,175 412,173 413,171 415,169 417,167 418,165 420,163 422,162 423,160 425,158 427,156 429,155 430,153 432,151 434,150 435,148 437,146 439,145 440,143 442,142 444,140 445,138 447,137 449,135 451,134 452,132 454,131 456,129 457,128 459,126 461,125 462,123 464,122 466,121 468,120 469,118 471,117 473,116 474,115 476,114 478,113 479,112 481,111 483,111 484,110 486,109 488,108 490,107 491,106 493,106 495,105 496,104 498,103 500,102 501,102 503,101 505,100 506,100 508,99 510,98 512,98 513,97 515,96 517,96 518,96 520,95 522,95 523,94 525,94 527,94 528,94 530,94 532,94 534,94 535,94 537,94 539,94 540,95 542,95 544,95 545,96 547,96 549,97 550,98 552,98 554,99 556,100 557,101 559,102 561,103 562,104 564,105 566,106 567,107 569,108 571,109 573,111 574,112 576,113 578,114 579,115 581,116 583,117 584,118 586,119 588,120 589,121 591,121 593,122 595,123 596,124 598,125 600,126 601,127 603,128 605,129 606,130 608,131 610,133 611,134 613,135 615,137 617,138 618,140 620,141 622,143 623,145 625,147 627,148 628,150 630,152 632,154 633,156 635,158 637,160 639,162 640,164 642,166 644,168 645,170 647,172 649,175 650,177 652,179 654,181 655,183 657,186 659,188 661,190 662,192 664,195 666,197 667,199 669,202 671,204 672,207 674,209 676,211 677,214 679,216 681,218 683,221 684,223 686,225 688,227 689,230 691,232 693,234 694,236 696,239 698,241 700,243 701,245 703,247 705,250 706,252 708,254 710,256 711,259 713,261 715,263 716,266 718,268 720,271 722,273 723,276 725,278 727,281 728,283 730,286 732,288 733,291 735,294 737,296 738,299 740,302 742,304 744,307 745,309 747,312 749,314 750,317 752,319 754,322 755,324 757,326 759,329 760,331 762,333 764,335 766,338 767,340 769,342 771,344 772,346 774,348 776,350 777,352 779,355 781,357 782,359 784,361 786,363 788,365 789,367 791,369 793,371 794,373 796,375 798,377 799,379 801,381 803,383 804,385 806,387 808,389 810,391 811,393 813,394 815,396 816,398 818,400 820,402 821,404 823,405 825,407 827,409 828,410 830,412 832,414 833,415 835,417 837,418 838,420 840,421 842,422 843,424 845,425 847,426 849,428 850,429 852,430 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="539,473 539,94 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

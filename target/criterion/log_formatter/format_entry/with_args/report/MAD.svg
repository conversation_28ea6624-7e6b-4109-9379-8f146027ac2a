<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/with_args:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="443" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,443 86,443 "/>
<text x="77" y="395" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,395 86,395 "/>
<text x="77" y="346" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,346 86,346 "/>
<text x="77" y="298" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,298 86,298 "/>
<text x="77" y="250" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,250 86,250 "/>
<text x="77" y="201" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,201 86,201 "/>
<text x="77" y="153" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,153 86,153 "/>
<text x="77" y="105" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,105 86,105 "/>
<text x="77" y="56" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,56 86,56 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="105" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="105,473 105,478 "/>
<text x="238" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="238,473 238,478 "/>
<text x="372" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="372,473 372,478 "/>
<text x="505" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="505,473 505,478 "/>
<text x="639" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="639,473 639,478 "/>
<text x="772" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="772,473 772,478 "/>
<text x="906" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="906,473 906,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,469 88,468 90,467 92,466 93,465 95,464 97,463 98,461 100,460 102,459 103,458 105,457 107,456 109,455 110,454 112,453 114,452 115,451 117,450 119,448 120,447 122,446 124,445 125,443 127,442 129,440 131,438 132,437 134,435 136,432 137,430 139,427 141,425 142,422 144,419 146,415 147,412 149,409 151,405 153,401 154,398 156,394 158,390 159,387 161,383 163,380 164,377 166,375 168,372 169,370 171,368 173,367 175,366 176,365 178,364 180,364 181,365 183,365 185,366 186,368 188,369 190,370 191,372 193,374 195,375 197,377 198,379 200,380 202,381 203,382 205,383 207,383 208,383 210,383 212,382 214,382 215,381 217,379 219,377 220,376 222,374 224,371 225,369 227,367 229,364 230,362 232,360 234,357 236,355 237,354 239,352 241,350 242,349 244,348 246,348 247,347 249,347 251,347 252,347 254,348 256,348 258,349 259,350 261,351 263,352 264,352 266,353 268,354 269,354 271,354 273,354 274,354 276,353 278,353 280,352 281,350 283,348 285,347 286,344 288,342 290,339 291,336 293,333 295,330 296,326 298,323 300,319 302,315 303,312 305,308 307,304 308,300 310,297 312,293 313,289 315,286 317,282 318,279 320,275 322,272 324,269 325,266 327,263 329,260 330,257 332,254 334,252 335,249 337,247 339,244 341,242 342,240 344,238 346,236 347,234 349,232 351,231 352,230 354,229 356,229 357,229 359,229 361,230 363,230 364,232 366,233 368,235 369,238 371,240 373,243 374,246 376,249 378,253 379,256 381,260 383,263 385,267 386,270 388,273 390,276 391,279 393,282 395,284 396,286 398,288 400,290 401,291 403,292 405,293 407,294 408,294 410,294 412,294 413,294 415,294 417,294 418,294 420,294 422,294 423,294 425,294 427,294 429,294 430,294 432,294 434,295 435,295 437,295 439,295 440,296 442,296 444,296 445,296 447,295 449,295 451,294 452,293 454,292 456,291 457,290 459,288 461,286 462,284 464,282 466,280 468,278 469,276 471,274 473,272 474,270 476,268 478,266 479,264 481,262 483,261 484,259 486,258 488,257 490,256 491,255 493,254 495,253 496,253 498,252 500,252 501,251 503,251 505,251 506,251 508,250 510,250 512,250 513,250 515,249 517,249 518,248 520,247 522,246 523,245 525,244 527,243 528,241 530,239 532,237 534,235 535,232 537,229 539,226 540,223 542,220 544,217 545,213 547,210 549,206 550,203 552,199 554,196 556,192 557,189 559,186 561,183 562,181 564,178 566,176 567,174 569,172 571,171 573,170 574,169 576,168 578,168 579,168 581,168 583,168 584,168 586,168 588,168 589,168 591,168 593,168 595,168 596,167 598,166 600,165 601,164 603,162 605,160 606,157 608,154 610,151 611,147 613,143 615,139 617,135 618,131 620,126 622,122 623,117 625,113 627,109 628,106 630,102 632,100 633,97 635,95 637,94 639,93 640,93 642,94 644,95 645,96 647,99 649,101 650,104 652,108 654,111 655,115 657,119 659,124 661,128 662,132 664,137 666,141 667,145 669,150 671,154 672,158 674,161 676,165 677,168 679,172 681,175 683,178 684,181 686,184 688,187 689,190 691,193 693,195 694,198 696,200 698,203 700,205 701,207 703,209 705,211 706,213 708,215 710,217 711,219 713,220 715,222 716,223 718,225 720,226 722,227 723,229 725,230 727,232 728,233 730,235 732,237 733,239 735,241 737,243 738,246 740,248 742,251 744,254 745,257 747,260 749,263 750,266 752,270 754,274 755,278 757,282 759,286 760,290 762,294 764,298 766,303 767,307 769,311 771,315 772,320 774,324 776,328 777,332 779,336 781,339 782,343 784,346 786,349 788,352 789,355 791,357 793,360 794,362 796,364 798,366 799,367 801,369 803,371 804,372 806,374 808,375 810,376 811,378 813,379 815,381 816,382 818,384 820,385 821,387 823,389 825,391 827,392 828,394 830,397 832,399 833,401 835,403 837,406 838,408 840,410 842,413 843,415 845,418 847,420 849,422 850,425 852,427 854,429 855,432 857,434 859,436 860,438 862,440 864,442 865,443 867,445 869,447 871,448 872,449 874,451 876,452 877,453 879,454 881,456 882,457 884,458 886,458 887,459 889,460 891,461 893,462 894,462 896,463 898,464 899,464 901,465 903,465 904,466 906,466 908,467 909,467 911,468 913,468 915,469 916,469 918,469 920,470 921,470 923,471 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,377 166,375 168,372 169,370 171,368 173,367 175,366 176,365 178,364 180,364 181,365 183,365 185,366 186,368 188,369 190,370 191,372 193,374 195,375 197,377 198,379 200,380 202,381 203,382 205,383 207,383 208,383 210,383 212,382 214,382 215,381 217,379 219,377 220,376 222,374 224,371 225,369 227,367 229,364 230,362 232,360 234,357 236,355 237,354 239,352 241,350 242,349 244,348 246,348 247,347 249,347 251,347 252,347 254,348 256,348 258,349 259,350 261,351 263,352 264,352 266,353 268,354 269,354 271,354 273,354 274,354 276,353 278,353 280,352 281,350 283,348 285,347 286,344 288,342 290,339 291,336 293,333 295,330 296,326 298,323 300,319 302,315 303,312 305,308 307,304 308,300 310,297 312,293 313,289 315,286 317,282 318,279 320,275 322,272 324,269 325,266 327,263 329,260 330,257 332,254 334,252 335,249 337,247 339,244 341,242 342,240 344,238 346,236 347,234 349,232 351,231 352,230 354,229 356,229 357,229 359,229 361,230 363,230 364,232 366,233 368,235 369,238 371,240 373,243 374,246 376,249 378,253 379,256 381,260 383,263 385,267 386,270 388,273 390,276 391,279 393,282 395,284 396,286 398,288 400,290 401,291 403,292 405,293 407,294 408,294 410,294 412,294 413,294 415,294 417,294 418,294 420,294 422,294 423,294 425,294 427,294 429,294 430,294 432,294 434,295 435,295 437,295 439,295 440,296 442,296 444,296 445,296 447,295 449,295 451,294 452,293 454,292 456,291 457,290 459,288 461,286 462,284 464,282 466,280 468,278 469,276 471,274 473,272 474,270 476,268 478,266 479,264 481,262 483,261 484,259 486,258 488,257 490,256 491,255 493,254 495,253 496,253 498,252 500,252 501,251 503,251 505,251 506,251 508,250 510,250 512,250 513,250 515,249 517,249 518,248 520,247 522,246 523,245 525,244 527,243 528,241 530,239 532,237 534,235 535,232 537,229 539,226 540,223 542,220 544,217 545,213 547,210 549,206 550,203 552,199 554,196 556,192 557,189 559,186 561,183 562,181 564,178 566,176 567,174 569,172 571,171 573,170 574,169 576,168 578,168 579,168 581,168 583,168 584,168 586,168 588,168 589,168 591,168 593,168 595,168 596,167 598,166 600,165 601,164 603,162 605,160 606,157 608,154 610,151 611,147 613,143 615,139 617,135 618,131 620,126 622,122 623,117 625,113 627,109 628,106 630,102 632,100 633,97 635,95 637,94 639,93 640,93 642,94 644,95 645,96 647,99 649,101 650,104 652,108 654,111 655,115 657,119 659,124 661,128 662,132 664,137 666,141 667,145 669,150 671,154 672,158 674,161 676,165 677,168 679,172 681,175 683,178 684,181 686,184 688,187 689,190 691,193 693,195 694,198 696,200 698,203 700,205 701,207 703,209 705,211 706,213 708,215 710,217 711,219 713,220 715,222 716,223 718,225 720,226 722,227 723,229 725,230 727,232 728,233 730,235 732,237 733,239 735,241 737,243 738,246 740,248 742,251 744,254 745,257 747,260 749,263 750,266 752,270 754,274 755,278 757,282 759,286 760,290 762,294 764,298 766,303 767,307 769,311 771,315 772,320 774,324 776,328 777,332 779,336 781,339 782,343 784,346 786,349 788,352 789,355 791,357 793,360 794,362 796,364 798,366 799,367 801,369 803,371 804,372 806,374 808,375 810,376 811,378 813,379 815,381 816,382 818,384 820,385 821,387 823,389 825,391 827,392 828,394 830,397 832,399 833,401 835,403 837,406 838,408 840,410 842,413 843,415 845,418 847,420 849,422 850,425 852,427 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="593,473 593,168 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

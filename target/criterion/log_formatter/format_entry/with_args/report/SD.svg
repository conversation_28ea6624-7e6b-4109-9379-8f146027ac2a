<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/with_args:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="440" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,440 86,440 "/>
<text x="77" y="398" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,398 86,398 "/>
<text x="77" y="357" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,357 86,357 "/>
<text x="77" y="315" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,315 86,315 "/>
<text x="77" y="274" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,274 86,274 "/>
<text x="77" y="232" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,232 86,232 "/>
<text x="77" y="190" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,190 86,190 "/>
<text x="77" y="149" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,149 86,149 "/>
<text x="77" y="107" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,107 86,107 "/>
<text x="77" y="66" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,66 86,66 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="129" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="129,473 129,478 "/>
<text x="237" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
12
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="237,473 237,478 "/>
<text x="345" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
14
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="345,473 345,478 "/>
<text x="453" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
16
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="453,473 453,478 "/>
<text x="560" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
18
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="560,473 560,478 "/>
<text x="668" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="668,473 668,478 "/>
<text x="776" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
22
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="776,473 776,478 "/>
<text x="884" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
24
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="884,473 884,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,469 97,468 98,467 100,466 102,466 103,464 105,463 107,462 109,461 110,460 112,459 114,457 115,456 117,454 119,453 120,451 122,450 124,448 125,446 127,444 129,442 131,440 132,438 134,436 136,434 137,431 139,429 141,426 142,424 144,421 146,418 147,415 149,413 151,409 153,406 154,403 156,400 158,396 159,393 161,389 163,386 164,382 166,378 168,374 169,370 171,366 173,362 175,358 176,354 178,350 180,345 181,341 183,337 185,332 186,328 188,323 190,319 191,315 193,310 195,306 197,301 198,297 200,293 202,288 203,284 205,280 207,276 208,272 210,268 212,264 214,261 215,257 217,253 219,250 220,247 222,243 224,240 225,237 227,235 229,232 230,229 232,227 234,225 236,223 237,221 239,219 241,217 242,216 244,215 246,214 247,213 249,212 251,212 252,212 254,212 256,212 258,213 259,213 261,214 263,215 264,216 266,218 268,220 269,222 271,224 273,226 274,229 276,232 278,235 280,238 281,241 283,244 285,248 286,252 288,256 290,260 291,264 293,268 295,272 296,277 298,281 300,286 302,291 303,296 305,300 307,305 308,310 310,315 312,320 313,325 315,330 317,335 318,340 320,344 322,349 324,354 325,359 327,363 329,368 330,373 332,377 334,381 335,386 337,390 339,394 341,398 342,401 344,405 346,409 347,412 349,415 351,418 352,421 354,424 356,427 357,429 359,432 361,434 363,436 364,438 366,440 368,441 369,443 371,444 373,445 374,446 376,447 378,447 379,448 381,448 383,448 385,448 386,447 388,447 390,446 391,445 393,444 395,442 396,440 398,438 400,436 401,433 403,430 405,427 407,423 408,420 410,415 412,411 413,406 415,401 417,396 418,390 420,384 422,378 423,371 425,364 427,357 429,349 430,342 432,333 434,325 435,317 437,308 439,299 440,290 442,280 444,271 445,261 447,252 449,242 451,232 452,223 454,213 456,204 457,195 459,186 461,177 462,169 464,160 466,153 468,145 469,138 471,131 473,125 474,119 476,114 478,109 479,105 481,102 483,99 484,96 486,94 488,93 490,92 491,92 493,93 495,94 496,96 498,98 500,101 501,105 503,108 505,113 506,118 508,123 510,129 512,136 513,142 515,150 517,157 518,165 520,173 522,181 523,190 525,198 527,207 528,216 530,225 532,234 534,244 535,253 537,262 539,271 540,280 542,289 544,298 545,306 547,315 549,323 550,331 552,339 554,346 556,354 557,361 559,367 561,374 562,380 564,386 566,391 567,397 569,402 571,406 573,411 574,415 576,419 578,422 579,425 581,428 583,431 584,433 586,435 588,436 589,438 591,439 593,439 595,440 596,440 598,439 600,439 601,438 603,436 605,435 606,433 608,430 610,428 611,425 613,422 615,418 617,414 618,410 620,406 622,401 623,396 625,391 627,385 628,380 630,374 632,368 633,362 635,356 637,349 639,343 640,337 642,330 644,324 645,318 647,312 649,305 650,300 652,294 654,288 655,283 657,278 659,274 661,270 662,266 664,262 666,259 667,257 669,254 671,253 672,252 674,251 676,251 677,251 679,252 681,253 683,255 684,257 686,260 688,263 689,267 691,271 693,275 694,280 696,285 698,291 700,296 701,302 703,308 705,315 706,321 708,327 710,334 711,340 713,347 715,353 716,360 718,366 720,372 722,378 723,384 725,390 727,395 728,401 730,406 732,410 733,415 735,419 737,423 738,427 740,431 742,434 744,437 745,439 747,442 749,444 750,446 752,447 754,449 755,450 757,451 759,451 760,452 762,452 764,452 766,452 767,451 769,451 771,450 772,449 774,448 776,446 777,445 779,443 781,442 782,440 784,438 786,436 788,434 789,432 791,430 793,427 794,425 796,423 798,421 799,419 801,417 803,415 804,413 806,411 808,410 810,408 811,407 813,406 815,405 816,404 818,403 820,403 821,402 823,402 825,402 827,402 828,403 830,403 832,404 833,405 835,406 837,407 838,409 840,410 842,412 843,414 845,416 847,418 849,420 850,422 852,424 854,426 855,429 857,431 859,433 860,436 862,438 864,440 865,443 867,445 869,447 871,449 872,451 874,453 876,455 877,456 879,458 881,459 882,461 884,462 886,463 887,464 889,465 891,466 893,467 894,468 896,468 898,469 899,469 901,469 903,470 904,470 906,470 908,470 909,470 911,470 913,469 915,469 916,469 918,469 920,468 921,468 923,468 925,467 926,467 928,466 930,466 932,466 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,382 166,378 168,374 169,370 171,366 173,362 175,358 176,354 178,350 180,345 181,341 183,337 185,332 186,328 188,323 190,319 191,315 193,310 195,306 197,301 198,297 200,293 202,288 203,284 205,280 207,276 208,272 210,268 212,264 214,261 215,257 217,253 219,250 220,247 222,243 224,240 225,237 227,235 229,232 230,229 232,227 234,225 236,223 237,221 239,219 241,217 242,216 244,215 246,214 247,213 249,212 251,212 252,212 254,212 256,212 258,213 259,213 261,214 263,215 264,216 266,218 268,220 269,222 271,224 273,226 274,229 276,232 278,235 280,238 281,241 283,244 285,248 286,252 288,256 290,260 291,264 293,268 295,272 296,277 298,281 300,286 302,291 303,296 305,300 307,305 308,310 310,315 312,320 313,325 315,330 317,335 318,340 320,344 322,349 324,354 325,359 327,363 329,368 330,373 332,377 334,381 335,386 337,390 339,394 341,398 342,401 344,405 346,409 347,412 349,415 351,418 352,421 354,424 356,427 357,429 359,432 361,434 363,436 364,438 366,440 368,441 369,443 371,444 373,445 374,446 376,447 378,447 379,448 381,448 383,448 385,448 386,447 388,447 390,446 391,445 393,444 395,442 396,440 398,438 400,436 401,433 403,430 405,427 407,423 408,420 410,415 412,411 413,406 415,401 417,396 418,390 420,384 422,378 423,371 425,364 427,357 429,349 430,342 432,333 434,325 435,317 437,308 439,299 440,290 442,280 444,271 445,261 447,252 449,242 451,232 452,223 454,213 456,204 457,195 459,186 461,177 462,169 464,160 466,153 468,145 469,138 471,131 473,125 474,119 476,114 478,109 479,105 481,102 483,99 484,96 486,94 488,93 490,92 491,92 493,93 495,94 496,96 498,98 500,101 501,105 503,108 505,113 506,118 508,123 510,129 512,136 513,142 515,150 517,157 518,165 520,173 522,181 523,190 525,198 527,207 528,216 530,225 532,234 534,244 535,253 537,262 539,271 540,280 542,289 544,298 545,306 547,315 549,323 550,331 552,339 554,346 556,354 557,361 559,367 561,374 562,380 564,386 566,391 567,397 569,402 571,406 573,411 574,415 576,419 578,422 579,425 581,428 583,431 584,433 586,435 588,436 589,438 591,439 593,439 595,440 596,440 598,439 600,439 601,438 603,436 605,435 606,433 608,430 610,428 611,425 613,422 615,418 617,414 618,410 620,406 622,401 623,396 625,391 627,385 628,380 630,374 632,368 633,362 635,356 637,349 639,343 640,337 642,330 644,324 645,318 647,312 649,305 650,300 652,294 654,288 655,283 657,278 659,274 661,270 662,266 664,262 666,259 667,257 669,254 671,253 672,252 674,251 676,251 677,251 679,252 681,253 683,255 684,257 686,260 688,263 689,267 691,271 693,275 694,280 696,285 698,291 700,296 701,302 703,308 705,315 706,321 708,327 710,334 711,340 713,347 715,353 716,360 718,366 720,372 722,378 723,384 725,390 727,395 728,401 730,406 732,410 733,415 735,419 737,423 738,427 740,431 742,434 744,437 745,439 747,442 749,444 750,446 752,447 754,449 755,450 757,451 759,451 760,452 762,452 764,452 766,452 767,451 769,451 771,450 772,449 774,448 776,446 777,445 779,443 781,442 782,440 784,438 786,436 788,434 789,432 791,430 793,427 794,425 796,423 798,421 799,419 801,417 803,415 804,413 806,411 808,410 810,408 811,407 813,406 815,405 816,404 818,403 820,403 821,402 823,402 825,402 827,402 828,403 830,403 832,404 833,405 835,406 837,407 838,409 840,410 842,412 843,414 845,416 847,418 849,420 850,422 852,424 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="498,473 498,98 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

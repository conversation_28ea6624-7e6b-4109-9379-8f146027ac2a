<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/with_args:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="421" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,421 86,421 "/>
<text x="77" y="369" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,369 86,369 "/>
<text x="77" y="317" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,317 86,317 "/>
<text x="77" y="265" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,265 86,265 "/>
<text x="77" y="213" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,213 86,213 "/>
<text x="77" y="161" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,161 86,161 "/>
<text x="77" y="110" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,110 86,110 "/>
<text x="77" y="58" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,58 86,58 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="206" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
790
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="206,473 206,478 "/>
<text x="342" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
792
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="342,473 342,478 "/>
<text x="478" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
794
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="478,473 478,478 "/>
<text x="614" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
796
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="614,473 614,478 "/>
<text x="750" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
798
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="750,473 750,478 "/>
<text x="887" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
800
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="887,473 887,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,466 88,466 90,465 92,465 93,465 95,465 97,465 98,464 100,464 102,464 103,464 105,463 107,463 109,463 110,463 112,462 114,462 115,462 117,461 119,461 120,460 122,460 124,459 125,459 127,458 129,457 131,456 132,455 134,454 136,453 137,452 139,451 141,449 142,448 144,446 146,444 147,442 149,440 151,438 153,436 154,434 156,431 158,429 159,426 161,424 163,422 164,419 166,417 168,415 169,413 171,410 173,409 175,407 176,405 178,404 180,402 181,401 183,400 185,399 186,398 188,397 190,396 191,395 193,395 195,394 197,393 198,393 200,392 202,391 203,390 205,390 207,389 208,388 210,387 212,386 214,385 215,384 217,382 219,381 220,380 222,378 224,377 225,375 227,373 229,372 230,370 232,368 234,366 236,364 237,362 239,359 241,357 242,354 244,351 246,347 247,344 249,340 251,337 252,333 254,329 256,325 258,321 259,317 261,313 263,310 264,307 266,304 268,302 269,301 271,300 273,300 274,301 276,303 278,306 280,309 281,313 283,318 285,323 286,328 288,334 290,340 291,345 293,350 295,355 296,360 298,363 300,366 302,368 303,369 305,370 307,369 308,368 310,366 312,363 313,360 315,357 317,353 318,349 320,346 322,343 324,340 325,338 327,336 329,335 330,335 332,335 334,336 335,338 337,340 339,342 341,345 342,347 344,349 346,351 347,351 349,351 351,350 352,348 354,345 356,340 357,333 359,325 361,315 363,304 364,292 366,278 368,264 369,248 371,232 373,216 374,200 376,184 378,168 379,154 381,140 383,128 385,117 386,108 388,101 390,96 391,93 393,92 395,92 396,95 398,99 400,105 401,112 403,120 405,129 407,139 408,150 410,161 412,172 413,183 415,194 417,205 418,215 420,225 422,234 423,243 425,251 427,259 429,266 430,273 432,279 434,284 435,289 437,293 439,297 440,301 442,304 444,307 445,309 447,312 449,314 451,316 452,317 454,319 456,321 457,322 459,324 461,325 462,326 464,327 466,329 468,330 469,331 471,332 473,332 474,333 476,333 478,333 479,332 481,331 483,330 484,328 486,326 488,324 490,322 491,319 493,317 495,315 496,313 498,311 500,310 501,309 503,309 505,310 506,312 508,314 510,317 512,320 513,325 515,329 517,334 518,339 520,345 522,350 523,355 525,360 527,364 528,369 530,372 532,376 534,378 535,381 537,383 539,385 540,386 542,387 544,388 545,389 547,390 549,391 550,392 552,393 554,394 556,396 557,397 559,398 561,399 562,401 564,402 566,403 567,403 569,404 571,404 573,404 574,404 576,404 578,403 579,402 581,401 583,401 584,400 586,399 588,398 589,398 591,398 593,398 595,399 596,400 598,401 600,403 601,405 603,407 605,410 606,413 608,416 610,420 611,423 613,427 615,430 617,434 618,438 620,441 622,445 623,448 625,451 627,453 628,456 630,458 632,460 633,462 635,463 637,465 639,466 640,467 642,468 644,468 645,469 647,469 649,470 650,470 652,470 654,470 655,470 657,470 659,470 661,469 662,469 664,469 666,469 667,469 669,468 671,468 672,468 674,468 676,467 677,467 679,467 681,467 683,467 684,466 686,466 688,466 689,466 691,465 693,465 694,464 696,464 698,463 700,462 701,462 703,461 705,460 706,459 708,458 710,457 711,456 713,455 715,454 716,453 718,452 720,451 722,450 723,450 725,449 727,449 728,449 730,450 732,450 733,451 735,451 737,452 738,453 740,454 742,456 744,457 745,458 747,459 749,461 750,462 752,463 754,464 755,465 757,466 759,467 760,468 762,469 764,469 766,470 767,470 769,471 771,471 772,471 774,471 776,471 777,472 779,472 781,472 782,472 784,472 786,472 788,472 789,472 791,472 793,472 794,472 796,472 798,471 799,471 801,471 803,471 804,471 806,471 808,471 810,471 811,470 813,470 815,470 816,469 818,469 820,469 821,468 823,467 825,467 827,466 828,465 830,464 832,463 833,462 835,461 837,460 838,459 840,458 842,457 843,456 845,455 847,454 849,454 850,453 852,453 854,453 855,453 857,453 859,453 860,454 862,454 864,455 865,456 867,457 869,458 871,459 872,460 874,461 876,462 877,463 879,464 881,465 882,466 884,467 886,467 887,468 889,469 891,469 893,470 894,470 896,471 898,471 899,471 901,472 903,472 904,472 906,472 908,472 909,472 911,472 913,472 915,472 916,472 918,472 920,472 921,472 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,419 166,417 168,415 169,413 171,410 173,409 175,407 176,405 178,404 180,402 181,401 183,400 185,399 186,398 188,397 190,396 191,395 193,395 195,394 197,393 198,393 200,392 202,391 203,390 205,390 207,389 208,388 210,387 212,386 214,385 215,384 217,382 219,381 220,380 222,378 224,377 225,375 227,373 229,372 230,370 232,368 234,366 236,364 237,362 239,359 241,357 242,354 244,351 246,347 247,344 249,340 251,337 252,333 254,329 256,325 258,321 259,317 261,313 263,310 264,307 266,304 268,302 269,301 271,300 273,300 274,301 276,303 278,306 280,309 281,313 283,318 285,323 286,328 288,334 290,340 291,345 293,350 295,355 296,360 298,363 300,366 302,368 303,369 305,370 307,369 308,368 310,366 312,363 313,360 315,357 317,353 318,349 320,346 322,343 324,340 325,338 327,336 329,335 330,335 332,335 334,336 335,338 337,340 339,342 341,345 342,347 344,349 346,351 347,351 349,351 351,350 352,348 354,345 356,340 357,333 359,325 361,315 363,304 364,292 366,278 368,264 369,248 371,232 373,216 374,200 376,184 378,168 379,154 381,140 383,128 385,117 386,108 388,101 390,96 391,93 393,92 395,92 396,95 398,99 400,105 401,112 403,120 405,129 407,139 408,150 410,161 412,172 413,183 415,194 417,205 418,215 420,225 422,234 423,243 425,251 427,259 429,266 430,273 432,279 434,284 435,289 437,293 439,297 440,301 442,304 444,307 445,309 447,312 449,314 451,316 452,317 454,319 456,321 457,322 459,324 461,325 462,326 464,327 466,329 468,330 469,331 471,332 473,332 474,333 476,333 478,333 479,332 481,331 483,330 484,328 486,326 488,324 490,322 491,319 493,317 495,315 496,313 498,311 500,310 501,309 503,309 505,310 506,312 508,314 510,317 512,320 513,325 515,329 517,334 518,339 520,345 522,350 523,355 525,360 527,364 528,369 530,372 532,376 534,378 535,381 537,383 539,385 540,386 542,387 544,388 545,389 547,390 549,391 550,392 552,393 554,394 556,396 557,397 559,398 561,399 562,401 564,402 566,403 567,403 569,404 571,404 573,404 574,404 576,404 578,403 579,402 581,401 583,401 584,400 586,399 588,398 589,398 591,398 593,398 595,399 596,400 598,401 600,403 601,405 603,407 605,410 606,413 608,416 610,420 611,423 613,427 615,430 617,434 618,438 620,441 622,445 623,448 625,451 627,453 628,456 630,458 632,460 633,462 635,463 637,465 639,466 640,467 642,468 644,468 645,469 647,469 649,470 650,470 652,470 654,470 655,470 657,470 659,470 661,469 662,469 664,469 666,469 667,469 669,468 671,468 672,468 674,468 676,467 677,467 679,467 681,467 683,467 684,466 686,466 688,466 689,466 691,465 693,465 694,464 696,464 698,463 700,462 701,462 703,461 705,460 706,459 708,458 710,457 711,456 713,455 715,454 716,453 718,452 720,451 722,450 723,450 725,449 727,449 728,449 730,450 732,450 733,451 735,451 737,452 738,453 740,454 742,456 744,457 745,458 747,459 749,461 750,462 752,463 754,464 755,465 757,466 759,467 760,468 762,469 764,469 766,470 767,470 769,471 771,471 772,471 774,471 776,471 777,472 779,472 781,472 782,472 784,472 786,472 788,472 789,472 791,472 793,472 794,472 796,472 798,471 799,471 801,471 803,471 804,471 806,471 808,471 810,471 811,470 813,470 815,470 816,469 818,469 820,469 821,468 823,467 825,467 827,466 828,465 830,464 832,463 833,462 835,461 837,460 838,459 840,458 842,457 843,456 845,455 847,454 849,454 850,453 852,453 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="394,473 394,92 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

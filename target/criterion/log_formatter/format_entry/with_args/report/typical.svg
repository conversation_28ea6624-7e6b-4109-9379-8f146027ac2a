<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/with_args:typical
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="417" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,417 86,417 "/>
<text x="77" y="339" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,339 86,339 "/>
<text x="77" y="261" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,261 86,261 "/>
<text x="77" y="183" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,183 86,183 "/>
<text x="77" y="105" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,105 86,105 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="152" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
792
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="152,473 152,478 "/>
<text x="265" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
793
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="265,473 265,478 "/>
<text x="378" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
794
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="378,473 378,478 "/>
<text x="492" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
795
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="492,473 492,478 "/>
<text x="605" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
796
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="605,473 605,478 "/>
<text x="718" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
797
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="718,473 718,478 "/>
<text x="832" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
798
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="832,473 832,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,471 93,470 95,470 97,469 98,468 100,468 102,467 103,466 105,466 107,465 109,464 110,463 112,463 114,462 115,461 117,460 119,459 120,458 122,458 124,457 125,456 127,455 129,454 131,453 132,452 134,451 136,450 137,449 139,448 141,447 142,446 144,444 146,443 147,442 149,441 151,439 153,438 154,437 156,435 158,434 159,433 161,431 163,430 164,428 166,427 168,426 169,424 171,423 173,421 175,420 176,419 178,417 180,416 181,415 183,413 185,412 186,411 188,410 190,408 191,407 193,406 195,405 197,403 198,402 200,401 202,399 203,398 205,396 207,395 208,393 210,392 212,390 214,389 215,387 217,385 219,384 220,382 222,380 224,379 225,377 227,375 229,374 230,372 232,370 234,368 236,367 237,365 239,363 241,361 242,360 244,358 246,356 247,355 249,353 251,351 252,349 254,347 256,345 258,344 259,342 261,340 263,338 264,336 266,334 268,332 269,329 271,327 273,325 274,323 276,320 278,318 280,316 281,313 283,311 285,308 286,305 288,303 290,300 291,298 293,295 295,292 296,290 298,287 300,284 302,281 303,279 305,276 307,273 308,271 310,268 312,265 313,263 315,260 317,258 318,255 320,253 322,250 324,248 325,245 327,243 329,241 330,238 332,236 334,234 335,232 337,230 339,228 341,225 342,223 344,221 346,219 347,217 349,215 351,213 352,211 354,208 356,206 357,204 359,202 361,200 363,198 364,196 366,193 368,191 369,189 371,187 373,185 374,183 376,181 378,179 379,177 381,176 383,174 385,172 386,170 388,169 390,167 391,165 393,163 395,162 396,160 398,158 400,157 401,155 403,153 405,152 407,150 408,148 410,147 412,145 413,143 415,142 417,140 418,139 420,137 422,136 423,134 425,133 427,131 429,130 430,129 432,127 434,126 435,125 437,124 439,122 440,121 442,120 444,119 445,118 447,117 449,116 451,115 452,114 454,113 456,112 457,111 459,110 461,108 462,107 464,106 466,105 468,104 469,103 471,102 473,100 474,99 476,98 478,97 479,97 481,96 483,95 484,95 486,94 488,94 490,94 491,94 493,94 495,94 496,94 498,94 500,95 501,95 503,96 505,97 506,97 508,98 510,99 512,100 513,100 515,101 517,102 518,103 520,104 522,104 523,105 525,106 527,106 528,107 530,108 532,108 534,109 535,110 537,110 539,111 540,111 542,112 544,112 545,113 547,114 549,114 550,115 552,115 554,116 556,117 557,117 559,118 561,119 562,119 564,120 566,121 567,122 569,123 571,124 573,125 574,126 576,128 578,129 579,130 581,132 583,134 584,136 586,137 588,139 589,141 591,143 593,146 595,148 596,150 598,152 600,155 601,157 603,159 605,162 606,164 608,166 610,169 611,171 613,174 615,176 617,178 618,181 620,183 622,185 623,187 625,190 627,192 628,194 630,196 632,199 633,201 635,203 637,205 639,208 640,210 642,212 644,214 645,217 647,219 649,222 650,224 652,226 654,229 655,231 657,233 659,236 661,238 662,241 664,243 666,245 667,248 669,250 671,252 672,254 674,257 676,259 677,261 679,263 681,265 683,267 684,269 686,272 688,274 689,276 691,277 693,279 694,281 696,283 698,285 700,287 701,289 703,291 705,293 706,295 708,296 710,298 711,300 713,302 715,304 716,306 718,308 720,309 722,311 723,313 725,315 727,317 728,319 730,321 732,322 733,324 735,326 737,328 738,330 740,332 742,334 744,336 745,338 747,340 749,342 750,344 752,346 754,348 755,350 757,352 759,354 760,356 762,358 764,360 766,362 767,364 769,366 771,368 772,370 774,372 776,374 777,375 779,377 781,379 782,381 784,382 786,384 788,385 789,387 791,389 793,390 794,392 796,393 798,394 799,396 801,397 803,399 804,400 806,401 808,403 810,404 811,405 813,407 815,408 816,409 818,411 820,412 821,413 823,415 825,416 827,417 828,418 830,420 832,421 833,422 835,424 837,425 838,426 840,427 842,428 843,430 845,431 847,432 849,433 850,434 852,435 854,436 855,437 857,438 859,439 860,440 862,441 864,442 865,443 867,444 869,445 871,446 872,447 874,448 876,449 877,449 879,450 881,451 882,452 884,453 886,453 887,454 889,455 891,456 893,456 894,457 896,458 898,459 899,459 901,460 903,461 904,462 906,462 908,463 909,464 911,464 913,465 915,466 916,466 918,467 920,468 921,468 923,469 925,470 926,470 928,471 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,428 166,427 168,426 169,424 171,423 173,421 175,420 176,419 178,417 180,416 181,415 183,413 185,412 186,411 188,410 190,408 191,407 193,406 195,405 197,403 198,402 200,401 202,399 203,398 205,396 207,395 208,393 210,392 212,390 214,389 215,387 217,385 219,384 220,382 222,380 224,379 225,377 227,375 229,374 230,372 232,370 234,368 236,367 237,365 239,363 241,361 242,360 244,358 246,356 247,355 249,353 251,351 252,349 254,347 256,345 258,344 259,342 261,340 263,338 264,336 266,334 268,332 269,329 271,327 273,325 274,323 276,320 278,318 280,316 281,313 283,311 285,308 286,305 288,303 290,300 291,298 293,295 295,292 296,290 298,287 300,284 302,281 303,279 305,276 307,273 308,271 310,268 312,265 313,263 315,260 317,258 318,255 320,253 322,250 324,248 325,245 327,243 329,241 330,238 332,236 334,234 335,232 337,230 339,228 341,225 342,223 344,221 346,219 347,217 349,215 351,213 352,211 354,208 356,206 357,204 359,202 361,200 363,198 364,196 366,193 368,191 369,189 371,187 373,185 374,183 376,181 378,179 379,177 381,176 383,174 385,172 386,170 388,169 390,167 391,165 393,163 395,162 396,160 398,158 400,157 401,155 403,153 405,152 407,150 408,148 410,147 412,145 413,143 415,142 417,140 418,139 420,137 422,136 423,134 425,133 427,131 429,130 430,129 432,127 434,126 435,125 437,124 439,122 440,121 442,120 444,119 445,118 447,117 449,116 451,115 452,114 454,113 456,112 457,111 459,110 461,108 462,107 464,106 466,105 468,104 469,103 471,102 473,100 474,99 476,98 478,97 479,97 481,96 483,95 484,95 486,94 488,94 490,94 491,94 493,94 495,94 496,94 498,94 500,95 501,95 503,96 505,97 506,97 508,98 510,99 512,100 513,100 515,101 517,102 518,103 520,104 522,104 523,105 525,106 527,106 528,107 530,108 532,108 534,109 535,110 537,110 539,111 540,111 542,112 544,112 545,113 547,114 549,114 550,115 552,115 554,116 556,117 557,117 559,118 561,119 562,119 564,120 566,121 567,122 569,123 571,124 573,125 574,126 576,128 578,129 579,130 581,132 583,134 584,136 586,137 588,139 589,141 591,143 593,146 595,148 596,150 598,152 600,155 601,157 603,159 605,162 606,164 608,166 610,169 611,171 613,174 615,176 617,178 618,181 620,183 622,185 623,187 625,190 627,192 628,194 630,196 632,199 633,201 635,203 637,205 639,208 640,210 642,212 644,214 645,217 647,219 649,222 650,224 652,226 654,229 655,231 657,233 659,236 661,238 662,241 664,243 666,245 667,248 669,250 671,252 672,254 674,257 676,259 677,261 679,263 681,265 683,267 684,269 686,272 688,274 689,276 691,277 693,279 694,281 696,283 698,285 700,287 701,289 703,291 705,293 706,295 708,296 710,298 711,300 713,302 715,304 716,306 718,308 720,309 722,311 723,313 725,315 727,317 728,319 730,321 732,322 733,324 735,326 737,328 738,330 740,332 742,334 744,336 745,338 747,340 749,342 750,344 752,346 754,348 755,350 757,352 759,354 760,356 762,358 764,360 766,362 767,364 769,366 771,368 772,370 774,372 776,374 777,375 779,377 781,379 782,381 784,382 786,384 788,385 789,387 791,389 793,390 794,392 796,393 798,394 799,396 801,397 803,399 804,400 806,401 808,403 810,404 811,405 813,407 815,408 816,409 818,411 820,412 821,413 823,415 825,416 827,417 828,418 830,420 832,421 833,422 835,424 837,425 838,426 840,427 842,428 843,430 845,431 847,432 849,433 850,434 852,435 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="502,473 502,96 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/with_args:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="436" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,436 86,436 "/>
<text x="77" y="390" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,390 86,390 "/>
<text x="77" y="344" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,344 86,344 "/>
<text x="77" y="298" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,298 86,298 "/>
<text x="77" y="253" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,253 86,253 "/>
<text x="77" y="207" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,207 86,207 "/>
<text x="77" y="161" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,161 86,161 "/>
<text x="77" y="115" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,115 86,115 "/>
<text x="77" y="69" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
180
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,69 86,69 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="115" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.018
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="115,473 115,478 "/>
<text x="207" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.016
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="207,473 207,478 "/>
<text x="298" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.014
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="298,473 298,478 "/>
<text x="389" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="389,473 389,478 "/>
<text x="480" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="480,473 480,478 "/>
<text x="572" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="572,473 572,478 "/>
<text x="663" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="663,473 663,478 "/>
<text x="754" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="754,473 754,478 "/>
<text x="846" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="846,473 846,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,471 95,470 97,470 98,469 100,469 102,468 103,468 105,467 107,466 109,465 110,465 112,464 114,463 115,462 117,460 119,459 120,458 122,457 124,455 125,454 127,452 129,450 131,449 132,447 134,445 136,443 137,441 139,439 141,437 142,435 144,433 146,430 147,428 149,426 151,424 153,422 154,419 156,417 158,415 159,412 161,410 163,408 164,405 166,403 168,400 169,398 171,395 173,393 175,390 176,387 178,384 180,381 181,378 183,375 185,372 186,368 188,365 190,361 191,357 193,353 195,349 197,345 198,341 200,337 202,333 203,329 205,324 207,320 208,316 210,312 212,308 214,304 215,300 217,296 219,293 220,289 222,286 224,283 225,280 227,277 229,275 230,272 232,270 234,268 236,266 237,264 239,262 241,261 242,259 244,257 246,256 247,254 249,252 251,251 252,249 254,248 256,246 258,244 259,243 261,241 263,240 264,238 266,237 268,236 269,235 271,233 273,232 274,231 276,230 278,229 280,228 281,227 283,226 285,225 286,223 288,222 290,220 291,217 293,215 295,212 296,208 298,205 300,200 302,196 303,191 305,185 307,179 308,173 310,167 312,160 313,154 315,147 317,141 318,134 320,128 322,121 324,115 325,110 327,104 329,99 330,95 332,91 334,87 335,83 337,80 339,77 341,74 342,71 344,69 346,67 347,65 349,63 351,62 352,60 354,59 356,57 357,56 359,55 361,55 363,54 364,54 366,53 368,54 369,54 371,55 373,56 374,57 376,59 378,61 379,64 381,66 383,70 385,73 386,77 388,81 390,85 391,90 393,94 395,99 396,104 398,109 400,114 401,119 403,123 405,128 407,133 408,138 410,143 412,148 413,153 415,158 417,163 418,168 420,173 422,179 423,184 425,189 427,195 429,200 430,206 432,211 434,217 435,223 437,228 439,234 440,240 442,245 444,251 445,256 447,262 449,267 451,272 452,277 454,282 456,286 457,291 459,295 461,299 462,303 464,307 466,310 468,314 469,317 471,321 473,324 474,327 476,330 478,333 479,337 481,340 483,343 484,346 486,349 488,352 490,355 491,358 493,361 495,363 496,366 498,369 500,371 501,374 503,376 505,379 506,381 508,384 510,386 512,388 513,391 515,393 517,395 518,398 520,400 522,402 523,405 525,407 527,409 528,411 530,414 532,416 534,418 535,420 537,421 539,423 540,425 542,427 544,428 545,430 547,431 549,433 550,434 552,436 554,437 556,438 557,439 559,441 561,442 562,443 564,444 566,445 567,446 569,447 571,447 573,448 574,449 576,449 578,449 579,450 581,450 583,450 584,450 586,450 588,449 589,449 591,449 593,449 595,448 596,448 598,447 600,447 601,447 603,446 605,446 606,446 608,446 610,446 611,445 613,445 615,445 617,446 618,446 620,446 622,446 623,446 625,446 627,447 628,447 630,447 632,448 633,448 635,449 637,449 639,449 640,450 642,450 644,451 645,451 647,452 649,452 650,452 652,453 654,453 655,454 657,455 659,455 661,456 662,456 664,457 666,457 667,458 669,458 671,459 672,459 674,460 676,460 677,461 679,461 681,461 683,461 684,461 686,461 688,461 689,461 691,461 693,460 694,460 696,460 698,460 700,459 701,459 703,458 705,458 706,458 708,457 710,457 711,457 713,457 715,456 716,456 718,456 720,456 722,456 723,455 725,455 727,455 728,455 730,455 732,455 733,455 735,455 737,455 738,455 740,455 742,455 744,455 745,455 747,455 749,455 750,455 752,455 754,455 755,455 757,456 759,456 760,456 762,456 764,457 766,457 767,457 769,458 771,458 772,459 774,459 776,460 777,460 779,461 781,461 782,462 784,462 786,462 788,463 789,463 791,464 793,464 794,464 796,464 798,464 799,465 801,465 803,465 804,465 806,465 808,465 810,465 811,465 813,465 815,465 816,465 818,465 820,465 821,466 823,466 825,466 827,466 828,466 830,466 832,466 833,466 835,466 837,467 838,467 840,467 842,467 843,467 845,467 847,467 849,467 850,468 852,468 854,468 855,468 857,468 859,468 860,468 862,469 864,469 865,469 867,469 869,469 871,470 872,470 874,470 876,470 877,471 879,471 881,471 882,471 884,471 886,472 887,472 889,472 891,472 893,472 894,472 896,472 898,472 899,472 901,472 903,472 904,472 906,472 908,471 909,471 911,471 913,470 915,470 916,470 918,469 920,469 921,468 923,468 925,467 926,467 928,467 930,466 932,466 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,405 166,403 168,400 169,398 171,395 173,393 175,390 176,387 178,384 180,381 181,378 183,375 185,372 186,368 188,365 190,361 191,357 193,353 195,349 197,345 198,341 200,337 202,333 203,329 205,324 207,320 208,316 210,312 212,308 214,304 215,300 217,296 219,293 220,289 222,286 224,283 225,280 227,277 229,275 230,272 232,270 234,268 236,266 237,264 239,262 241,261 242,259 244,257 246,256 247,254 249,252 251,251 252,249 254,248 256,246 258,244 259,243 261,241 263,240 264,238 266,237 268,236 269,235 271,233 273,232 274,231 276,230 278,229 280,228 281,227 283,226 285,225 286,223 288,222 290,220 291,217 293,215 295,212 296,208 298,205 300,200 302,196 303,191 305,185 307,179 308,173 310,167 312,160 313,154 315,147 317,141 318,134 320,128 322,121 324,115 325,110 327,104 329,99 330,95 332,91 334,87 335,83 337,80 339,77 341,74 342,71 344,69 346,67 347,65 349,63 351,62 352,60 354,59 356,57 357,56 359,55 361,55 363,54 364,54 366,53 368,54 369,54 371,55 373,56 374,57 376,59 378,61 379,64 381,66 383,70 385,73 386,77 388,81 390,85 391,90 393,94 395,99 396,104 398,109 400,114 401,119 403,123 405,128 407,133 408,138 410,143 412,148 413,153 415,158 417,163 418,168 420,173 422,179 423,184 425,189 427,195 429,200 430,206 432,211 434,217 435,223 437,228 439,234 440,240 442,245 444,251 445,256 447,262 449,267 451,272 452,277 454,282 456,286 457,291 459,295 461,299 462,303 464,307 466,310 468,314 469,317 471,321 473,324 474,327 476,330 478,333 479,337 481,340 483,343 484,346 486,349 488,352 490,355 491,358 493,361 495,363 496,366 498,369 500,371 501,374 503,376 505,379 506,381 508,384 510,386 512,388 513,391 515,393 517,395 518,398 520,400 522,402 523,405 525,407 527,409 528,411 530,414 532,416 534,418 535,420 537,421 539,423 540,425 542,427 544,428 545,430 547,431 549,433 550,434 552,436 554,437 556,438 557,439 559,441 561,442 562,443 564,444 566,445 567,446 569,447 571,447 573,448 574,449 576,449 578,449 579,450 581,450 583,450 584,450 586,450 588,449 589,449 591,449 593,449 595,448 596,448 598,447 600,447 601,447 603,446 605,446 606,446 608,446 610,446 611,445 613,445 615,445 617,446 618,446 620,446 622,446 623,446 625,446 627,447 628,447 630,447 632,448 633,448 635,449 637,449 639,449 640,450 642,450 644,451 645,451 647,452 649,452 650,452 652,453 654,453 655,454 657,455 659,455 661,456 662,456 664,457 666,457 667,458 669,458 671,459 672,459 674,460 676,460 677,461 679,461 681,461 683,461 684,461 686,461 688,461 689,461 691,461 693,460 694,460 696,460 698,460 700,459 701,459 703,458 705,458 706,458 708,457 710,457 711,457 713,457 715,456 716,456 718,456 720,456 722,456 723,455 725,455 727,455 728,455 730,455 732,455 733,455 735,455 737,455 738,455 740,455 742,455 744,455 745,455 747,455 749,455 750,455 752,455 754,455 755,455 757,456 759,456 760,456 762,456 764,457 766,457 767,457 769,458 771,458 772,459 774,459 776,460 777,460 779,461 781,461 782,462 784,462 786,462 788,463 789,463 791,464 793,464 794,464 796,464 798,464 799,465 801,465 803,465 804,465 806,465 808,465 810,465 811,465 813,465 815,465 816,465 818,465 820,465 821,466 823,466 825,466 827,466 828,466 830,466 832,466 833,466 835,466 837,467 838,467 840,467 842,467 843,467 845,467 847,467 849,467 850,468 852,468 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="357,473 357,56 "/>
<rect x="480" y="53" width="452" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/with_args:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="425" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,425 86,425 "/>
<text x="77" y="359" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,359 86,359 "/>
<text x="77" y="293" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,293 86,293 "/>
<text x="77" y="227" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,227 86,227 "/>
<text x="77" y="161" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,161 86,161 "/>
<text x="77" y="95" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,95 86,95 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="117" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.016
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="117,473 117,478 "/>
<text x="228" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.014
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="228,473 228,478 "/>
<text x="339" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="339,473 339,478 "/>
<text x="450" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="450,473 450,478 "/>
<text x="560" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="560,473 560,478 "/>
<text x="671" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="671,473 671,478 "/>
<text x="782" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="782,473 782,478 "/>
<text x="893" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="893,473 893,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,470 95,470 97,469 98,469 100,468 102,467 103,466 105,466 107,465 109,464 110,463 112,462 114,461 115,461 117,460 119,459 120,458 122,457 124,456 125,455 127,453 129,452 131,451 132,450 134,449 136,448 137,447 139,446 141,444 142,443 144,442 146,441 147,439 149,438 151,437 153,435 154,434 156,432 158,431 159,429 161,428 163,426 164,425 166,423 168,421 169,420 171,418 173,416 175,415 176,413 178,411 180,409 181,407 183,406 185,404 186,402 188,400 190,398 191,396 193,394 195,392 197,390 198,388 200,386 202,384 203,382 205,380 207,378 208,376 210,374 212,372 214,370 215,368 217,365 219,363 220,361 222,358 224,356 225,354 227,351 229,348 230,346 232,343 234,340 236,338 237,335 239,332 241,329 242,326 244,323 246,320 247,317 249,314 251,311 252,308 254,305 256,301 258,298 259,295 261,292 263,289 264,286 266,283 268,280 269,277 271,273 273,270 274,267 276,264 278,261 280,258 281,255 283,252 285,249 286,246 288,243 290,240 291,237 293,234 295,232 296,229 298,226 300,223 302,220 303,217 305,214 307,211 308,208 310,206 312,203 313,200 315,197 317,195 318,192 320,189 322,187 324,184 325,181 327,179 329,176 330,173 332,171 334,168 335,165 337,162 339,160 341,157 342,154 344,151 346,148 347,146 349,143 351,140 352,137 354,134 356,132 357,129 359,126 361,124 363,121 364,119 366,117 368,114 369,112 371,110 373,108 374,106 376,104 378,103 379,101 381,99 383,98 385,96 386,95 388,93 390,92 391,90 393,89 395,88 396,86 398,85 400,84 401,82 403,81 405,79 407,78 408,77 410,75 412,74 413,73 415,71 417,70 418,69 420,67 422,66 423,65 425,64 427,62 429,61 430,60 432,59 434,59 435,58 437,57 439,56 440,56 442,55 444,55 445,54 447,54 449,54 451,54 452,54 454,54 456,53 457,54 459,54 461,54 462,54 464,54 466,54 468,55 469,55 471,55 473,56 474,56 476,57 478,58 479,58 481,59 483,60 484,61 486,62 488,63 490,64 491,65 493,66 495,67 496,69 498,70 500,71 501,73 503,74 505,76 506,77 508,79 510,81 512,82 513,84 515,86 517,88 518,90 520,92 522,94 523,96 525,98 527,100 528,102 530,104 532,106 534,109 535,111 537,113 539,116 540,118 542,121 544,123 545,126 547,128 549,131 550,133 552,136 554,138 556,141 557,144 559,146 561,149 562,151 564,154 566,156 567,158 569,161 571,163 573,165 574,168 576,170 578,172 579,174 581,176 583,178 584,180 586,182 588,184 589,186 591,188 593,190 595,192 596,194 598,196 600,198 601,200 603,202 605,204 606,206 608,208 610,210 611,212 613,214 615,216 617,218 618,220 620,222 622,224 623,225 625,227 627,229 628,231 630,233 632,235 633,237 635,239 637,241 639,243 640,244 642,246 644,248 645,250 647,252 649,254 650,256 652,258 654,259 655,261 657,263 659,265 661,267 662,269 664,271 666,273 667,276 669,278 671,280 672,282 674,285 676,287 677,289 679,292 681,294 683,296 684,299 686,301 688,304 689,306 691,308 693,311 694,313 696,315 698,318 700,320 701,322 703,324 705,326 706,328 708,330 710,332 711,334 713,336 715,338 716,340 718,342 720,343 722,345 723,347 725,349 727,350 728,352 730,354 732,355 733,357 735,358 737,360 738,362 740,363 742,365 744,367 745,368 747,370 749,372 750,373 752,375 754,376 755,378 757,379 759,381 760,382 762,384 764,385 766,387 767,388 769,389 771,391 772,392 774,393 776,394 777,395 779,396 781,398 782,399 784,400 786,401 788,402 789,403 791,404 793,405 794,406 796,407 798,408 799,409 801,410 803,411 804,412 806,413 808,414 810,415 811,416 813,417 815,417 816,418 818,419 820,420 821,421 823,422 825,423 827,424 828,425 830,426 832,427 833,428 835,429 837,430 838,431 840,432 842,433 843,434 845,435 847,436 849,437 850,437 852,438 854,439 855,440 857,441 859,442 860,442 862,443 864,444 865,445 867,445 869,446 871,447 872,447 874,448 876,449 877,450 879,450 881,451 882,452 884,452 886,453 887,454 889,454 891,455 893,455 894,456 896,457 898,457 899,458 901,459 903,459 904,460 906,460 908,461 909,461 911,462 913,462 915,463 916,463 918,464 920,464 921,465 923,465 925,466 926,466 928,467 930,467 932,467 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,425 166,423 168,421 169,420 171,418 173,416 175,415 176,413 178,411 180,409 181,407 183,406 185,404 186,402 188,400 190,398 191,396 193,394 195,392 197,390 198,388 200,386 202,384 203,382 205,380 207,378 208,376 210,374 212,372 214,370 215,368 217,365 219,363 220,361 222,358 224,356 225,354 227,351 229,348 230,346 232,343 234,340 236,338 237,335 239,332 241,329 242,326 244,323 246,320 247,317 249,314 251,311 252,308 254,305 256,301 258,298 259,295 261,292 263,289 264,286 266,283 268,280 269,277 271,273 273,270 274,267 276,264 278,261 280,258 281,255 283,252 285,249 286,246 288,243 290,240 291,237 293,234 295,232 296,229 298,226 300,223 302,220 303,217 305,214 307,211 308,208 310,206 312,203 313,200 315,197 317,195 318,192 320,189 322,187 324,184 325,181 327,179 329,176 330,173 332,171 334,168 335,165 337,162 339,160 341,157 342,154 344,151 346,148 347,146 349,143 351,140 352,137 354,134 356,132 357,129 359,126 361,124 363,121 364,119 366,117 368,114 369,112 371,110 373,108 374,106 376,104 378,103 379,101 381,99 383,98 385,96 386,95 388,93 390,92 391,90 393,89 395,88 396,86 398,85 400,84 401,82 403,81 405,79 407,78 408,77 410,75 412,74 413,73 415,71 417,70 418,69 420,67 422,66 423,65 425,64 427,62 429,61 430,60 432,59 434,59 435,58 437,57 439,56 440,56 442,55 444,55 445,54 447,54 449,54 451,54 452,54 454,54 456,53 457,54 459,54 461,54 462,54 464,54 466,54 468,55 469,55 471,55 473,56 474,56 476,57 478,58 479,58 481,59 483,60 484,61 486,62 488,63 490,64 491,65 493,66 495,67 496,69 498,70 500,71 501,73 503,74 505,76 506,77 508,79 510,81 512,82 513,84 515,86 517,88 518,90 520,92 522,94 523,96 525,98 527,100 528,102 530,104 532,106 534,109 535,111 537,113 539,116 540,118 542,121 544,123 545,126 547,128 549,131 550,133 552,136 554,138 556,141 557,144 559,146 561,149 562,151 564,154 566,156 567,158 569,161 571,163 573,165 574,168 576,170 578,172 579,174 581,176 583,178 584,180 586,182 588,184 589,186 591,188 593,190 595,192 596,194 598,196 600,198 601,200 603,202 605,204 606,206 608,208 610,210 611,212 613,214 615,216 617,218 618,220 620,222 622,224 623,225 625,227 627,229 628,231 630,233 632,235 633,237 635,239 637,241 639,243 640,244 642,246 644,248 645,250 647,252 649,254 650,256 652,258 654,259 655,261 657,263 659,265 661,267 662,269 664,271 666,273 667,276 669,278 671,280 672,282 674,285 676,287 677,289 679,292 681,294 683,296 684,299 686,301 688,304 689,306 691,308 693,311 694,313 696,315 698,318 700,320 701,322 703,324 705,326 706,328 708,330 710,332 711,334 713,336 715,338 716,340 718,342 720,343 722,345 723,347 725,349 727,350 728,352 730,354 732,355 733,357 735,358 737,360 738,362 740,363 742,365 744,367 745,368 747,370 749,372 750,373 752,375 754,376 755,378 757,379 759,381 760,382 762,384 764,385 766,387 767,388 769,389 771,391 772,392 774,393 776,394 777,395 779,396 781,398 782,399 784,400 786,401 788,402 789,403 791,404 793,405 794,406 796,407 798,408 799,409 801,410 803,411 804,412 806,413 808,414 810,415 811,416 813,417 815,417 816,418 818,419 820,420 821,421 823,422 825,423 827,424 828,425 830,426 832,427 833,428 835,429 837,430 838,431 840,432 842,433 843,434 845,435 847,436 849,437 850,437 852,438 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="480,473 480,58 "/>
<rect x="450" y="53" width="482" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>log_formatter/format_entry/simple - Criterion.rs</title>
    <style type="text/css">
        body {
            font: 14px Helvetica Neue;
            text-rendering: optimizelegibility;
        }

        .body {
            width: 960px;
            margin: auto;
        }

        th {
            font-weight: 200
        }

        th,
        td {
            padding-right: 3px;
            padding-bottom: 3px;
        }

        a:link {
            color: #1F78B4;
            text-decoration: none;
        }

        th.ci-bound {
            opacity: 0.6
        }

        td.ci-bound {
            opacity: 0.5
        }

        .stats {
            width: 80%;
            margin: auto;
            display: flex;
        }

        .additional_stats {
            flex: 0 0 60%
        }

        .additional_plots {
            flex: 1
        }

        h2 {
            font-size: 36px;
            font-weight: 300;
        }

        h3 {
            font-size: 24px;
            font-weight: 300;
        }

        #footer {
            height: 40px;
            background: #888;
            color: white;
            font-size: larger;
            font-weight: 300;
        }

        #footer a {
            color: white;
            text-decoration: underline;
        }

        #footer p {
            text-align: center
        }
    </style>
</head>

<body>
    <div class="body">
        <h2>log_formatter/format_entry/simple</h2>
        <div class="absolute">
            <section class="plots">
                <table width="100%">
                    <tbody>
                        <tr>
                            <td>
                                <a href="pdf.svg">
                                    <img src="pdf_small.svg" alt="PDF of Slope" width="450" height="300" />
                                </a>
                            </td>
                            <td>
                                <a href="regression.svg">
                                    <img src="regression_small.svg" alt="Regression" width="450" height="300" />
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>
            <section class="stats">
                <div class="additional_stats">
                    <h4>Additional Statistics:</h4>
                    <table>
                        <thead>
                            <tr>
                                <th></th>
                                <th title="0.95 confidence level" class="ci-bound">Lower bound</th>
                                <th>Estimate</th>
                                <th title="0.95 confidence level" class="ci-bound">Upper bound</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Slope</td>
                                <td class="ci-bound">574.08 ns</td>
                                <td>575.31 ns</td>
                                <td class="ci-bound">576.85 ns</td>
                            </tr>
                            <tr>
                                <td>R&#xb2;</td>
                                <td class="ci-bound">0.9951402</td>
                                <td>0.9954702</td>
                                <td class="ci-bound">0.9949513</td>
                            </tr>
                            <tr>
                                <td>Mean</td>
                                <td class="ci-bound">574.89 ns</td>
                                <td>576.52 ns</td>
                                <td class="ci-bound">578.46 ns</td>
                            </tr>
                            <tr>
                                <td title="Standard Deviation">Std. Dev.</td>
                                <td class="ci-bound">3.6769 ns</td>
                                <td>6.5105 ns</td>
                                <td class="ci-bound">8.9866 ns</td>
                            </tr>
                            <tr>
                                <td>Median</td>
                                <td class="ci-bound">573.68 ns</td>
                                <td>574.62 ns</td>
                                <td class="ci-bound">575.73 ns</td>
                            </tr>
                            <tr>
                                <td title="Median Absolute Deviation">MAD</td>
                                <td class="ci-bound">2.0081 ns</td>
                                <td>3.0725 ns</td>
                                <td class="ci-bound">4.6125 ns</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="additional_plots">
                    <h4>Additional Plots:</h4>
                    <ul>
                        
                        <li>
                            <a href="typical.svg">Typical</a>
                        </li>
                        <li>
                            <a href="mean.svg">Mean</a>
                        </li>
                        <li>
                            <a href="SD.svg">Std. Dev.</a>
                        </li>
                        <li>
                            <a href="median.svg">Median</a>
                        </li>
                        <li>
                            <a href="MAD.svg">MAD</a>
                        </li>
                        <li>
                            <a href="slope.svg">Slope</a>
                        </li>
                    </ul>
                </div>
            </section>
            <section class="explanation">
                <h4>Understanding this report:</h4>
                <p>The plot on the left displays the average time per iteration for this benchmark. The shaded region
                    shows the estimated probability of an iteration taking a certain amount of time, while the line
                    shows the mean. Click on the plot for a larger view showing the outliers.</p>
                <p>The plot on the right shows the linear regression calculated from the measurements. Each point
                    represents a sample, though here it shows the total time for the sample rather than time per
                    iteration. The line is the line of best fit for these measurements.</p>
                <p>See <a href="https://bheisler.github.io/criterion.rs/book/user_guide/command_line_output.html#additional-statistics">the
                        documentation</a> for more details on the additional statistics.</p>
            </section>
        </div>
        <section class="plots">
            <h3>Change Since Previous Benchmark</h3>
            <div class="relative">
                <table width="100%">
                    <tbody>
                        <tr>
                            <td>
                                <a href="both/pdf.svg">
                                    <img src="relative_pdf_small.svg" alt="PDF Comparison" width="450"
                                        height="300" />
                                </a>
                            </td>
                            <td>
                                <a href="both/regression.svg">
                                    <img src="relative_regression_small.svg" alt="Regression Comparison" width="450"
                                        height="300" />
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
        <section class="stats">
            <div class="additional_stats">
                <h4>Additional Statistics:</h4>
                <table>
                    <thead>
                        <tr>
                            <th></th>
                            <th title="0.95 confidence level" class="ci-bound">Lower bound</th>
                            <th>Estimate</th>
                            <th title="0.95 confidence level" class="ci-bound">Upper bound</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Change in time</td>
                            <td class="ci-bound">-2.8808%</td>
                            <td>-2.5477%</td>
                            <td class="ci-bound">-2.1598%</td>
                            <td>(p = 0.00 &lt;
                                0.05)</td>
                        </tr>
                    </tbody>
                </table>
                Performance has improved.
            </div>
            <div class="additional_plots">
                <h4>Additional Plots:</h4>
                <ul>
                    
                    <li>
                        <a href="change/mean.svg">Change in mean</a>
                    </li>
                    <li>
                        <a href="change/median.svg">Change in median</a>
                    </li>
                    <li>
                        <a href="change/t-test.svg">T-Test</a>
                    </li>
                </ul>
            </div>
        </section>
        <section class="explanation">
            <h4>Understanding this report:</h4>
            <p>The plot on the left shows the probability of the function taking a certain amount of time. The red
                curve represents the saved measurements from the last time this benchmark was run, while the blue curve
                shows the measurements from this run. The lines represent the mean time per iteration. Click on the
                plot for a larger view.</p>
            <p>The plot on the right shows the two regressions. Again, the red line represents the previous measurement
                while the blue line shows the current measurement.</p>
            <p>See <a href="https://bheisler.github.io/criterion.rs/book/user_guide/command_line_output.html#change">the
                    documentation</a> for more details on the additional statistics.</p>
        </section>
    </div>
    <div id="footer">
        <p>This report was generated by
            <a href="https://github.com/bheisler/criterion.rs">Criterion.rs</a>, a statistics-driven benchmarking
            library in Rust.</p>
    </div>
</body>

</html>
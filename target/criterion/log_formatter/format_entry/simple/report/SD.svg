<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="428" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,428 86,428 "/>
<text x="77" y="360" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,360 86,360 "/>
<text x="77" y="293" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,293 86,293 "/>
<text x="77" y="225" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,225 86,225 "/>
<text x="77" y="157" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,157 86,157 "/>
<text x="77" y="90" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,90 86,90 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="205" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="205,473 205,478 "/>
<text x="336" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="336,473 336,478 "/>
<text x="466" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="466,473 466,478 "/>
<text x="596" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="596,473 596,478 "/>
<text x="726" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="726,473 726,478 "/>
<text x="856" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="856,473 856,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,469 95,468 97,467 98,466 100,465 102,464 103,463 105,462 107,461 109,460 110,459 112,458 114,457 115,455 117,454 119,453 120,452 122,451 124,449 125,448 127,447 129,446 131,444 132,443 134,441 136,440 137,439 139,437 141,436 142,434 144,433 146,431 147,430 149,428 151,427 153,425 154,424 156,422 158,421 159,419 161,418 163,416 164,414 166,413 168,411 169,409 171,408 173,406 175,405 176,403 178,401 180,399 181,398 183,396 185,394 186,392 188,391 190,389 191,387 193,385 195,383 197,381 198,379 200,377 202,375 203,373 205,371 207,369 208,367 210,365 212,363 214,361 215,359 217,356 219,354 220,352 222,350 224,347 225,345 227,343 229,340 230,338 232,336 234,333 236,331 237,329 239,326 241,324 242,322 244,320 246,317 247,315 249,313 251,311 252,309 254,307 256,305 258,303 259,301 261,299 263,298 264,296 266,295 268,293 269,292 271,290 273,289 274,288 276,287 278,286 280,285 281,284 283,283 285,282 286,281 288,280 290,280 291,279 293,278 295,277 296,277 298,276 300,275 302,274 303,274 305,273 307,272 308,271 310,271 312,270 313,269 315,268 317,268 318,267 320,266 322,265 324,265 325,264 327,263 329,262 330,261 332,261 334,260 335,259 337,258 339,258 341,257 342,256 344,255 346,254 347,253 349,252 351,251 352,250 354,249 356,248 357,247 359,246 361,245 363,244 364,243 366,241 368,240 369,239 371,237 373,236 374,234 376,233 378,231 379,230 381,228 383,226 385,225 386,223 388,221 390,219 391,217 393,215 395,213 396,211 398,209 400,207 401,205 403,203 405,200 407,198 408,196 410,193 412,191 413,188 415,186 417,183 418,180 420,178 422,175 423,173 425,170 427,167 429,165 430,162 432,160 434,157 435,155 437,153 439,150 440,148 442,146 444,144 445,142 447,140 449,137 451,135 452,133 454,131 456,130 457,128 459,126 461,124 462,122 464,120 466,118 468,116 469,115 471,113 473,111 474,109 476,108 478,106 479,104 481,103 483,102 484,100 486,99 488,98 490,97 491,96 493,95 495,95 496,94 498,94 500,94 501,94 503,94 505,94 506,95 508,96 510,97 512,98 513,99 515,101 517,102 518,104 520,106 522,108 523,110 525,113 527,115 528,117 530,120 532,122 534,124 535,127 537,129 539,132 540,134 542,136 544,138 545,140 547,142 549,144 550,146 552,148 554,149 556,151 557,152 559,153 561,155 562,156 564,157 566,158 567,159 569,160 571,161 573,162 574,162 576,163 578,164 579,164 581,165 583,166 584,166 586,167 588,168 589,168 591,169 593,169 595,170 596,171 598,171 600,172 601,172 603,173 605,173 606,174 608,174 610,175 611,175 613,176 615,176 617,177 618,178 620,178 622,179 623,179 625,180 627,181 628,181 630,182 632,183 633,184 635,185 637,186 639,187 640,188 642,190 644,191 645,193 647,194 649,196 650,198 652,200 654,202 655,204 657,207 659,209 661,212 662,214 664,217 666,220 667,222 669,225 671,228 672,231 674,234 676,236 677,239 679,242 681,245 683,248 684,251 686,253 688,256 689,259 691,261 693,264 694,267 696,269 698,272 700,275 701,277 703,280 705,282 706,285 708,287 710,290 711,292 713,295 715,297 716,299 718,302 720,304 722,306 723,309 725,311 727,313 728,315 730,317 732,319 733,321 735,323 737,325 738,326 740,328 742,330 744,331 745,333 747,335 749,336 750,337 752,339 754,340 755,342 757,343 759,344 760,346 762,347 764,348 766,350 767,351 769,352 771,354 772,355 774,357 776,358 777,360 779,361 781,363 782,364 784,366 786,368 788,369 789,371 791,373 793,375 794,376 796,378 798,380 799,382 801,384 803,385 804,387 806,389 808,391 810,393 811,395 813,397 815,398 816,400 818,402 820,404 821,406 823,407 825,409 827,411 828,412 830,414 832,416 833,417 835,419 837,420 838,422 840,423 842,425 843,426 845,427 847,429 849,430 850,431 852,432 854,434 855,435 857,436 859,437 860,438 862,439 864,440 865,441 867,442 869,443 871,444 872,445 874,446 876,447 877,448 879,449 881,450 882,450 884,451 886,452 887,453 889,453 891,454 893,455 894,456 896,456 898,457 899,458 901,458 903,459 904,460 906,460 908,461 909,462 911,463 913,463 915,464 916,465 918,465 920,466 921,467 923,467 925,468 926,469 928,470 930,470 932,471 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,414 166,413 168,411 169,409 171,408 173,406 175,405 176,403 178,401 180,399 181,398 183,396 185,394 186,392 188,391 190,389 191,387 193,385 195,383 197,381 198,379 200,377 202,375 203,373 205,371 207,369 208,367 210,365 212,363 214,361 215,359 217,356 219,354 220,352 222,350 224,347 225,345 227,343 229,340 230,338 232,336 234,333 236,331 237,329 239,326 241,324 242,322 244,320 246,317 247,315 249,313 251,311 252,309 254,307 256,305 258,303 259,301 261,299 263,298 264,296 266,295 268,293 269,292 271,290 273,289 274,288 276,287 278,286 280,285 281,284 283,283 285,282 286,281 288,280 290,280 291,279 293,278 295,277 296,277 298,276 300,275 302,274 303,274 305,273 307,272 308,271 310,271 312,270 313,269 315,268 317,268 318,267 320,266 322,265 324,265 325,264 327,263 329,262 330,261 332,261 334,260 335,259 337,258 339,258 341,257 342,256 344,255 346,254 347,253 349,252 351,251 352,250 354,249 356,248 357,247 359,246 361,245 363,244 364,243 366,241 368,240 369,239 371,237 373,236 374,234 376,233 378,231 379,230 381,228 383,226 385,225 386,223 388,221 390,219 391,217 393,215 395,213 396,211 398,209 400,207 401,205 403,203 405,200 407,198 408,196 410,193 412,191 413,188 415,186 417,183 418,180 420,178 422,175 423,173 425,170 427,167 429,165 430,162 432,160 434,157 435,155 437,153 439,150 440,148 442,146 444,144 445,142 447,140 449,137 451,135 452,133 454,131 456,130 457,128 459,126 461,124 462,122 464,120 466,118 468,116 469,115 471,113 473,111 474,109 476,108 478,106 479,104 481,103 483,102 484,100 486,99 488,98 490,97 491,96 493,95 495,95 496,94 498,94 500,94 501,94 503,94 505,94 506,95 508,96 510,97 512,98 513,99 515,101 517,102 518,104 520,106 522,108 523,110 525,113 527,115 528,117 530,120 532,122 534,124 535,127 537,129 539,132 540,134 542,136 544,138 545,140 547,142 549,144 550,146 552,148 554,149 556,151 557,152 559,153 561,155 562,156 564,157 566,158 567,159 569,160 571,161 573,162 574,162 576,163 578,164 579,164 581,165 583,166 584,166 586,167 588,168 589,168 591,169 593,169 595,170 596,171 598,171 600,172 601,172 603,173 605,173 606,174 608,174 610,175 611,175 613,176 615,176 617,177 618,178 620,178 622,179 623,179 625,180 627,181 628,181 630,182 632,183 633,184 635,185 637,186 639,187 640,188 642,190 644,191 645,193 647,194 649,196 650,198 652,200 654,202 655,204 657,207 659,209 661,212 662,214 664,217 666,220 667,222 669,225 671,228 672,231 674,234 676,236 677,239 679,242 681,245 683,248 684,251 686,253 688,256 689,259 691,261 693,264 694,267 696,269 698,272 700,275 701,277 703,280 705,282 706,285 708,287 710,290 711,292 713,295 715,297 716,299 718,302 720,304 722,306 723,309 725,311 727,313 728,315 730,317 732,319 733,321 735,323 737,325 738,326 740,328 742,330 744,331 745,333 747,335 749,336 750,337 752,339 754,340 755,342 757,343 759,344 760,346 762,347 764,348 766,350 767,351 769,352 771,354 772,355 774,357 776,358 777,360 779,361 781,363 782,364 784,366 786,368 788,369 789,371 791,373 793,375 794,376 796,378 798,380 799,382 801,384 803,385 804,387 806,389 808,391 810,393 811,395 813,397 815,398 816,400 818,402 820,404 821,406 823,407 825,409 827,411 828,412 830,414 832,416 833,417 835,419 837,420 838,422 840,423 842,425 843,426 845,427 847,429 849,430 850,431 852,432 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="532,473 532,123 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="434" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,434 86,434 "/>
<text x="77" y="382" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,382 86,382 "/>
<text x="77" y="330" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,330 86,330 "/>
<text x="77" y="279" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,279 86,279 "/>
<text x="77" y="227" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,227 86,227 "/>
<text x="77" y="175" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,175 86,175 "/>
<text x="77" y="123" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,123 86,123 "/>
<text x="77" y="71" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,71 86,71 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="161" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="161,473 161,478 "/>
<text x="294" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
2.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="294,473 294,478 "/>
<text x="427" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="427,473 427,478 "/>
<text x="559" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
3.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="559,473 559,478 "/>
<text x="692" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="692,473 692,478 "/>
<text x="825" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
4.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="825,473 825,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,471 93,470 95,470 97,469 98,469 100,468 102,467 103,466 105,465 107,465 109,464 110,463 112,462 114,460 115,459 117,458 119,457 120,455 122,454 124,452 125,451 127,449 129,448 131,446 132,445 134,443 136,441 137,439 139,438 141,436 142,434 144,433 146,431 147,430 149,428 151,426 153,425 154,423 156,422 158,420 159,419 161,418 163,416 164,415 166,413 168,412 169,410 171,408 173,407 175,405 176,403 178,401 180,399 181,397 183,394 185,392 186,390 188,387 190,384 191,381 193,378 195,375 197,372 198,369 200,366 202,362 203,359 205,355 207,352 208,348 210,344 212,340 214,337 215,333 217,329 219,325 220,321 222,317 224,313 225,309 227,305 229,301 230,297 232,293 234,289 236,285 237,281 239,278 241,274 242,270 244,266 246,262 247,258 249,254 251,250 252,246 254,242 256,238 258,234 259,230 261,226 263,222 264,218 266,213 268,209 269,204 271,199 273,195 274,189 276,184 278,179 280,173 281,168 283,162 285,156 286,150 288,145 290,139 291,133 293,127 295,122 296,117 298,112 300,107 302,104 303,100 305,97 307,95 308,94 310,93 312,93 313,93 315,95 317,96 318,99 320,102 322,105 324,109 325,113 327,118 329,123 330,127 332,132 334,137 335,142 337,146 339,150 341,154 342,158 344,162 346,165 347,168 349,170 351,173 352,175 354,176 356,178 357,179 359,181 361,182 363,183 364,185 366,186 368,187 369,189 371,191 373,192 374,194 376,197 378,199 379,201 381,204 383,206 385,209 386,212 388,214 390,217 391,220 393,222 395,225 396,227 398,229 400,231 401,232 403,234 405,235 407,235 408,236 410,236 412,235 413,235 415,234 417,233 418,231 420,229 422,228 423,226 425,223 427,221 429,219 430,217 432,215 434,213 435,211 437,209 439,208 440,206 442,205 444,205 445,205 447,205 449,205 451,206 452,207 454,208 456,210 457,211 459,214 461,216 462,218 464,221 466,224 468,227 469,230 471,232 473,235 474,238 476,241 478,244 479,247 481,250 483,253 484,256 486,258 488,261 490,263 491,266 493,268 495,271 496,273 498,275 500,277 501,279 503,281 505,283 506,285 508,287 510,288 512,290 513,292 515,293 517,295 518,296 520,298 522,299 523,300 525,301 527,302 528,303 530,303 532,304 534,304 535,304 537,304 539,304 540,304 542,303 544,302 545,301 547,300 549,299 550,297 552,296 554,294 556,292 557,291 559,289 561,287 562,286 564,284 566,283 567,281 569,280 571,279 573,278 574,277 576,276 578,276 579,275 581,275 583,275 584,275 586,275 588,275 589,275 591,276 593,276 595,277 596,277 598,278 600,279 601,280 603,281 605,282 606,283 608,285 610,286 611,288 613,290 615,292 617,294 618,296 620,298 622,300 623,302 625,304 627,306 628,309 630,311 632,313 633,315 635,317 637,319 639,320 640,322 642,324 644,326 645,327 647,329 649,331 650,332 652,334 654,336 655,338 657,339 659,341 661,343 662,345 664,347 666,349 667,351 669,353 671,355 672,358 674,360 676,362 677,364 679,366 681,368 683,370 684,372 686,374 688,376 689,377 691,379 693,380 694,381 696,382 698,383 700,384 701,385 703,385 705,386 706,387 708,387 710,388 711,388 713,389 715,390 716,390 718,391 720,392 722,393 723,394 725,395 727,396 728,397 730,398 732,399 733,400 735,401 737,403 738,404 740,405 742,406 744,408 745,409 747,410 749,411 750,413 752,414 754,415 755,416 757,417 759,418 760,419 762,420 764,421 766,421 767,422 769,423 771,424 772,425 774,425 776,426 777,426 779,427 781,428 782,428 784,429 786,429 788,429 789,430 791,430 793,430 794,431 796,431 798,431 799,431 801,432 803,432 804,432 806,432 808,432 810,433 811,433 813,433 815,433 816,433 818,434 820,434 821,434 823,434 825,434 827,435 828,435 830,435 832,436 833,436 835,436 837,436 838,437 840,437 842,437 843,438 845,438 847,439 849,439 850,440 852,440 854,441 855,441 857,442 859,443 860,444 862,444 864,445 865,446 867,447 869,448 871,449 872,450 874,451 876,452 877,452 879,453 881,454 882,455 884,456 886,456 887,457 889,458 891,458 893,459 894,460 896,460 898,461 899,461 901,461 903,462 904,462 906,463 908,463 909,464 911,464 913,464 915,465 916,465 918,466 920,466 921,467 923,467 925,467 926,468 928,468 930,469 932,469 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,415 166,413 168,412 169,410 171,408 173,407 175,405 176,403 178,401 180,399 181,397 183,394 185,392 186,390 188,387 190,384 191,381 193,378 195,375 197,372 198,369 200,366 202,362 203,359 205,355 207,352 208,348 210,344 212,340 214,337 215,333 217,329 219,325 220,321 222,317 224,313 225,309 227,305 229,301 230,297 232,293 234,289 236,285 237,281 239,278 241,274 242,270 244,266 246,262 247,258 249,254 251,250 252,246 254,242 256,238 258,234 259,230 261,226 263,222 264,218 266,213 268,209 269,204 271,199 273,195 274,189 276,184 278,179 280,173 281,168 283,162 285,156 286,150 288,145 290,139 291,133 293,127 295,122 296,117 298,112 300,107 302,104 303,100 305,97 307,95 308,94 310,93 312,93 313,93 315,95 317,96 318,99 320,102 322,105 324,109 325,113 327,118 329,123 330,127 332,132 334,137 335,142 337,146 339,150 341,154 342,158 344,162 346,165 347,168 349,170 351,173 352,175 354,176 356,178 357,179 359,181 361,182 363,183 364,185 366,186 368,187 369,189 371,191 373,192 374,194 376,197 378,199 379,201 381,204 383,206 385,209 386,212 388,214 390,217 391,220 393,222 395,225 396,227 398,229 400,231 401,232 403,234 405,235 407,235 408,236 410,236 412,235 413,235 415,234 417,233 418,231 420,229 422,228 423,226 425,223 427,221 429,219 430,217 432,215 434,213 435,211 437,209 439,208 440,206 442,205 444,205 445,205 447,205 449,205 451,206 452,207 454,208 456,210 457,211 459,214 461,216 462,218 464,221 466,224 468,227 469,230 471,232 473,235 474,238 476,241 478,244 479,247 481,250 483,253 484,256 486,258 488,261 490,263 491,266 493,268 495,271 496,273 498,275 500,277 501,279 503,281 505,283 506,285 508,287 510,288 512,290 513,292 515,293 517,295 518,296 520,298 522,299 523,300 525,301 527,302 528,303 530,303 532,304 534,304 535,304 537,304 539,304 540,304 542,303 544,302 545,301 547,300 549,299 550,297 552,296 554,294 556,292 557,291 559,289 561,287 562,286 564,284 566,283 567,281 569,280 571,279 573,278 574,277 576,276 578,276 579,275 581,275 583,275 584,275 586,275 588,275 589,275 591,276 593,276 595,277 596,277 598,278 600,279 601,280 603,281 605,282 606,283 608,285 610,286 611,288 613,290 615,292 617,294 618,296 620,298 622,300 623,302 625,304 627,306 628,309 630,311 632,313 633,315 635,317 637,319 639,320 640,322 642,324 644,326 645,327 647,329 649,331 650,332 652,334 654,336 655,338 657,339 659,341 661,343 662,345 664,347 666,349 667,351 669,353 671,355 672,358 674,360 676,362 677,364 679,366 681,368 683,370 684,372 686,374 688,376 689,377 691,379 693,380 694,381 696,382 698,383 700,384 701,385 703,385 705,386 706,387 708,387 710,388 711,388 713,389 715,390 716,390 718,391 720,392 722,393 723,394 725,395 727,396 728,397 730,398 732,399 733,400 735,401 737,403 738,404 740,405 742,406 744,408 745,409 747,410 749,411 750,413 752,414 754,415 755,416 757,417 759,418 760,419 762,420 764,421 766,421 767,422 769,423 771,424 772,425 774,425 776,426 777,426 779,427 781,428 782,428 784,429 786,429 788,429 789,430 791,430 793,430 794,431 796,431 798,431 799,431 801,432 803,432 804,432 806,432 808,432 810,433 811,433 813,433 815,433 816,433 818,434 820,434 821,434 823,434 825,434 827,435 828,435 830,435 832,436 833,436 835,436 837,436 838,437 840,437 842,437 843,438 845,438 847,439 849,439 850,440 852,440 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="446,473 446,205 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="427" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,427 86,427 "/>
<text x="77" y="363" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,363 86,363 "/>
<text x="77" y="298" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,298 86,298 "/>
<text x="77" y="233" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,233 86,233 "/>
<text x="77" y="168" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
250
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,168 86,168 "/>
<text x="77" y="104" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
300
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,104 86,104 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="153" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.029
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="153,473 153,478 "/>
<text x="233" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.0285
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="233,473 233,478 "/>
<text x="313" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.028
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="313,473 313,478 "/>
<text x="394" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.0275
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="394,473 394,478 "/>
<text x="474" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.027
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="474,473 474,478 "/>
<text x="554" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.0265
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="554,473 554,478 "/>
<text x="635" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.026
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="635,473 635,478 "/>
<text x="715" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.0255
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="715,473 715,478 "/>
<text x="795" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="795,473 795,478 "/>
<text x="876" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.0245
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="876,473 876,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,469 95,468 97,467 98,465 100,464 102,463 103,462 105,461 107,459 109,458 110,457 112,455 114,454 115,453 117,451 119,450 120,448 122,447 124,445 125,444 127,442 129,440 131,439 132,437 134,435 136,433 137,431 139,429 141,427 142,425 144,423 146,421 147,419 149,416 151,414 153,411 154,409 156,406 158,404 159,401 161,399 163,396 164,393 166,391 168,388 169,385 171,382 173,380 175,377 176,374 178,371 180,369 181,366 183,363 185,360 186,357 188,355 190,352 191,349 193,346 195,343 197,340 198,338 200,335 202,332 203,329 205,326 207,324 208,321 210,318 212,315 214,313 215,310 217,307 219,304 220,301 222,299 224,296 225,293 227,290 229,287 230,284 232,281 234,278 236,275 237,272 239,269 241,265 242,262 244,259 246,255 247,252 249,248 251,244 252,240 254,237 256,233 258,229 259,225 261,221 263,216 264,212 266,208 268,203 269,199 271,194 273,190 274,185 276,181 278,176 280,172 281,168 283,163 285,159 286,155 288,151 290,148 291,144 293,141 295,137 296,134 298,132 300,129 302,127 303,125 305,123 307,121 308,120 310,118 312,117 313,116 315,116 317,115 318,114 320,114 322,113 324,113 325,113 327,112 329,112 330,112 332,112 334,112 335,112 337,112 339,112 341,112 342,112 344,113 346,113 347,114 349,115 351,115 352,117 354,118 356,119 357,120 359,122 361,123 363,125 364,127 366,129 368,131 369,133 371,135 373,137 374,139 376,141 378,143 379,145 381,146 383,148 385,150 386,152 388,154 390,156 391,157 393,159 395,160 396,162 398,163 400,164 401,165 403,166 405,167 407,168 408,169 410,169 412,169 413,169 415,169 417,169 418,168 420,168 422,166 423,165 425,164 427,162 429,159 430,157 432,154 434,151 435,148 437,144 439,141 440,137 442,132 444,128 445,124 447,120 449,115 451,111 452,107 454,103 456,99 457,95 459,91 461,88 462,85 464,82 466,79 468,77 469,74 471,72 473,70 474,69 476,67 478,66 479,64 481,63 483,62 484,61 486,60 488,59 490,58 491,57 493,56 495,56 496,55 498,55 500,54 501,54 503,54 505,54 506,53 508,54 510,54 512,54 513,55 515,55 517,56 518,57 520,58 522,59 523,60 525,62 527,63 528,65 530,67 532,68 534,70 535,73 537,75 539,77 540,80 542,82 544,85 545,88 547,90 549,93 550,96 552,99 554,102 556,105 557,108 559,111 561,114 562,117 564,121 566,124 567,127 569,130 571,133 573,137 574,140 576,143 578,146 579,149 581,152 583,155 584,158 586,160 588,163 589,165 591,167 593,169 595,171 596,172 598,173 600,174 601,175 603,176 605,176 606,176 608,175 610,175 611,174 613,173 615,172 617,171 618,169 620,168 622,166 623,164 625,163 627,161 628,159 630,158 632,156 633,155 635,154 637,153 639,152 640,152 642,151 644,152 645,152 647,152 649,153 650,154 652,156 654,157 655,159 657,161 659,163 661,165 662,167 664,169 666,172 667,174 669,177 671,180 672,182 674,185 676,188 677,191 679,194 681,197 683,201 684,204 686,208 688,211 689,215 691,219 693,223 694,227 696,231 698,235 700,239 701,243 703,247 705,251 706,255 708,259 710,263 711,267 713,270 715,274 716,277 718,281 720,284 722,287 723,290 725,293 727,296 728,299 730,302 732,305 733,308 735,310 737,313 738,316 740,319 742,321 744,324 745,327 747,330 749,332 750,335 752,337 754,340 755,342 757,344 759,346 760,348 762,350 764,352 766,353 767,355 769,356 771,357 772,359 774,360 776,361 777,362 779,362 781,363 782,364 784,364 786,365 788,366 789,366 791,367 793,368 794,368 796,369 798,370 799,370 801,371 803,372 804,373 806,374 808,375 810,376 811,377 813,378 815,379 816,380 818,381 820,382 821,383 823,384 825,385 827,387 828,388 830,389 832,391 833,392 835,393 837,395 838,396 840,398 842,400 843,401 845,403 847,405 849,407 850,408 852,410 854,412 855,414 857,416 859,418 860,419 862,421 864,423 865,425 867,427 869,428 871,430 872,431 874,433 876,435 877,436 879,437 881,439 882,440 884,442 886,443 887,444 889,446 891,447 893,448 894,449 896,450 898,452 899,453 901,454 903,455 904,456 906,457 908,458 909,459 911,460 913,460 915,461 916,462 918,463 920,463 921,464 923,465 925,465 926,466 928,466 930,467 932,467 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,393 166,391 168,388 169,385 171,382 173,380 175,377 176,374 178,371 180,369 181,366 183,363 185,360 186,357 188,355 190,352 191,349 193,346 195,343 197,340 198,338 200,335 202,332 203,329 205,326 207,324 208,321 210,318 212,315 214,313 215,310 217,307 219,304 220,301 222,299 224,296 225,293 227,290 229,287 230,284 232,281 234,278 236,275 237,272 239,269 241,265 242,262 244,259 246,255 247,252 249,248 251,244 252,240 254,237 256,233 258,229 259,225 261,221 263,216 264,212 266,208 268,203 269,199 271,194 273,190 274,185 276,181 278,176 280,172 281,168 283,163 285,159 286,155 288,151 290,148 291,144 293,141 295,137 296,134 298,132 300,129 302,127 303,125 305,123 307,121 308,120 310,118 312,117 313,116 315,116 317,115 318,114 320,114 322,113 324,113 325,113 327,112 329,112 330,112 332,112 334,112 335,112 337,112 339,112 341,112 342,112 344,113 346,113 347,114 349,115 351,115 352,117 354,118 356,119 357,120 359,122 361,123 363,125 364,127 366,129 368,131 369,133 371,135 373,137 374,139 376,141 378,143 379,145 381,146 383,148 385,150 386,152 388,154 390,156 391,157 393,159 395,160 396,162 398,163 400,164 401,165 403,166 405,167 407,168 408,169 410,169 412,169 413,169 415,169 417,169 418,168 420,168 422,166 423,165 425,164 427,162 429,159 430,157 432,154 434,151 435,148 437,144 439,141 440,137 442,132 444,128 445,124 447,120 449,115 451,111 452,107 454,103 456,99 457,95 459,91 461,88 462,85 464,82 466,79 468,77 469,74 471,72 473,70 474,69 476,67 478,66 479,64 481,63 483,62 484,61 486,60 488,59 490,58 491,57 493,56 495,56 496,55 498,55 500,54 501,54 503,54 505,54 506,53 508,54 510,54 512,54 513,55 515,55 517,56 518,57 520,58 522,59 523,60 525,62 527,63 528,65 530,67 532,68 534,70 535,73 537,75 539,77 540,80 542,82 544,85 545,88 547,90 549,93 550,96 552,99 554,102 556,105 557,108 559,111 561,114 562,117 564,121 566,124 567,127 569,130 571,133 573,137 574,140 576,143 578,146 579,149 581,152 583,155 584,158 586,160 588,163 589,165 591,167 593,169 595,171 596,172 598,173 600,174 601,175 603,176 605,176 606,176 608,175 610,175 611,174 613,173 615,172 617,171 618,169 620,168 622,166 623,164 625,163 627,161 628,159 630,158 632,156 633,155 635,154 637,153 639,152 640,152 642,151 644,152 645,152 647,152 649,153 650,154 652,156 654,157 655,159 657,161 659,163 661,165 662,167 664,169 666,172 667,174 669,177 671,180 672,182 674,185 676,188 677,191 679,194 681,197 683,201 684,204 686,208 688,211 689,215 691,219 693,223 694,227 696,231 698,235 700,239 701,243 703,247 705,251 706,255 708,259 710,263 711,267 713,270 715,274 716,277 718,281 720,284 722,287 723,290 725,293 727,296 728,299 730,302 732,305 733,308 735,310 737,313 738,316 740,319 742,321 744,324 745,327 747,330 749,332 750,335 752,337 754,340 755,342 757,344 759,346 760,348 762,350 764,352 766,353 767,355 769,356 771,357 772,359 774,360 776,361 777,362 779,362 781,363 782,364 784,364 786,365 788,366 789,366 791,367 793,368 794,368 796,369 798,370 799,370 801,371 803,372 804,373 806,374 808,375 810,376 811,377 813,378 815,379 816,380 818,381 820,382 821,383 823,384 825,385 827,387 828,388 830,389 832,391 833,392 835,393 837,395 838,396 840,398 842,400 843,401 845,403 847,405 849,407 850,408 852,410 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="457,473 457,95 "/>
<rect x="509" y="53" width="0" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

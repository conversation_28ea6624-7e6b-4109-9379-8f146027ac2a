<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Relative change (%)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="456" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
20
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,456 86,456 "/>
<text x="77" y="412" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
40
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,412 86,412 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
60
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="324" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
80
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,324 86,324 "/>
<text x="77" y="280" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,280 86,280 "/>
<text x="77" y="236" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
120
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,236 86,236 "/>
<text x="77" y="191" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
140
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,191 86,191 "/>
<text x="77" y="147" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
160
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,147 86,147 "/>
<text x="77" y="103" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
180
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,103 86,103 "/>
<text x="77" y="59" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,59 86,59 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="145" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.029
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="145,473 145,478 "/>
<text x="241" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.028
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="241,473 241,478 "/>
<text x="337" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.027
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="337,473 337,478 "/>
<text x="433" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.026
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="433,473 433,478 "/>
<text x="528" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="528,473 528,478 "/>
<text x="624" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.024
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="624,473 624,478 "/>
<text x="720" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.023
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="720,473 720,478 "/>
<text x="816" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.022
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="816,473 816,478 "/>
<text x="912" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
-0.021
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="912,473 912,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,469 95,468 97,467 98,466 100,465 102,464 103,463 105,462 107,461 109,460 110,459 112,458 114,457 115,456 117,455 119,454 120,453 122,452 124,451 125,450 127,449 129,448 131,447 132,446 134,445 136,443 137,442 139,441 141,440 142,439 144,438 146,436 147,435 149,434 151,433 153,431 154,430 156,429 158,427 159,426 161,424 163,423 164,422 166,420 168,418 169,417 171,415 173,414 175,412 176,410 178,408 180,407 181,405 183,403 185,401 186,399 188,397 190,395 191,393 193,391 195,389 197,386 198,384 200,382 202,379 203,377 205,375 207,372 208,369 210,367 212,364 214,362 215,359 217,356 219,353 220,351 222,348 224,345 225,342 227,340 229,337 230,334 232,331 234,329 236,326 237,323 239,320 241,318 242,315 244,312 246,309 247,307 249,304 251,301 252,299 254,296 256,293 258,291 259,288 261,285 263,283 264,280 266,277 268,275 269,272 271,269 273,266 274,264 276,261 278,258 280,255 281,253 283,250 285,247 286,244 288,242 290,239 291,236 293,234 295,231 296,228 298,226 300,223 302,221 303,219 305,216 307,214 308,211 310,209 312,207 313,205 315,202 317,200 318,198 320,196 322,194 324,192 325,189 327,187 329,185 330,183 332,181 334,179 335,177 337,175 339,173 341,171 342,168 344,166 346,164 347,162 349,160 351,158 352,156 354,153 356,151 357,149 359,147 361,144 363,142 364,140 366,138 368,136 369,133 371,131 373,129 374,127 376,125 378,123 379,121 381,119 383,117 385,115 386,113 388,111 390,110 391,108 393,106 395,105 396,103 398,101 400,100 401,98 403,97 405,95 407,94 408,93 410,91 412,90 413,89 415,87 417,86 418,85 420,84 422,82 423,81 425,80 427,78 429,77 430,76 432,75 434,73 435,72 437,71 439,69 440,68 442,67 444,66 445,65 447,63 449,62 451,61 452,60 454,59 456,59 457,58 459,57 461,56 462,56 464,55 466,55 468,54 469,54 471,54 473,54 474,54 476,53 478,54 479,54 481,54 483,54 484,54 486,54 488,55 490,55 491,55 493,55 495,55 496,56 498,56 500,56 501,56 503,56 505,56 506,56 508,56 510,56 512,56 513,56 515,56 517,56 518,56 520,56 522,56 523,55 525,55 527,55 528,55 530,55 532,55 534,55 535,55 537,55 539,55 540,55 542,55 544,55 545,56 547,56 549,56 550,56 552,57 554,57 556,58 557,58 559,59 561,59 562,60 564,61 566,62 567,63 569,64 571,65 573,66 574,67 576,68 578,70 579,71 581,73 583,74 584,76 586,77 588,79 589,81 591,83 593,84 595,86 596,88 598,90 600,93 601,95 603,97 605,99 606,102 608,104 610,107 611,110 613,112 615,115 617,118 618,121 620,124 622,127 623,130 625,133 627,136 628,139 630,142 632,145 633,148 635,151 637,155 639,158 640,161 642,164 644,167 645,170 647,173 649,176 650,179 652,182 654,185 655,187 657,190 659,193 661,196 662,198 664,201 666,204 667,206 669,209 671,211 672,214 674,216 676,219 677,221 679,223 681,226 683,228 684,230 686,233 688,235 689,237 691,239 693,242 694,244 696,246 698,249 700,251 701,253 703,255 705,258 706,260 708,263 710,265 711,267 713,270 715,272 716,275 718,277 720,280 722,282 723,285 725,287 727,290 728,292 730,295 732,297 733,300 735,302 737,304 738,307 740,309 742,311 744,314 745,316 747,318 749,320 750,322 752,324 754,326 755,328 757,330 759,332 760,333 762,335 764,337 766,339 767,341 769,342 771,344 772,346 774,348 776,350 777,351 779,353 781,355 782,356 784,358 786,360 788,362 789,363 791,365 793,367 794,368 796,370 798,372 799,374 801,376 803,377 804,379 806,381 808,383 810,384 811,386 813,388 815,390 816,391 818,393 820,395 821,396 823,398 825,400 827,401 828,403 830,404 832,406 833,407 835,409 837,411 838,412 840,414 842,415 843,417 845,418 847,419 849,421 850,422 852,424 854,425 855,427 857,428 859,430 860,431 862,432 864,434 865,435 867,436 869,438 871,439 872,440 874,442 876,443 877,444 879,445 881,446 882,447 884,449 886,450 887,451 889,452 891,453 893,454 894,455 896,456 898,457 899,457 901,458 903,459 904,460 906,461 908,462 909,463 911,463 913,464 915,465 916,466 918,466 920,467 921,468 923,469 925,469 926,470 928,471 930,471 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,422 166,420 168,418 169,417 171,415 173,414 175,412 176,410 178,408 180,407 181,405 183,403 185,401 186,399 188,397 190,395 191,393 193,391 195,389 197,386 198,384 200,382 202,379 203,377 205,375 207,372 208,369 210,367 212,364 214,362 215,359 217,356 219,353 220,351 222,348 224,345 225,342 227,340 229,337 230,334 232,331 234,329 236,326 237,323 239,320 241,318 242,315 244,312 246,309 247,307 249,304 251,301 252,299 254,296 256,293 258,291 259,288 261,285 263,283 264,280 266,277 268,275 269,272 271,269 273,266 274,264 276,261 278,258 280,255 281,253 283,250 285,247 286,244 288,242 290,239 291,236 293,234 295,231 296,228 298,226 300,223 302,221 303,219 305,216 307,214 308,211 310,209 312,207 313,205 315,202 317,200 318,198 320,196 322,194 324,192 325,189 327,187 329,185 330,183 332,181 334,179 335,177 337,175 339,173 341,171 342,168 344,166 346,164 347,162 349,160 351,158 352,156 354,153 356,151 357,149 359,147 361,144 363,142 364,140 366,138 368,136 369,133 371,131 373,129 374,127 376,125 378,123 379,121 381,119 383,117 385,115 386,113 388,111 390,110 391,108 393,106 395,105 396,103 398,101 400,100 401,98 403,97 405,95 407,94 408,93 410,91 412,90 413,89 415,87 417,86 418,85 420,84 422,82 423,81 425,80 427,78 429,77 430,76 432,75 434,73 435,72 437,71 439,69 440,68 442,67 444,66 445,65 447,63 449,62 451,61 452,60 454,59 456,59 457,58 459,57 461,56 462,56 464,55 466,55 468,54 469,54 471,54 473,54 474,54 476,53 478,54 479,54 481,54 483,54 484,54 486,54 488,55 490,55 491,55 493,55 495,55 496,56 498,56 500,56 501,56 503,56 505,56 506,56 508,56 510,56 512,56 513,56 515,56 517,56 518,56 520,56 522,56 523,55 525,55 527,55 528,55 530,55 532,55 534,55 535,55 537,55 539,55 540,55 542,55 544,55 545,56 547,56 549,56 550,56 552,57 554,57 556,58 557,58 559,59 561,59 562,60 564,61 566,62 567,63 569,64 571,65 573,66 574,67 576,68 578,70 579,71 581,73 583,74 584,76 586,77 588,79 589,81 591,83 593,84 595,86 596,88 598,90 600,93 601,95 603,97 605,99 606,102 608,104 610,107 611,110 613,112 615,115 617,118 618,121 620,124 622,127 623,130 625,133 627,136 628,139 630,142 632,145 633,148 635,151 637,155 639,158 640,161 642,164 644,167 645,170 647,173 649,176 650,179 652,182 654,185 655,187 657,190 659,193 661,196 662,198 664,201 666,204 667,206 669,209 671,211 672,214 674,216 676,219 677,221 679,223 681,226 683,228 684,230 686,233 688,235 689,237 691,239 693,242 694,244 696,246 698,249 700,251 701,253 703,255 705,258 706,260 708,263 710,265 711,267 713,270 715,272 716,275 718,277 720,280 722,282 723,285 725,287 727,290 728,292 730,295 732,297 733,300 735,302 737,304 738,307 740,309 742,311 744,314 745,316 747,318 749,320 750,322 752,324 754,326 755,328 757,330 759,332 760,333 762,335 764,337 766,339 767,341 769,342 771,344 772,346 774,348 776,350 777,351 779,353 781,355 782,356 784,358 786,360 788,362 789,363 791,365 793,367 794,368 796,370 798,372 799,374 801,376 803,377 804,379 806,381 808,383 810,384 811,386 813,388 815,390 816,391 818,393 820,395 821,396 823,398 825,400 827,401 828,403 830,404 832,406 833,407 835,409 837,411 838,412 840,414 842,415 843,417 845,418 847,419 849,421 850,422 852,424 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="483,473 483,54 "/>
<rect x="509" y="53" width="0" height="419" opacity="0.1" fill="#E31A1C" stroke="none"/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<text x="798" y="113" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Noise threshold
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
<rect x="768" y="113" width="20" height="10" opacity="0.25" fill="#E31A1C" stroke="none"/>
</svg>

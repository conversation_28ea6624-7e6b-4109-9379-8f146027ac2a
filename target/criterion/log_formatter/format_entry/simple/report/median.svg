<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="447" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,447 86,447 "/>
<text x="77" y="408" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,408 86,408 "/>
<text x="77" y="368" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,368 86,368 "/>
<text x="77" y="329" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,329 86,329 "/>
<text x="77" y="290" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,290 86,290 "/>
<text x="77" y="250" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,250 86,250 "/>
<text x="77" y="211" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,211 86,211 "/>
<text x="77" y="172" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,172 86,172 "/>
<text x="77" y="133" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,133 86,133 "/>
<text x="77" y="93" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,93 86,93 "/>
<text x="77" y="54" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,54 86,54 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="104" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
573.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="104,473 104,478 "/>
<text x="272" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
574
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="272,473 272,478 "/>
<text x="440" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
574.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="440,473 440,478 "/>
<text x="608" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
575
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="608,473 608,478 "/>
<text x="776" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
575.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="776,473 776,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,472 98,472 100,472 102,472 103,472 105,472 107,471 109,471 110,470 112,470 114,469 115,468 117,467 119,466 120,465 122,464 124,462 125,461 127,459 129,458 131,456 132,454 134,452 136,450 137,448 139,446 141,444 142,442 144,440 146,438 147,436 149,434 151,432 153,430 154,428 156,426 158,424 159,422 161,420 163,418 164,416 166,414 168,412 169,410 171,407 173,405 175,402 176,399 178,396 180,393 181,390 183,386 185,383 186,379 188,375 190,370 191,366 193,361 195,357 197,352 198,347 200,342 202,338 203,333 205,329 207,325 208,321 210,317 212,314 214,311 215,309 217,307 219,305 220,304 222,303 224,302 225,302 227,302 229,303 230,303 232,304 234,305 236,307 237,308 239,309 241,311 242,312 244,313 246,314 247,315 249,316 251,316 252,317 254,317 256,317 258,317 259,317 261,317 263,316 264,316 266,315 268,314 269,313 271,313 273,312 274,311 276,310 278,309 280,308 281,307 283,306 285,304 286,303 288,302 290,301 291,299 293,298 295,296 296,294 298,292 300,289 302,287 303,284 305,280 307,276 308,272 310,268 312,263 313,257 315,251 317,245 318,238 320,231 322,223 324,215 325,207 327,198 329,189 330,180 332,171 334,163 335,154 337,146 339,138 341,131 342,124 344,118 346,113 347,108 349,105 351,102 352,101 354,100 356,101 357,102 359,104 361,107 363,111 364,115 366,120 368,126 369,132 371,139 373,147 374,154 376,162 378,171 379,180 381,188 383,198 385,207 386,217 388,226 390,236 391,246 393,256 395,266 396,276 398,286 400,296 401,306 403,316 405,326 407,335 408,344 410,353 412,361 413,369 415,376 417,383 418,389 420,394 422,399 423,403 425,406 427,408 429,410 430,411 432,411 434,410 435,408 437,405 439,402 440,398 442,393 444,388 445,382 447,376 449,369 451,362 452,355 454,347 456,340 457,332 459,324 461,317 462,309 464,303 466,296 468,290 469,285 471,280 473,277 474,273 476,271 478,270 479,270 481,270 483,272 484,275 486,278 488,282 490,287 491,293 493,300 495,307 496,314 498,322 500,330 501,339 503,347 505,355 506,364 508,372 510,379 512,387 513,393 515,399 517,405 518,410 520,414 522,417 523,419 525,420 527,421 528,420 530,418 532,416 534,412 535,407 537,401 539,394 540,386 542,377 544,367 545,356 547,344 549,331 550,318 552,303 554,289 556,274 557,259 559,244 561,228 562,213 564,199 566,185 567,171 569,159 571,147 573,136 574,126 576,118 578,110 579,104 581,99 583,96 584,94 586,93 588,93 589,95 591,97 593,101 595,105 596,110 598,116 600,123 601,130 603,138 605,146 606,154 608,163 610,172 611,181 613,190 615,198 617,207 618,215 620,224 622,232 623,239 625,247 627,254 628,261 630,267 632,273 633,278 635,283 637,288 639,292 640,296 642,299 644,303 645,305 647,308 649,310 650,312 652,314 654,316 655,317 657,319 659,320 661,321 662,323 664,324 666,325 667,326 669,327 671,328 672,329 674,330 676,331 677,332 679,332 681,333 683,334 684,334 686,335 688,335 689,335 691,336 693,336 694,337 696,337 698,338 700,338 701,339 703,340 705,341 706,341 708,343 710,344 711,345 713,346 715,347 716,348 718,350 720,351 722,352 723,353 725,354 727,355 728,356 730,357 732,358 733,358 735,359 737,359 738,360 740,360 742,360 744,360 745,360 747,360 749,360 750,360 752,360 754,360 755,360 757,360 759,360 760,360 762,359 764,359 766,359 767,359 769,359 771,359 772,359 774,360 776,360 777,360 779,360 781,360 782,360 784,361 786,361 788,361 789,362 791,362 793,363 794,363 796,364 798,365 799,366 801,367 803,368 804,369 806,371 808,373 810,374 811,377 813,379 815,381 816,384 818,387 820,390 821,393 823,396 825,399 827,403 828,406 830,409 832,413 833,416 835,419 837,422 838,425 840,428 842,430 843,433 845,435 847,437 849,439 850,441 852,443 854,444 855,446 857,447 859,448 860,449 862,450 864,451 865,452 867,453 869,454 871,454 872,455 874,456 876,456 877,457 879,457 881,458 882,458 884,459 886,459 887,459 889,460 891,460 893,460 894,460 896,460 898,460 899,460 901,460 903,461 904,461 906,461 908,461 909,461 911,462 913,462 915,462 916,463 918,463 920,464 921,465 923,465 925,466 926,467 928,467 930,468 932,469 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,416 166,414 168,412 169,410 171,407 173,405 175,402 176,399 178,396 180,393 181,390 183,386 185,383 186,379 188,375 190,370 191,366 193,361 195,357 197,352 198,347 200,342 202,338 203,333 205,329 207,325 208,321 210,317 212,314 214,311 215,309 217,307 219,305 220,304 222,303 224,302 225,302 227,302 229,303 230,303 232,304 234,305 236,307 237,308 239,309 241,311 242,312 244,313 246,314 247,315 249,316 251,316 252,317 254,317 256,317 258,317 259,317 261,317 263,316 264,316 266,315 268,314 269,313 271,313 273,312 274,311 276,310 278,309 280,308 281,307 283,306 285,304 286,303 288,302 290,301 291,299 293,298 295,296 296,294 298,292 300,289 302,287 303,284 305,280 307,276 308,272 310,268 312,263 313,257 315,251 317,245 318,238 320,231 322,223 324,215 325,207 327,198 329,189 330,180 332,171 334,163 335,154 337,146 339,138 341,131 342,124 344,118 346,113 347,108 349,105 351,102 352,101 354,100 356,101 357,102 359,104 361,107 363,111 364,115 366,120 368,126 369,132 371,139 373,147 374,154 376,162 378,171 379,180 381,188 383,198 385,207 386,217 388,226 390,236 391,246 393,256 395,266 396,276 398,286 400,296 401,306 403,316 405,326 407,335 408,344 410,353 412,361 413,369 415,376 417,383 418,389 420,394 422,399 423,403 425,406 427,408 429,410 430,411 432,411 434,410 435,408 437,405 439,402 440,398 442,393 444,388 445,382 447,376 449,369 451,362 452,355 454,347 456,340 457,332 459,324 461,317 462,309 464,303 466,296 468,290 469,285 471,280 473,277 474,273 476,271 478,270 479,270 481,270 483,272 484,275 486,278 488,282 490,287 491,293 493,300 495,307 496,314 498,322 500,330 501,339 503,347 505,355 506,364 508,372 510,379 512,387 513,393 515,399 517,405 518,410 520,414 522,417 523,419 525,420 527,421 528,420 530,418 532,416 534,412 535,407 537,401 539,394 540,386 542,377 544,367 545,356 547,344 549,331 550,318 552,303 554,289 556,274 557,259 559,244 561,228 562,213 564,199 566,185 567,171 569,159 571,147 573,136 574,126 576,118 578,110 579,104 581,99 583,96 584,94 586,93 588,93 589,95 591,97 593,101 595,105 596,110 598,116 600,123 601,130 603,138 605,146 606,154 608,163 610,172 611,181 613,190 615,198 617,207 618,215 620,224 622,232 623,239 625,247 627,254 628,261 630,267 632,273 633,278 635,283 637,288 639,292 640,296 642,299 644,303 645,305 647,308 649,310 650,312 652,314 654,316 655,317 657,319 659,320 661,321 662,323 664,324 666,325 667,326 669,327 671,328 672,329 674,330 676,331 677,332 679,332 681,333 683,334 684,334 686,335 688,335 689,335 691,336 693,336 694,337 696,337 698,338 700,338 701,339 703,340 705,341 706,341 708,343 710,344 711,345 713,346 715,347 716,348 718,350 720,351 722,352 723,353 725,354 727,355 728,356 730,357 732,358 733,358 735,359 737,359 738,360 740,360 742,360 744,360 745,360 747,360 749,360 750,360 752,360 754,360 755,360 757,360 759,360 760,360 762,359 764,359 766,359 767,359 769,359 771,359 772,359 774,360 776,360 777,360 779,360 781,360 782,360 784,361 786,361 788,361 789,362 791,362 793,363 794,363 796,364 798,365 799,366 801,367 803,368 804,369 806,371 808,373 810,374 811,377 813,379 815,381 816,384 818,387 820,390 821,393 823,396 825,399 827,403 828,406 830,409 832,413 833,416 835,419 837,422 838,425 840,428 842,430 843,433 845,435 847,437 849,439 850,441 852,443 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="482,473 482,271 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average Time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="430" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,430 86,430 "/>
<text x="77" y="387" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,387 86,387 "/>
<text x="77" y="344" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,344 86,344 "/>
<text x="77" y="301" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,301 86,301 "/>
<text x="77" y="258" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,258 86,258 "/>
<text x="77" y="215" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.06
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,215 86,215 "/>
<text x="77" y="173" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.07
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,173 86,173 "/>
<text x="77" y="130" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.08
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,130 86,130 "/>
<text x="77" y="87" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.09
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,87 86,87 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="335" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
580
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="335,473 335,478 "/>
<text x="584" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
600
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="584,473 584,478 "/>
<text x="833" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
620
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="833,473 833,478 "/>
<polygon opacity="0.5" fill="#E31A1C" points="276,472 277,472 278,472 280,472 281,472 282,472 283,472 285,472 286,472 287,472 289,472 290,472 291,472 293,472 294,472 295,472 297,472 298,472 299,472 301,471 302,471 303,471 304,471 306,471 307,471 308,471 310,470 311,470 312,470 314,470 315,469 316,469 318,469 319,468 320,468 322,467 323,467 324,466 326,466 327,465 328,465 329,464 331,463 332,463 333,462 335,461 336,460 337,459 339,458 340,457 341,456 343,455 344,454 345,453 347,451 348,450 349,449 350,447 352,445 353,444 354,442 356,440 357,438 358,436 360,434 361,432 362,429 364,427 365,424 366,422 368,419 369,416 370,413 372,410 373,406 374,403 375,400 377,396 378,392 379,388 381,384 382,380 383,376 385,371 386,367 387,362 389,357 390,352 391,347 393,342 394,336 395,331 396,325 398,319 399,313 400,307 402,301 403,295 404,288 406,282 407,275 408,268 410,262 411,255 412,248 414,241 415,234 416,227 418,219 419,212 420,205 421,198 423,190 424,183 425,176 427,169 428,162 429,155 431,148 432,141 433,134 435,128 436,122 437,115 439,109 440,104 441,98 443,93 444,88 445,83 446,79 448,75 449,71 450,67 452,64 453,62 454,59 456,57 457,56 458,55 460,54 461,53 462,54 464,54 465,55 466,56 467,58 469,60 470,62 471,65 473,69 474,72 475,76 477,80 478,85 479,89 481,94 482,100 483,105 485,111 486,117 487,123 489,129 490,136 491,142 492,149 494,156 495,162 496,169 498,176 499,183 500,190 502,197 503,204 504,210 506,217 507,224 508,230 510,237 511,243 512,249 513,256 515,262 516,268 517,274 519,279 520,285 521,290 523,296 524,301 525,306 527,311 528,316 529,320 531,325 532,329 533,333 535,337 536,341 537,345 538,348 540,352 541,355 542,358 544,362 545,364 546,367 548,370 549,373 550,375 552,377 553,380 554,382 556,384 557,386 558,388 559,389 561,391 562,393 563,394 565,396 566,397 567,398 569,400 570,401 571,402 573,403 574,404 575,405 577,406 578,407 579,408 581,409 582,410 583,411 584,411 586,412 587,413 588,414 590,414 591,415 592,416 594,417 595,417 596,418 598,419 599,420 600,420 602,421 603,422 604,423 606,423 607,424 608,425 609,426 611,427 612,428 613,428 615,429 616,430 617,431 619,432 620,433 621,434 623,435 624,436 625,437 627,438 628,439 629,440 630,441 632,443 633,444 634,445 636,446 637,447 638,448 640,449 641,450 642,451 644,452 645,453 646,454 648,455 649,456 650,457 652,457 653,458 654,459 655,460 657,460 658,461 659,462 661,462 662,463 663,463 665,464 666,464 667,465 669,465 670,465 671,465 673,466 674,466 675,466 676,466 678,466 679,466 680,466 682,467 683,467 684,467 686,467 687,466 688,466 690,466 691,466 692,466 694,466 695,466 696,466 698,466 699,466 700,466 701,466 703,465 704,465 705,465 707,465 708,465 709,465 711,465 712,465 713,465 715,465 716,465 717,465 719,465 720,465 721,465 722,465 724,465 725,466 726,466 728,466 729,466 730,466 732,466 733,467 734,467 736,467 737,467 738,467 740,467 741,468 742,468 744,468 745,468 746,469 747,469 749,469 750,469 751,469 753,469 754,470 755,470 757,470 758,470 759,470 761,470 762,471 763,471 765,471 766,471 767,471 769,471 770,471 771,471 772,471 774,471 775,471 776,471 778,471 779,472 780,472 782,472 783,471 784,471 786,471 787,471 788,471 790,471 791,471 792,471 793,471 795,471 796,471 797,471 799,471 800,470 801,470 803,470 804,470 805,470 807,470 808,469 809,469 811,469 812,469 813,469 815,468 816,468 817,468 818,468 820,468 821,467 822,467 824,467 825,467 826,467 828,466 829,466 830,466 832,466 833,466 834,466 836,466 837,465 838,465 839,465 841,465 842,465 843,465 845,465 846,465 847,465 849,465 850,465 851,465 853,465 854,465 855,466 857,466 858,466 859,466 861,466 862,466 863,466 864,467 866,467 867,467 868,467 870,467 871,468 872,468 874,468 875,468 876,468 878,469 879,469 880,469 882,469 883,469 884,470 885,470 887,470 888,470 889,470 891,470 892,471 893,471 895,471 896,471 897,471 899,471 900,471 901,471 903,472 904,472 905,472 907,472 908,472 909,472 910,472 912,472 913,472 914,472 916,472 917,472 918,472 920,472 921,472 922,472 924,472 925,472 926,472 928,472 929,472 930,472 932,472 932,472 276,472 "/>
<polygon opacity="0.5" fill="#1F78B4" points="87,472 88,472 89,472 91,472 92,472 93,472 95,472 96,472 97,472 99,472 100,472 102,472 103,471 104,471 106,471 107,471 108,471 110,471 111,470 113,470 114,470 115,470 117,469 118,469 119,469 121,469 122,468 123,468 125,467 126,467 128,466 129,466 130,465 132,464 133,464 134,463 136,462 137,461 139,460 140,459 141,458 143,457 144,456 145,455 147,453 148,452 149,450 151,449 152,447 154,445 155,443 156,441 158,439 159,437 160,435 162,432 163,429 165,427 166,424 167,421 169,418 170,415 171,411 173,408 174,404 175,400 177,396 178,392 180,388 181,384 182,379 184,375 185,370 186,365 188,360 189,355 191,350 192,344 193,339 195,333 196,327 197,321 199,315 200,309 202,303 203,297 204,291 206,285 207,278 208,272 210,265 211,259 212,253 214,246 215,240 217,233 218,227 219,221 221,214 222,208 223,202 225,196 226,190 228,184 229,179 230,173 232,168 233,162 234,157 236,153 237,148 238,144 240,139 241,135 243,132 244,128 245,125 247,122 248,119 249,116 251,114 252,112 254,111 255,109 256,108 258,107 259,107 260,106 262,106 263,107 264,107 266,108 267,109 269,111 270,112 271,114 273,116 274,119 275,121 277,124 278,127 280,130 281,134 282,138 284,141 285,146 286,150 288,154 289,159 291,163 292,168 293,173 295,178 296,183 297,188 299,193 300,198 301,204 303,209 304,214 306,220 307,225 308,230 310,236 311,241 312,246 314,251 315,257 317,262 318,267 319,272 321,277 322,282 323,287 325,291 326,296 327,301 329,305 330,309 332,314 333,318 334,322 336,326 337,329 338,333 340,337 341,340 343,344 344,347 345,350 347,353 348,356 349,359 351,361 352,364 353,367 355,369 356,371 358,374 359,376 360,378 362,380 363,382 364,384 366,385 367,387 369,389 370,390 371,392 373,393 374,394 375,396 377,397 378,398 380,400 381,401 382,402 384,403 385,404 386,405 388,406 389,407 390,408 392,409 393,410 395,411 396,411 397,412 399,413 400,414 401,415 403,416 404,416 406,417 407,418 408,419 410,420 411,420 412,421 414,422 415,423 416,424 418,424 419,425 421,426 422,427 423,428 425,428 426,429 427,430 429,431 430,431 432,432 433,433 434,434 436,435 437,435 438,436 440,437 441,438 442,438 444,439 445,440 447,440 448,441 449,442 451,443 452,443 453,444 455,445 456,445 458,446 459,446 460,447 462,448 463,448 464,449 466,449 467,450 468,450 470,451 471,451 473,452 474,452 475,452 477,453 478,453 479,454 481,454 482,454 484,454 485,455 486,455 488,455 489,456 490,456 492,456 493,456 495,456 496,457 497,457 499,457 500,457 501,457 503,458 504,458 505,458 507,458 508,458 510,458 511,459 512,459 514,459 515,459 516,459 518,459 519,459 521,460 522,460 523,460 525,460 526,460 527,460 529,461 530,461 531,461 533,461 534,461 536,462 537,462 538,462 540,462 541,462 542,463 544,463 545,463 547,463 548,463 549,464 551,464 552,464 553,464 555,465 556,465 557,465 559,465 560,465 562,465 563,466 564,466 566,466 567,466 568,466 570,466 571,467 573,467 574,467 575,467 577,467 578,467 579,467 581,467 582,467 584,467 585,467 586,467 588,467 589,467 590,467 592,467 593,467 594,467 596,467 597,467 599,467 600,467 601,467 603,466 604,466 605,466 607,466 608,466 610,466 611,466 612,465 614,465 615,465 616,465 618,465 619,465 620,464 622,464 623,464 625,464 626,464 627,463 629,463 630,463 631,463 633,463 634,463 636,463 637,462 638,462 640,462 641,462 642,462 644,462 645,462 646,462 648,462 649,462 651,462 652,462 653,462 655,462 656,462 657,462 659,462 660,462 662,462 663,462 664,462 666,462 667,462 668,463 670,463 671,463 673,463 674,463 675,463 677,464 678,464 679,464 681,464 682,464 683,465 685,465 686,465 688,465 689,466 690,466 692,466 693,466 694,467 696,467 697,467 699,467 700,467 701,468 703,468 704,468 705,468 707,468 708,469 709,469 711,469 712,469 714,469 715,470 716,470 718,470 719,470 720,470 722,470 723,470 725,471 726,471 727,471 729,471 730,471 731,471 733,471 734,471 735,471 737,472 738,472 740,472 741,472 742,472 744,472 745,472 746,472 748,472 749,472 751,472 752,472 753,472 755,472 756,472 757,472 759,472 760,472 761,472 763,472 764,472 766,472 767,472 768,472 770,472 770,472 87,472 "/>
<polyline fill="none" opacity="1" stroke="#E31A1C" stroke-width="2" points="479,473 479,90 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="2" points="292,473 292,168 "/>
<text x="869" y="235" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Base PDF
</text>
<text x="869" y="250" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
New PDF
</text>
<text x="869" y="265" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Base Mean
</text>
<text x="869" y="280" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
New Mean
</text>
<rect x="839" y="235" width="20" height="10" opacity="0.5" fill="#E31A1C" stroke="none"/>
<rect x="839" y="250" width="20" height="10" opacity="0.5" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#E31A1C" stroke-width="1" points="839,270 859,270 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="839,285 859,285 "/>
</svg>

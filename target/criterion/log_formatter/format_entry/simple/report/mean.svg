<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
log_formatter/format_entry/simple:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="449" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,449 86,449 "/>
<text x="77" y="404" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,404 86,404 "/>
<text x="77" y="358" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,358 86,358 "/>
<text x="77" y="313" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,313 86,313 "/>
<text x="77" y="267" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,267 86,267 "/>
<text x="77" y="222" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,222 86,222 "/>
<text x="77" y="176" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.35
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,176 86,176 "/>
<text x="77" y="131" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,131 86,131 "/>
<text x="77" y="85" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.45
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,85 86,85 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="88" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
574.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="88,473 88,478 "/>
<text x="185" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
575
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="185,473 185,478 "/>
<text x="282" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
575.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="282,473 282,478 "/>
<text x="378" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
576
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="378,473 378,478 "/>
<text x="475" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
576.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="475,473 475,478 "/>
<text x="572" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
577
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="572,473 572,478 "/>
<text x="669" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
577.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="669,473 669,478 "/>
<text x="766" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
578
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="766,473 766,478 "/>
<text x="863" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
578.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="863,473 863,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,471 90,470 92,469 93,469 95,468 97,467 98,466 100,465 102,464 103,463 105,462 107,461 109,460 110,459 112,458 114,457 115,456 117,455 119,454 120,453 122,452 124,450 125,449 127,448 129,447 131,446 132,444 134,443 136,442 137,440 139,439 141,438 142,437 144,435 146,434 147,432 149,431 151,430 153,428 154,427 156,425 158,424 159,422 161,420 163,419 164,417 166,415 168,414 169,412 171,410 173,408 175,406 176,404 178,402 180,401 181,399 183,397 185,395 186,393 188,391 190,389 191,387 193,385 195,384 197,382 198,380 200,378 202,376 203,375 205,373 207,371 208,369 210,367 212,365 214,363 215,361 217,359 219,357 220,355 222,352 224,350 225,348 227,345 229,343 230,340 232,337 234,335 236,332 237,329 239,326 241,323 242,320 244,317 246,314 247,311 249,308 251,305 252,302 254,299 256,296 258,293 259,290 261,287 263,284 264,281 266,278 268,275 269,272 271,269 273,266 274,263 276,260 278,257 280,254 281,252 283,249 285,246 286,244 288,241 290,239 291,236 293,234 295,232 296,229 298,227 300,225 302,223 303,221 305,219 307,217 308,215 310,213 312,210 313,208 315,206 317,204 318,202 320,200 322,197 324,195 325,193 327,190 329,188 330,185 332,183 334,180 335,178 337,175 339,173 341,170 342,168 344,165 346,163 347,161 349,159 351,156 352,154 354,152 356,151 357,149 359,147 361,145 363,143 364,142 366,140 368,139 369,137 371,136 373,134 374,132 376,131 378,129 379,128 381,126 383,125 385,123 386,122 388,120 390,119 391,117 393,116 395,115 396,113 398,112 400,111 401,110 403,109 405,108 407,107 408,106 410,105 412,105 413,104 415,103 417,103 418,102 420,102 422,101 423,100 425,100 427,99 429,99 430,98 432,98 434,97 435,97 437,96 439,96 440,95 442,95 444,95 445,94 447,94 449,94 451,94 452,94 454,94 456,94 457,94 459,94 461,94 462,95 464,95 466,96 468,97 469,97 471,98 473,99 474,100 476,101 478,102 479,103 481,104 483,105 484,106 486,107 488,108 490,109 491,110 493,111 495,112 496,112 498,113 500,114 501,115 503,116 505,116 506,117 508,118 510,119 512,119 513,120 515,121 517,122 518,123 520,123 522,124 523,125 525,126 527,128 528,129 530,130 532,131 534,133 535,134 537,136 539,137 540,139 542,141 544,142 545,144 547,146 549,148 550,150 552,152 554,153 556,155 557,157 559,159 561,161 562,163 564,165 566,168 567,170 569,172 571,174 573,176 574,178 576,180 578,182 579,185 581,187 583,189 584,191 586,193 588,196 589,198 591,200 593,203 595,205 596,207 598,209 600,212 601,214 603,216 605,218 606,221 608,223 610,225 611,227 613,229 615,231 617,233 618,235 620,236 622,238 623,240 625,242 627,244 628,245 630,247 632,249 633,250 635,252 637,254 639,255 640,257 642,259 644,260 645,262 647,264 649,266 650,267 652,269 654,271 655,273 657,275 659,277 661,279 662,281 664,283 666,285 667,287 669,289 671,291 672,293 674,296 676,298 677,300 679,302 681,305 683,307 684,309 686,311 688,313 689,316 691,318 693,320 694,322 696,324 698,326 700,328 701,330 703,332 705,334 706,336 708,338 710,340 711,342 713,343 715,345 716,347 718,348 720,350 722,352 723,353 725,355 727,356 728,358 730,359 732,361 733,362 735,364 737,365 738,367 740,368 742,369 744,371 745,372 747,374 749,375 750,376 752,378 754,379 755,380 757,381 759,383 760,384 762,385 764,387 766,388 767,389 769,390 771,392 772,393 774,394 776,395 777,397 779,398 781,399 782,401 784,402 786,403 788,405 789,406 791,407 793,409 794,410 796,412 798,413 799,414 801,416 803,417 804,418 806,420 808,421 810,422 811,424 813,425 815,426 816,427 818,429 820,430 821,431 823,432 825,433 827,434 828,435 830,436 832,437 833,438 835,439 837,439 838,440 840,441 842,442 843,442 845,443 847,444 849,444 850,445 852,446 854,446 855,447 857,448 859,448 860,449 862,449 864,450 865,451 867,451 869,452 871,452 872,453 874,454 876,454 877,455 879,456 881,456 882,457 884,458 886,458 887,459 889,460 891,460 893,461 894,461 896,462 898,463 899,463 901,464 903,464 904,465 906,466 908,466 909,467 911,467 913,468 915,468 916,469 918,469 920,470 921,470 923,470 925,471 926,471 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,417 166,415 168,414 169,412 171,410 173,408 175,406 176,404 178,402 180,401 181,399 183,397 185,395 186,393 188,391 190,389 191,387 193,385 195,384 197,382 198,380 200,378 202,376 203,375 205,373 207,371 208,369 210,367 212,365 214,363 215,361 217,359 219,357 220,355 222,352 224,350 225,348 227,345 229,343 230,340 232,337 234,335 236,332 237,329 239,326 241,323 242,320 244,317 246,314 247,311 249,308 251,305 252,302 254,299 256,296 258,293 259,290 261,287 263,284 264,281 266,278 268,275 269,272 271,269 273,266 274,263 276,260 278,257 280,254 281,252 283,249 285,246 286,244 288,241 290,239 291,236 293,234 295,232 296,229 298,227 300,225 302,223 303,221 305,219 307,217 308,215 310,213 312,210 313,208 315,206 317,204 318,202 320,200 322,197 324,195 325,193 327,190 329,188 330,185 332,183 334,180 335,178 337,175 339,173 341,170 342,168 344,165 346,163 347,161 349,159 351,156 352,154 354,152 356,151 357,149 359,147 361,145 363,143 364,142 366,140 368,139 369,137 371,136 373,134 374,132 376,131 378,129 379,128 381,126 383,125 385,123 386,122 388,120 390,119 391,117 393,116 395,115 396,113 398,112 400,111 401,110 403,109 405,108 407,107 408,106 410,105 412,105 413,104 415,103 417,103 418,102 420,102 422,101 423,100 425,100 427,99 429,99 430,98 432,98 434,97 435,97 437,96 439,96 440,95 442,95 444,95 445,94 447,94 449,94 451,94 452,94 454,94 456,94 457,94 459,94 461,94 462,95 464,95 466,96 468,97 469,97 471,98 473,99 474,100 476,101 478,102 479,103 481,104 483,105 484,106 486,107 488,108 490,109 491,110 493,111 495,112 496,112 498,113 500,114 501,115 503,116 505,116 506,117 508,118 510,119 512,119 513,120 515,121 517,122 518,123 520,123 522,124 523,125 525,126 527,128 528,129 530,130 532,131 534,133 535,134 537,136 539,137 540,139 542,141 544,142 545,144 547,146 549,148 550,150 552,152 554,153 556,155 557,157 559,159 561,161 562,163 564,165 566,168 567,170 569,172 571,174 573,176 574,178 576,180 578,182 579,185 581,187 583,189 584,191 586,193 588,196 589,198 591,200 593,203 595,205 596,207 598,209 600,212 601,214 603,216 605,218 606,221 608,223 610,225 611,227 613,229 615,231 617,233 618,235 620,236 622,238 623,240 625,242 627,244 628,245 630,247 632,249 633,250 635,252 637,254 639,255 640,257 642,259 644,260 645,262 647,264 649,266 650,267 652,269 654,271 655,273 657,275 659,277 661,279 662,281 664,283 666,285 667,287 669,289 671,291 672,293 674,296 676,298 677,300 679,302 681,305 683,307 684,309 686,311 688,313 689,316 691,318 693,320 694,322 696,324 698,326 700,328 701,330 703,332 705,334 706,336 708,338 710,340 711,342 713,343 715,345 716,347 718,348 720,350 722,352 723,353 725,355 727,356 728,358 730,359 732,361 733,362 735,364 737,365 738,367 740,368 742,369 744,371 745,372 747,374 749,375 750,376 752,378 754,379 755,380 757,381 759,383 760,384 762,385 764,387 766,388 767,389 769,390 771,392 772,393 774,394 776,395 777,397 779,398 781,399 782,401 784,402 786,403 788,405 789,406 791,407 793,409 794,410 796,412 798,413 799,414 801,416 803,417 804,418 806,420 808,421 810,422 811,424 813,425 815,426 816,427 818,429 820,430 821,431 823,432 825,433 827,434 828,435 830,436 832,437 833,438 835,439 837,439 838,440 840,441 842,442 843,442 845,443 847,444 849,444 850,445 852,446 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="480,473 480,103 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

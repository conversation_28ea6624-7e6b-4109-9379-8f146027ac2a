<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
memory_allocations/optimized_formatting:median
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="441" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,441 86,441 "/>
<text x="77" y="404" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,404 86,404 "/>
<text x="77" y="366" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,366 86,366 "/>
<text x="77" y="328" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.4
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,328 86,328 "/>
<text x="77" y="291" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,291 86,291 "/>
<text x="77" y="253" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,253 86,253 "/>
<text x="77" y="216" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,216 86,216 "/>
<text x="77" y="178" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,178 86,178 "/>
<text x="77" y="141" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,141 86,141 "/>
<text x="77" y="103" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,103 86,103 "/>
<text x="77" y="65" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
1.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,65 86,65 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="124" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
648
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="124,473 124,478 "/>
<text x="241" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
648.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="241,473 241,478 "/>
<text x="358" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
649
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="358,473 358,478 "/>
<text x="474" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
649.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="474,473 474,478 "/>
<text x="591" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
650
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="591,473 591,478 "/>
<text x="708" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
650.5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="708,473 708,478 "/>
<text x="825" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
651
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="825,473 825,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,470 88,470 90,469 92,469 93,469 95,468 97,468 98,468 100,467 102,467 103,467 105,466 107,466 109,465 110,465 112,464 114,464 115,463 117,463 119,462 120,462 122,461 124,461 125,460 127,460 129,459 131,459 132,458 134,458 136,457 137,457 139,457 141,456 142,456 144,456 146,456 147,455 149,455 151,455 153,455 154,455 156,455 158,455 159,455 161,455 163,455 164,455 166,455 168,455 169,455 171,455 173,455 175,455 176,455 178,455 180,455 181,455 183,455 185,454 186,454 188,454 190,454 191,454 193,453 195,453 197,453 198,452 200,452 202,452 203,451 205,451 207,450 208,450 210,449 212,449 214,448 215,447 217,447 219,446 220,445 222,444 224,443 225,442 227,441 229,440 230,439 232,438 234,436 236,435 237,434 239,432 241,430 242,429 244,427 246,425 247,423 249,421 251,419 252,417 254,415 256,413 258,411 259,410 261,408 263,407 264,406 266,405 268,404 269,404 271,403 273,404 274,404 276,405 278,406 280,407 281,408 283,410 285,412 286,413 288,415 290,417 291,419 293,421 295,422 296,424 298,425 300,426 302,427 303,428 305,428 307,428 308,428 310,427 312,427 313,426 315,425 317,425 318,424 320,423 322,422 324,421 325,420 327,420 329,419 330,419 332,419 334,418 335,418 337,419 339,419 341,419 342,419 344,419 346,419 347,419 349,419 351,418 352,417 354,415 356,414 357,411 359,408 361,405 363,401 364,397 366,392 368,386 369,380 371,373 373,366 374,359 376,350 378,342 379,333 381,325 383,316 385,306 386,297 388,289 390,280 391,272 393,264 395,257 396,250 398,244 400,239 401,235 403,232 405,230 407,229 408,229 410,230 412,233 413,237 415,241 417,247 418,254 420,262 422,270 423,280 425,289 427,300 429,310 430,321 432,332 434,343 435,353 437,364 439,374 440,383 442,392 444,400 445,407 447,414 449,420 451,425 452,429 454,432 456,435 457,437 459,438 461,438 462,437 464,436 466,434 468,432 469,429 471,425 473,421 474,417 476,413 478,408 479,403 481,399 483,394 484,390 486,386 488,382 490,378 491,376 493,373 495,371 496,370 498,370 500,370 501,371 503,372 505,374 506,376 508,379 510,383 512,386 513,390 515,394 517,399 518,403 520,407 522,412 523,416 525,419 527,423 528,426 530,428 532,430 534,432 535,433 537,433 539,433 540,432 542,430 544,427 545,424 547,420 549,415 550,410 552,404 554,398 556,391 557,384 559,376 561,368 562,360 564,351 566,343 567,335 569,328 571,320 573,313 574,307 576,301 578,296 579,291 581,288 583,285 584,283 586,282 588,281 589,281 591,282 593,283 595,285 596,288 598,291 600,294 601,297 603,301 605,305 606,309 608,313 610,317 611,321 613,324 615,328 617,332 618,335 620,338 622,341 623,344 625,347 627,350 628,352 630,354 632,356 633,358 635,359 637,361 639,362 640,363 642,364 644,365 645,365 647,366 649,366 650,366 652,366 654,366 655,366 657,365 659,365 661,365 662,364 664,364 666,363 667,363 669,363 671,362 672,362 674,362 676,362 677,362 679,362 681,363 683,363 684,363 686,363 688,363 689,363 691,362 693,361 694,359 696,357 698,354 700,351 701,346 703,341 705,335 706,328 708,321 710,312 711,303 713,293 715,282 716,270 718,258 720,246 722,234 723,221 725,208 727,196 728,183 730,171 732,160 733,149 735,139 737,130 738,122 740,114 742,108 744,103 745,98 747,95 749,93 750,92 752,92 754,93 755,96 757,99 759,103 760,108 762,115 764,121 766,129 767,138 769,147 771,157 772,168 774,179 776,190 777,202 779,214 781,226 782,239 784,251 786,263 788,275 789,287 791,298 793,309 794,320 796,330 798,339 799,348 801,357 803,364 804,371 806,378 808,384 810,389 811,394 813,398 815,401 816,405 818,407 820,410 821,412 823,413 825,415 827,416 828,417 830,417 832,418 833,419 835,419 837,419 838,420 840,420 842,420 843,421 845,421 847,422 849,423 850,423 852,424 854,425 855,427 857,428 859,429 860,431 862,433 864,434 865,436 867,438 869,440 871,442 872,443 874,445 876,447 877,449 879,451 881,452 882,454 884,455 886,457 887,458 889,460 891,461 893,462 894,463 896,464 898,465 899,466 901,466 903,467 904,468 906,468 908,469 909,469 911,469 913,470 915,470 916,471 918,471 920,471 921,471 923,472 925,472 926,472 928,472 930,472 932,472 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,455 166,455 168,455 169,455 171,455 173,455 175,455 176,455 178,455 180,455 181,455 183,455 185,454 186,454 188,454 190,454 191,454 193,453 195,453 197,453 198,452 200,452 202,452 203,451 205,451 207,450 208,450 210,449 212,449 214,448 215,447 217,447 219,446 220,445 222,444 224,443 225,442 227,441 229,440 230,439 232,438 234,436 236,435 237,434 239,432 241,430 242,429 244,427 246,425 247,423 249,421 251,419 252,417 254,415 256,413 258,411 259,410 261,408 263,407 264,406 266,405 268,404 269,404 271,403 273,404 274,404 276,405 278,406 280,407 281,408 283,410 285,412 286,413 288,415 290,417 291,419 293,421 295,422 296,424 298,425 300,426 302,427 303,428 305,428 307,428 308,428 310,427 312,427 313,426 315,425 317,425 318,424 320,423 322,422 324,421 325,420 327,420 329,419 330,419 332,419 334,418 335,418 337,419 339,419 341,419 342,419 344,419 346,419 347,419 349,419 351,418 352,417 354,415 356,414 357,411 359,408 361,405 363,401 364,397 366,392 368,386 369,380 371,373 373,366 374,359 376,350 378,342 379,333 381,325 383,316 385,306 386,297 388,289 390,280 391,272 393,264 395,257 396,250 398,244 400,239 401,235 403,232 405,230 407,229 408,229 410,230 412,233 413,237 415,241 417,247 418,254 420,262 422,270 423,280 425,289 427,300 429,310 430,321 432,332 434,343 435,353 437,364 439,374 440,383 442,392 444,400 445,407 447,414 449,420 451,425 452,429 454,432 456,435 457,437 459,438 461,438 462,437 464,436 466,434 468,432 469,429 471,425 473,421 474,417 476,413 478,408 479,403 481,399 483,394 484,390 486,386 488,382 490,378 491,376 493,373 495,371 496,370 498,370 500,370 501,371 503,372 505,374 506,376 508,379 510,383 512,386 513,390 515,394 517,399 518,403 520,407 522,412 523,416 525,419 527,423 528,426 530,428 532,430 534,432 535,433 537,433 539,433 540,432 542,430 544,427 545,424 547,420 549,415 550,410 552,404 554,398 556,391 557,384 559,376 561,368 562,360 564,351 566,343 567,335 569,328 571,320 573,313 574,307 576,301 578,296 579,291 581,288 583,285 584,283 586,282 588,281 589,281 591,282 593,283 595,285 596,288 598,291 600,294 601,297 603,301 605,305 606,309 608,313 610,317 611,321 613,324 615,328 617,332 618,335 620,338 622,341 623,344 625,347 627,350 628,352 630,354 632,356 633,358 635,359 637,361 639,362 640,363 642,364 644,365 645,365 647,366 649,366 650,366 652,366 654,366 655,366 657,365 659,365 661,365 662,364 664,364 666,363 667,363 669,363 671,362 672,362 674,362 676,362 677,362 679,362 681,363 683,363 684,363 686,363 688,363 689,363 691,362 693,361 694,359 696,357 698,354 700,351 701,346 703,341 705,335 706,328 708,321 710,312 711,303 713,293 715,282 716,270 718,258 720,246 722,234 723,221 725,208 727,196 728,183 730,171 732,160 733,149 735,139 737,130 738,122 740,114 742,108 744,103 745,98 747,95 749,93 750,92 752,92 754,93 755,96 757,99 759,103 760,108 762,115 764,121 766,129 767,138 769,147 771,157 772,168 774,179 776,190 777,202 779,214 781,226 782,239 784,251 786,263 788,275 789,287 791,298 793,309 794,320 796,330 798,339 799,348 801,357 803,364 804,371 806,378 808,384 810,389 811,394 813,398 815,401 816,405 818,407 820,410 821,412 823,413 825,415 827,416 828,417 830,417 832,418 833,419 835,419 837,419 838,420 840,420 842,420 843,421 845,421 847,422 849,423 850,423 852,424 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="609,473 609,315 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

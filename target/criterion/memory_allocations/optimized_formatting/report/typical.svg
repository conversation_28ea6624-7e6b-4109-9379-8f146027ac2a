<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
memory_allocations/optimized_formatting:typical
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="426" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,426 86,426 "/>
<text x="77" y="379" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,379 86,379 "/>
<text x="77" y="332" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,332 86,332 "/>
<text x="77" y="284" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,284 86,284 "/>
<text x="77" y="237" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,237 86,237 "/>
<text x="77" y="190" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,190 86,190 "/>
<text x="77" y="143" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.035
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,143 86,143 "/>
<text x="77" y="96" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.04
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,96 86,96 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="168" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
650
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="168,473 168,478 "/>
<text x="257" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
655
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="257,473 257,478 "/>
<text x="345" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
660
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="345,473 345,478 "/>
<text x="434" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
665
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="434,473 434,478 "/>
<text x="523" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
670
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="523,473 523,478 "/>
<text x="611" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
675
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="611,473 611,478 "/>
<text x="700" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
680
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="700,473 700,478 "/>
<text x="789" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
685
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="789,473 789,478 "/>
<text x="877" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
690
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="877,473 877,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,471 97,471 98,470 100,470 102,469 103,468 105,467 107,466 109,464 110,463 112,461 114,459 115,456 117,453 119,450 120,447 122,443 124,439 125,435 127,430 129,425 131,420 132,414 134,408 136,402 137,396 139,390 141,383 142,377 144,370 146,364 147,358 149,352 151,346 153,340 154,335 156,330 158,326 159,322 161,318 163,315 164,312 166,310 168,307 169,306 171,304 173,303 175,301 176,300 178,299 180,298 181,297 183,296 185,295 186,294 188,292 190,291 191,289 193,287 195,285 197,283 198,281 200,278 202,276 203,273 205,271 207,268 208,265 210,262 212,259 214,256 215,253 217,250 219,247 220,243 222,240 224,237 225,234 227,231 229,228 230,225 232,222 234,219 236,217 237,214 239,212 241,210 242,208 244,207 246,205 247,204 249,203 251,203 252,202 254,202 256,202 258,202 259,202 261,202 263,202 264,202 266,201 268,201 269,201 271,200 273,199 274,198 276,197 278,195 280,193 281,190 283,188 285,185 286,182 288,178 290,174 291,170 293,166 295,162 296,158 298,153 300,149 302,145 303,141 305,136 307,132 308,129 310,125 312,121 313,118 315,115 317,113 318,110 320,108 322,106 324,104 325,103 327,101 329,100 330,99 332,99 334,98 335,97 337,97 339,97 341,97 342,96 344,96 346,96 347,96 349,96 351,96 352,95 354,95 356,95 357,95 359,94 361,94 363,94 364,93 366,93 368,93 369,93 371,92 373,92 374,92 376,92 378,92 379,92 381,92 383,92 385,92 386,92 388,93 390,93 391,93 393,94 395,95 396,95 398,96 400,97 401,98 403,99 405,100 407,101 408,102 410,103 412,104 413,105 415,106 417,108 418,109 420,110 422,111 423,112 425,113 427,115 429,116 430,117 432,118 434,119 435,119 437,120 439,121 440,122 442,123 444,124 445,124 447,125 449,126 451,127 452,127 454,128 456,129 457,130 459,130 461,131 462,132 464,133 466,134 468,135 469,136 471,137 473,138 474,139 476,140 478,141 479,143 481,144 483,145 484,147 486,148 488,149 490,151 491,152 493,154 495,155 496,157 498,158 500,160 501,161 503,163 505,164 506,166 508,167 510,169 512,171 513,172 515,174 517,175 518,177 520,178 522,180 523,181 525,183 527,184 528,186 530,187 532,189 534,191 535,192 537,194 539,195 540,197 542,199 544,200 545,202 547,204 549,206 550,207 552,209 554,211 556,212 557,214 559,216 561,218 562,220 564,221 566,223 567,225 569,227 571,229 573,230 574,232 576,234 578,236 579,238 581,240 583,242 584,244 586,246 588,248 589,250 591,252 593,254 595,256 596,258 598,260 600,262 601,264 603,266 605,268 606,270 608,272 610,274 611,275 613,277 615,279 617,281 618,283 620,284 622,286 623,288 625,289 627,291 628,292 630,294 632,295 633,296 635,298 637,299 639,300 640,302 642,303 644,304 645,306 647,307 649,308 650,310 652,311 654,312 655,314 657,315 659,316 661,318 662,319 664,321 666,322 667,323 669,325 671,326 672,328 674,329 676,330 677,332 679,333 681,335 683,336 684,338 686,339 688,340 689,342 691,343 693,345 694,346 696,348 698,349 700,351 701,352 703,354 705,355 706,356 708,358 710,359 711,361 713,362 715,364 716,365 718,366 720,368 722,369 723,370 725,371 727,373 728,374 730,375 732,376 733,377 735,378 737,379 738,380 740,381 742,382 744,383 745,384 747,385 749,386 750,387 752,388 754,389 755,390 757,391 759,392 760,392 762,393 764,394 766,395 767,396 769,397 771,397 772,398 774,399 776,400 777,401 779,401 781,402 782,403 784,404 786,404 788,405 789,406 791,407 793,407 794,408 796,409 798,410 799,410 801,411 803,412 804,413 806,413 808,414 810,415 811,416 813,416 815,417 816,418 818,418 820,419 821,420 823,421 825,421 827,422 828,423 830,424 832,424 833,425 835,426 837,426 838,427 840,428 842,429 843,429 845,430 847,431 849,431 850,432 852,432 854,433 855,434 857,434 859,435 860,435 862,436 864,436 865,437 867,437 869,438 871,438 872,439 874,439 876,440 877,440 879,441 881,441 882,442 884,442 886,443 887,443 889,444 891,444 893,445 894,445 896,446 898,446 899,446 901,447 903,447 904,448 906,448 908,448 909,449 911,449 913,449 915,450 916,450 918,450 920,451 921,451 923,451 925,452 926,452 928,452 930,453 932,453 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,312 166,310 168,307 169,306 171,304 173,303 175,301 176,300 178,299 180,298 181,297 183,296 185,295 186,294 188,292 190,291 191,289 193,287 195,285 197,283 198,281 200,278 202,276 203,273 205,271 207,268 208,265 210,262 212,259 214,256 215,253 217,250 219,247 220,243 222,240 224,237 225,234 227,231 229,228 230,225 232,222 234,219 236,217 237,214 239,212 241,210 242,208 244,207 246,205 247,204 249,203 251,203 252,202 254,202 256,202 258,202 259,202 261,202 263,202 264,202 266,201 268,201 269,201 271,200 273,199 274,198 276,197 278,195 280,193 281,190 283,188 285,185 286,182 288,178 290,174 291,170 293,166 295,162 296,158 298,153 300,149 302,145 303,141 305,136 307,132 308,129 310,125 312,121 313,118 315,115 317,113 318,110 320,108 322,106 324,104 325,103 327,101 329,100 330,99 332,99 334,98 335,97 337,97 339,97 341,97 342,96 344,96 346,96 347,96 349,96 351,96 352,95 354,95 356,95 357,95 359,94 361,94 363,94 364,93 366,93 368,93 369,93 371,92 373,92 374,92 376,92 378,92 379,92 381,92 383,92 385,92 386,92 388,93 390,93 391,93 393,94 395,95 396,95 398,96 400,97 401,98 403,99 405,100 407,101 408,102 410,103 412,104 413,105 415,106 417,108 418,109 420,110 422,111 423,112 425,113 427,115 429,116 430,117 432,118 434,119 435,119 437,120 439,121 440,122 442,123 444,124 445,124 447,125 449,126 451,127 452,127 454,128 456,129 457,130 459,130 461,131 462,132 464,133 466,134 468,135 469,136 471,137 473,138 474,139 476,140 478,141 479,143 481,144 483,145 484,147 486,148 488,149 490,151 491,152 493,154 495,155 496,157 498,158 500,160 501,161 503,163 505,164 506,166 508,167 510,169 512,171 513,172 515,174 517,175 518,177 520,178 522,180 523,181 525,183 527,184 528,186 530,187 532,189 534,191 535,192 537,194 539,195 540,197 542,199 544,200 545,202 547,204 549,206 550,207 552,209 554,211 556,212 557,214 559,216 561,218 562,220 564,221 566,223 567,225 569,227 571,229 573,230 574,232 576,234 578,236 579,238 581,240 583,242 584,244 586,246 588,248 589,250 591,252 593,254 595,256 596,258 598,260 600,262 601,264 603,266 605,268 606,270 608,272 610,274 611,275 613,277 615,279 617,281 618,283 620,284 622,286 623,288 625,289 627,291 628,292 630,294 632,295 633,296 635,298 637,299 639,300 640,302 642,303 644,304 645,306 647,307 649,308 650,310 652,311 654,312 655,314 657,315 659,316 661,318 662,319 664,321 666,322 667,323 669,325 671,326 672,328 674,329 676,330 677,332 679,333 681,335 683,336 684,338 686,339 688,340 689,342 691,343 693,345 694,346 696,348 698,349 700,351 701,352 703,354 705,355 706,356 708,358 710,359 711,361 713,362 715,364 716,365 718,366 720,368 722,369 723,370 725,371 727,373 728,374 730,375 732,376 733,377 735,378 737,379 738,380 740,381 742,382 744,383 745,384 747,385 749,386 750,387 752,388 754,389 755,390 757,391 759,392 760,392 762,393 764,394 766,395 767,396 769,397 771,397 772,398 774,399 776,400 777,401 779,401 781,402 782,403 784,404 786,404 788,405 789,406 791,407 793,407 794,408 796,409 798,410 799,410 801,411 803,412 804,413 806,413 808,414 810,415 811,416 813,416 815,417 816,418 818,418 820,419 821,420 823,421 825,421 827,422 828,423 830,424 832,424 833,425 835,426 837,426 838,427 840,428 842,429 843,429 845,430 847,431 849,431 850,432 852,432 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="448,473 448,126 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

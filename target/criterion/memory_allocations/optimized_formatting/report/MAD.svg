<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
memory_allocations/optimized_formatting:MAD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="421" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.05
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,421 86,421 "/>
<text x="77" y="359" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.1
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,359 86,359 "/>
<text x="77" y="296" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.15
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,296 86,296 "/>
<text x="77" y="234" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.2
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,234 86,234 "/>
<text x="77" y="172" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.25
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,172 86,172 "/>
<text x="77" y="109" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.3
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,109 86,109 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="206" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
5
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="206,473 206,478 "/>
<text x="329" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
6
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="329,473 329,478 "/>
<text x="453" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
7
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="453,473 453,478 "/>
<text x="576" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
8
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="576,473 576,478 "/>
<text x="700" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
9
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="700,473 700,478 "/>
<text x="823" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
10
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="823,473 823,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,471 92,470 93,469 95,469 97,468 98,466 100,465 102,464 103,463 105,461 107,459 109,458 110,456 112,454 114,452 115,450 117,448 119,446 120,444 122,441 124,439 125,436 127,434 129,431 131,429 132,426 134,424 136,421 137,419 139,416 141,414 142,411 144,409 146,406 147,404 149,401 151,399 153,397 154,394 156,392 158,390 159,388 161,386 163,384 164,382 166,380 168,378 169,376 171,374 173,372 175,370 176,368 178,366 180,363 181,361 183,359 185,356 186,353 188,351 190,348 191,345 193,342 195,339 197,336 198,332 200,329 202,325 203,321 205,317 207,313 208,309 210,304 212,300 214,295 215,290 217,284 219,279 220,273 222,267 224,260 225,254 227,247 229,240 230,233 232,225 234,218 236,210 237,203 239,195 241,188 242,180 244,173 246,167 247,160 249,154 251,149 252,143 254,139 256,134 258,130 259,127 261,124 263,122 264,120 266,118 268,116 269,115 271,114 273,113 274,113 276,112 278,111 280,111 281,110 283,109 285,108 286,107 288,106 290,104 291,103 293,102 295,100 296,99 298,97 300,96 302,95 303,94 305,93 307,93 308,93 310,93 312,93 313,94 315,95 317,97 318,99 320,101 322,103 324,106 325,109 327,111 329,114 330,117 332,120 334,123 335,126 337,128 339,131 341,133 342,135 344,137 346,138 347,139 349,141 351,141 352,142 354,143 356,143 357,143 359,143 361,143 363,143 364,142 366,142 368,142 369,141 371,141 373,141 374,141 376,141 378,142 379,142 381,143 383,144 385,145 386,147 388,148 390,150 391,153 393,155 395,157 396,160 398,162 400,165 401,168 403,170 405,172 407,174 408,176 410,178 412,180 413,181 415,182 417,183 418,183 420,184 422,184 423,184 425,184 427,184 429,184 430,184 432,184 434,183 435,184 437,184 439,184 440,185 442,185 444,186 445,188 447,189 449,190 451,192 452,194 454,196 456,198 457,200 459,203 461,205 462,207 464,209 466,211 468,213 469,215 471,217 473,218 474,219 476,220 478,221 479,222 481,223 483,224 484,224 486,225 488,225 490,225 491,226 493,226 495,227 496,227 498,228 500,228 501,229 503,229 505,230 506,231 508,232 510,233 512,234 513,235 515,236 517,237 518,239 520,240 522,241 523,243 525,244 527,246 528,247 530,248 532,249 534,250 535,251 537,252 539,252 540,252 542,252 544,252 545,251 547,251 549,250 550,248 552,247 554,245 556,243 557,241 559,238 561,236 562,233 564,231 566,228 567,225 569,223 571,221 573,219 574,217 576,216 578,215 579,214 581,214 583,215 584,216 586,217 588,220 589,222 591,225 593,229 595,232 596,237 598,241 600,245 601,250 603,255 605,260 606,265 608,269 610,274 611,279 613,283 615,287 617,292 618,296 620,299 622,303 623,307 625,310 627,313 628,317 630,320 632,323 633,326 635,328 637,331 639,334 640,337 642,339 644,342 645,345 647,347 649,350 650,353 652,355 654,358 655,361 657,363 659,366 661,368 662,371 664,373 666,376 667,378 669,380 671,382 672,384 674,386 676,388 677,390 679,391 681,393 683,394 684,395 686,396 688,397 689,398 691,398 693,399 694,399 696,399 698,400 700,400 701,400 703,399 705,399 706,399 708,399 710,399 711,399 713,400 715,400 716,400 718,401 720,402 722,402 723,403 725,405 727,406 728,407 730,409 732,411 733,412 735,414 737,416 738,418 740,420 742,422 744,424 745,425 747,427 749,429 750,430 752,431 754,433 755,434 757,435 759,436 760,436 762,437 764,438 766,438 767,438 769,439 771,439 772,439 774,439 776,439 777,439 779,439 781,439 782,439 784,439 786,439 788,438 789,438 791,438 793,438 794,437 796,437 798,437 799,436 801,436 803,436 804,435 806,435 808,435 810,434 811,434 813,434 815,434 816,433 818,433 820,433 821,434 823,434 825,434 827,434 828,435 830,435 832,435 833,436 835,436 837,437 838,437 840,438 842,439 843,439 845,440 847,440 849,441 850,442 852,442 854,443 855,443 857,444 859,444 860,444 862,445 864,445 865,446 867,446 869,446 871,446 872,447 874,447 876,447 877,448 879,448 881,448 882,448 884,449 886,449 887,449 889,450 891,450 893,451 894,451 896,452 898,452 899,453 901,453 903,454 904,455 906,456 908,456 909,457 911,458 913,459 915,459 916,460 918,461 920,462 921,463 923,463 925,464 926,465 928,465 930,466 932,466 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,382 166,380 168,378 169,376 171,374 173,372 175,370 176,368 178,366 180,363 181,361 183,359 185,356 186,353 188,351 190,348 191,345 193,342 195,339 197,336 198,332 200,329 202,325 203,321 205,317 207,313 208,309 210,304 212,300 214,295 215,290 217,284 219,279 220,273 222,267 224,260 225,254 227,247 229,240 230,233 232,225 234,218 236,210 237,203 239,195 241,188 242,180 244,173 246,167 247,160 249,154 251,149 252,143 254,139 256,134 258,130 259,127 261,124 263,122 264,120 266,118 268,116 269,115 271,114 273,113 274,113 276,112 278,111 280,111 281,110 283,109 285,108 286,107 288,106 290,104 291,103 293,102 295,100 296,99 298,97 300,96 302,95 303,94 305,93 307,93 308,93 310,93 312,93 313,94 315,95 317,97 318,99 320,101 322,103 324,106 325,109 327,111 329,114 330,117 332,120 334,123 335,126 337,128 339,131 341,133 342,135 344,137 346,138 347,139 349,141 351,141 352,142 354,143 356,143 357,143 359,143 361,143 363,143 364,142 366,142 368,142 369,141 371,141 373,141 374,141 376,141 378,142 379,142 381,143 383,144 385,145 386,147 388,148 390,150 391,153 393,155 395,157 396,160 398,162 400,165 401,168 403,170 405,172 407,174 408,176 410,178 412,180 413,181 415,182 417,183 418,183 420,184 422,184 423,184 425,184 427,184 429,184 430,184 432,184 434,183 435,184 437,184 439,184 440,185 442,185 444,186 445,188 447,189 449,190 451,192 452,194 454,196 456,198 457,200 459,203 461,205 462,207 464,209 466,211 468,213 469,215 471,217 473,218 474,219 476,220 478,221 479,222 481,223 483,224 484,224 486,225 488,225 490,225 491,226 493,226 495,227 496,227 498,228 500,228 501,229 503,229 505,230 506,231 508,232 510,233 512,234 513,235 515,236 517,237 518,239 520,240 522,241 523,243 525,244 527,246 528,247 530,248 532,249 534,250 535,251 537,252 539,252 540,252 542,252 544,252 545,251 547,251 549,250 550,248 552,247 554,245 556,243 557,241 559,238 561,236 562,233 564,231 566,228 567,225 569,223 571,221 573,219 574,217 576,216 578,215 579,214 581,214 583,215 584,216 586,217 588,220 589,222 591,225 593,229 595,232 596,237 598,241 600,245 601,250 603,255 605,260 606,265 608,269 610,274 611,279 613,283 615,287 617,292 618,296 620,299 622,303 623,307 625,310 627,313 628,317 630,320 632,323 633,326 635,328 637,331 639,334 640,337 642,339 644,342 645,345 647,347 649,350 650,353 652,355 654,358 655,361 657,363 659,366 661,368 662,371 664,373 666,376 667,378 669,380 671,382 672,384 674,386 676,388 677,390 679,391 681,393 683,394 684,395 686,396 688,397 689,398 691,398 693,399 694,399 696,399 698,400 700,400 701,400 703,399 705,399 706,399 708,399 710,399 711,399 713,400 715,400 716,400 718,401 720,402 722,402 723,403 725,405 727,406 728,407 730,409 732,411 733,412 735,414 737,416 738,418 740,420 742,422 744,424 745,425 747,427 749,429 750,430 752,431 754,433 755,434 757,435 759,436 760,436 762,437 764,438 766,438 767,438 769,439 771,439 772,439 774,439 776,439 777,439 779,439 781,439 782,439 784,439 786,439 788,438 789,438 791,438 793,438 794,437 796,437 798,437 799,436 801,436 803,436 804,435 806,435 808,435 810,434 811,434 813,434 815,434 816,433 818,433 820,433 821,434 823,434 825,434 827,434 828,435 830,435 832,435 833,436 835,436 837,437 838,437 840,438 842,439 843,439 845,440 847,440 849,441 850,442 852,442 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="416,473 416,183 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

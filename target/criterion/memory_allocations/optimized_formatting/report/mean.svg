<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
memory_allocations/optimized_formatting:mean
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="416" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.005
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,416 86,416 "/>
<text x="77" y="360" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,360 86,360 "/>
<text x="77" y="303" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.015
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,303 86,303 "/>
<text x="77" y="247" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.02
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,247 86,247 "/>
<text x="77" y="190" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.025
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,190 86,190 "/>
<text x="77" y="134" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.03
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,134 86,134 "/>
<text x="77" y="77" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.035
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,77 86,77 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="145" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
650
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="145,473 145,478 "/>
<text x="270" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
660
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="270,473 270,478 "/>
<text x="396" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
670
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="396,473 396,478 "/>
<text x="521" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
680
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="521,473 521,478 "/>
<text x="646" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
690
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="646,473 646,478 "/>
<text x="772" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
700
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="772,473 772,478 "/>
<text x="897" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
710
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="897,473 897,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,471 97,471 98,471 100,470 102,469 103,469 105,468 107,467 109,466 110,464 112,462 114,461 115,458 117,456 119,453 120,450 122,447 124,443 125,439 127,434 129,429 131,423 132,417 134,411 136,404 137,397 139,390 141,382 142,374 144,365 146,357 147,348 149,339 151,330 153,321 154,311 156,302 158,293 159,284 161,275 163,266 164,257 166,249 168,241 169,233 171,225 173,218 175,211 176,204 178,197 180,191 181,185 183,179 185,173 186,168 188,163 190,158 191,153 193,149 195,144 197,140 198,136 200,133 202,129 203,126 205,124 207,121 208,119 210,117 212,116 214,115 215,114 217,114 219,114 220,114 222,115 224,116 225,117 227,118 229,120 230,123 232,125 234,128 236,131 237,134 239,137 241,141 242,144 244,148 246,152 247,156 249,160 251,165 252,169 254,174 256,178 258,183 259,187 261,192 263,197 264,202 266,207 268,212 269,217 271,222 273,227 274,232 276,237 278,242 280,247 281,252 283,256 285,261 286,265 288,270 290,274 291,277 293,281 295,284 296,287 298,289 300,291 302,293 303,294 305,295 307,295 308,295 310,294 312,293 313,291 315,289 317,286 318,282 320,278 322,274 324,269 325,264 327,258 329,252 330,246 332,240 334,233 335,227 337,220 339,213 341,206 342,200 344,193 346,187 347,180 349,174 351,169 352,163 354,157 356,152 357,147 359,143 361,138 363,134 364,130 366,126 368,122 369,119 371,116 373,113 374,110 376,107 378,105 379,102 381,100 383,98 385,96 386,95 388,94 390,93 391,92 393,92 395,92 396,92 398,92 400,93 401,94 403,96 405,98 407,100 408,102 410,105 412,108 413,111 415,114 417,118 418,121 420,125 422,129 423,133 425,138 427,142 429,147 430,151 432,156 434,161 435,166 437,170 439,175 440,180 442,185 444,190 445,196 447,201 449,206 451,211 452,216 454,221 456,227 457,232 459,237 461,242 462,247 464,252 466,257 468,262 469,267 471,272 473,277 474,281 476,285 478,289 479,293 481,297 483,300 484,303 486,306 488,309 490,311 491,313 493,315 495,316 496,317 498,318 500,318 501,318 503,318 505,318 506,317 508,316 510,315 512,314 513,312 515,311 517,309 518,307 520,305 522,304 523,302 525,300 527,298 528,296 530,294 532,293 534,291 535,290 537,288 539,287 540,285 542,284 544,283 545,282 547,281 549,280 550,279 552,278 554,277 556,277 557,276 559,275 561,275 562,274 564,273 566,273 567,273 569,273 571,272 573,272 574,272 576,273 578,273 579,273 581,274 583,275 584,275 586,276 588,278 589,279 591,280 593,282 595,283 596,285 598,287 600,288 601,290 603,293 605,295 606,297 608,299 610,302 611,304 613,306 615,309 617,312 618,314 620,317 622,320 623,323 625,325 627,328 628,331 630,334 632,337 633,340 635,343 637,346 639,349 640,352 642,355 644,358 645,361 647,364 649,367 650,369 652,372 654,375 655,377 657,380 659,382 661,384 662,386 664,389 666,390 667,392 669,394 671,396 672,397 674,399 676,400 677,401 679,402 681,403 683,404 684,404 686,405 688,405 689,406 691,406 693,406 694,406 696,406 698,406 700,406 701,406 703,406 705,406 706,406 708,406 710,406 711,406 713,405 715,405 716,405 718,405 720,405 722,405 723,404 725,404 727,404 728,404 730,404 732,404 733,404 735,403 737,403 738,403 740,403 742,403 744,403 745,403 747,402 749,402 750,402 752,402 754,402 755,402 757,403 759,403 760,403 762,403 764,403 766,404 767,404 769,405 771,405 772,406 774,406 776,407 777,408 779,408 781,409 782,410 784,411 786,412 788,412 789,413 791,414 793,415 794,416 796,417 798,418 799,419 801,420 803,421 804,421 806,422 808,423 810,424 811,425 813,426 815,427 816,428 818,429 820,430 821,431 823,432 825,433 827,434 828,435 830,436 832,437 833,438 835,439 837,440 838,441 840,442 842,443 843,444 845,445 847,445 849,446 850,447 852,447 854,448 855,449 857,449 859,450 860,450 862,451 864,451 865,451 867,452 869,452 871,452 872,453 874,453 876,453 877,453 879,453 881,453 882,454 884,454 886,454 887,454 889,454 891,454 893,454 894,454 896,454 898,454 899,454 901,454 903,454 904,454 906,454 908,454 909,454 911,454 913,454 915,454 916,454 918,454 920,454 921,454 923,454 925,454 926,454 928,454 930,454 932,454 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,257 166,249 168,241 169,233 171,225 173,218 175,211 176,204 178,197 180,191 181,185 183,179 185,173 186,168 188,163 190,158 191,153 193,149 195,144 197,140 198,136 200,133 202,129 203,126 205,124 207,121 208,119 210,117 212,116 214,115 215,114 217,114 219,114 220,114 222,115 224,116 225,117 227,118 229,120 230,123 232,125 234,128 236,131 237,134 239,137 241,141 242,144 244,148 246,152 247,156 249,160 251,165 252,169 254,174 256,178 258,183 259,187 261,192 263,197 264,202 266,207 268,212 269,217 271,222 273,227 274,232 276,237 278,242 280,247 281,252 283,256 285,261 286,265 288,270 290,274 291,277 293,281 295,284 296,287 298,289 300,291 302,293 303,294 305,295 307,295 308,295 310,294 312,293 313,291 315,289 317,286 318,282 320,278 322,274 324,269 325,264 327,258 329,252 330,246 332,240 334,233 335,227 337,220 339,213 341,206 342,200 344,193 346,187 347,180 349,174 351,169 352,163 354,157 356,152 357,147 359,143 361,138 363,134 364,130 366,126 368,122 369,119 371,116 373,113 374,110 376,107 378,105 379,102 381,100 383,98 385,96 386,95 388,94 390,93 391,92 393,92 395,92 396,92 398,92 400,93 401,94 403,96 405,98 407,100 408,102 410,105 412,108 413,111 415,114 417,118 418,121 420,125 422,129 423,133 425,138 427,142 429,147 430,151 432,156 434,161 435,166 437,170 439,175 440,180 442,185 444,190 445,196 447,201 449,206 451,211 452,216 454,221 456,227 457,232 459,237 461,242 462,247 464,252 466,257 468,262 469,267 471,272 473,277 474,281 476,285 478,289 479,293 481,297 483,300 484,303 486,306 488,309 490,311 491,313 493,315 495,316 496,317 498,318 500,318 501,318 503,318 505,318 506,317 508,316 510,315 512,314 513,312 515,311 517,309 518,307 520,305 522,304 523,302 525,300 527,298 528,296 530,294 532,293 534,291 535,290 537,288 539,287 540,285 542,284 544,283 545,282 547,281 549,280 550,279 552,278 554,277 556,277 557,276 559,275 561,275 562,274 564,273 566,273 567,273 569,273 571,272 573,272 574,272 576,273 578,273 579,273 581,274 583,275 584,275 586,276 588,278 589,279 591,280 593,282 595,283 596,285 598,287 600,288 601,290 603,293 605,295 606,297 608,299 610,302 611,304 613,306 615,309 617,312 618,314 620,317 622,320 623,323 625,325 627,328 628,331 630,334 632,337 633,340 635,343 637,346 639,349 640,352 642,355 644,358 645,361 647,364 649,367 650,369 652,372 654,375 655,377 657,380 659,382 661,384 662,386 664,389 666,390 667,392 669,394 671,396 672,397 674,399 676,400 677,401 679,402 681,403 683,404 684,404 686,405 688,405 689,406 691,406 693,406 694,406 696,406 698,406 700,406 701,406 703,406 705,406 706,406 708,406 710,406 711,406 713,405 715,405 716,405 718,405 720,405 722,405 723,404 725,404 727,404 728,404 730,404 732,404 733,404 735,403 737,403 738,403 740,403 742,403 744,403 745,403 747,402 749,402 750,402 752,402 754,402 755,402 757,403 759,403 760,403 762,403 764,403 766,404 767,404 769,405 771,405 772,406 774,406 776,407 777,408 779,408 781,409 782,410 784,411 786,412 788,412 789,413 791,414 793,415 794,416 796,417 798,418 799,419 801,420 803,421 804,421 806,422 808,423 810,424 811,425 813,426 815,427 816,428 818,429 820,430 821,431 823,432 825,433 827,434 828,435 830,436 832,437 833,438 835,439 837,440 838,441 840,442 842,443 843,444 845,445 847,445 849,446 850,447 852,447 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="418,473 418,120 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

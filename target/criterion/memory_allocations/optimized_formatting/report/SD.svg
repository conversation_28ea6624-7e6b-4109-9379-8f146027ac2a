<svg width="960" height="540" viewBox="0 0 960 540" xmlns="http://www.w3.org/2000/svg">
<text x="480" y="32" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="16.129032258064516" opacity="1" fill="#000000">
memory_allocations/optimized_formatting:SD
</text>
<text x="27" y="263" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000" transform="rotate(270, 27, 263)">
Density (a.u.)
</text>
<text x="510" y="513" dy="-0.5ex" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Average time (ns)
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="86,53 86,472 "/>
<text x="77" y="425" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.002
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,425 86,425 "/>
<text x="77" y="377" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.004
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,377 86,377 "/>
<text x="77" y="329" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.006
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,329 86,329 "/>
<text x="77" y="281" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.008
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,281 86,281 "/>
<text x="77" y="233" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.01
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,233 86,233 "/>
<text x="77" y="185" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.012
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,185 86,185 "/>
<text x="77" y="137" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.014
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,137 86,137 "/>
<text x="77" y="89" dy="0.5ex" text-anchor="end" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0.016
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="81,89 86,89 "/>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="87,473 932,473 "/>
<text x="136" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
0
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="136,473 136,478 "/>
<text x="277" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
50
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="277,473 277,478 "/>
<text x="418" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
100
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="418,473 418,478 "/>
<text x="559" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
150
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="559,473 559,478 "/>
<text x="700" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
200
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="700,473 700,478 "/>
<text x="842" y="483" dy="0.76em" text-anchor="middle" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
250
</text>
<polyline fill="none" opacity="1" stroke="#000000" stroke-width="1" points="842,473 842,478 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="87,472 88,472 90,472 92,472 93,472 95,472 97,472 98,472 100,471 102,471 103,471 105,470 107,470 109,469 110,469 112,468 114,467 115,466 117,465 119,464 120,463 122,461 124,459 125,458 127,456 129,454 131,451 132,449 134,446 136,444 137,441 139,438 141,435 142,431 144,428 146,425 147,421 149,418 151,415 153,411 154,408 156,405 158,402 159,398 161,395 163,393 164,390 166,387 168,385 169,382 171,380 173,378 175,376 176,374 178,373 180,371 181,370 183,369 185,368 186,367 188,366 190,366 191,365 193,365 195,365 197,365 198,364 200,365 202,365 203,365 205,365 207,365 208,365 210,365 212,365 214,365 215,365 217,364 219,363 220,362 222,361 224,360 225,358 227,356 229,354 230,351 232,348 234,345 236,342 237,338 239,335 241,331 242,327 244,324 246,320 247,317 249,314 251,310 252,308 254,305 256,303 258,301 259,300 261,299 263,298 264,298 266,298 268,299 269,300 271,301 273,302 274,304 276,306 278,308 280,310 281,313 283,315 285,318 286,321 288,324 290,326 291,329 293,332 295,335 296,338 298,341 300,344 302,347 303,350 305,353 307,356 308,359 310,363 312,366 313,369 315,372 317,376 318,379 320,382 322,385 324,389 325,392 327,395 329,398 330,401 332,404 334,408 335,411 337,413 339,416 341,419 342,422 344,424 346,427 347,429 349,432 351,434 352,436 354,439 356,441 357,443 359,444 361,446 363,448 364,450 366,451 368,453 369,454 371,455 373,457 374,458 376,459 378,460 379,461 381,462 383,463 385,464 386,465 388,465 390,466 391,466 393,467 395,468 396,468 398,468 400,469 401,469 403,470 405,470 407,470 408,470 410,471 412,471 413,471 415,471 417,471 418,472 420,472 422,472 423,472 425,472 427,472 429,472 430,472 432,472 434,472 435,472 437,472 439,472 440,472 442,472 444,472 445,472 447,472 449,472 451,472 452,472 454,472 456,472 457,472 459,472 461,472 462,472 464,472 466,472 468,472 469,472 471,472 473,472 474,472 476,472 478,471 479,471 481,470 483,470 484,469 486,468 488,468 490,466 491,465 493,463 495,462 496,459 498,457 500,454 501,450 503,446 505,442 506,437 508,431 510,425 512,418 513,410 515,401 517,392 518,382 520,371 522,360 523,347 525,334 527,321 528,306 530,292 532,277 534,261 535,246 537,231 539,215 540,200 542,186 544,172 545,158 547,146 549,134 550,124 552,115 554,107 556,101 557,96 559,93 561,92 562,92 564,93 566,96 567,101 569,107 571,114 573,123 574,133 576,144 578,155 579,168 581,181 583,195 584,209 586,223 588,237 589,252 591,266 593,280 595,294 596,307 598,320 600,332 601,344 603,356 605,366 606,376 608,386 610,394 611,402 613,410 615,417 617,423 618,429 620,434 622,439 623,443 625,447 627,450 628,453 630,456 632,458 633,460 635,462 637,463 639,464 640,466 642,466 644,467 645,468 647,468 649,468 650,468 652,468 654,468 655,468 657,467 659,466 661,465 662,464 664,463 666,461 667,459 669,457 671,454 672,451 674,447 676,444 677,439 679,435 681,429 683,424 684,418 686,411 688,404 689,397 691,389 693,381 694,373 696,364 698,356 700,347 701,338 703,330 705,322 706,314 708,306 710,299 711,292 713,286 715,281 716,277 718,274 720,271 722,270 723,269 725,269 727,271 728,273 730,277 732,281 733,286 735,291 737,298 738,305 740,312 742,320 744,328 745,336 747,345 749,353 750,362 752,370 754,378 755,386 757,394 759,401 760,408 762,414 764,420 766,426 767,431 769,436 771,440 772,444 774,447 776,450 777,453 779,456 781,458 782,459 784,461 786,462 788,463 789,463 791,463 793,463 794,463 796,463 798,462 799,461 801,460 803,459 804,457 806,456 808,454 810,452 811,449 813,447 815,444 816,442 818,439 820,436 821,433 823,430 825,427 827,424 828,421 830,418 832,415 833,413 835,410 837,408 838,406 840,405 842,404 843,403 845,402 847,402 849,402 850,403 852,404 854,405 855,407 857,408 859,411 860,413 862,415 864,418 865,421 867,424 869,427 871,430 872,433 874,436 876,439 877,442 879,444 881,447 882,450 884,452 886,454 887,456 889,458 891,459 893,461 894,462 896,463 898,464 899,465 901,465 903,466 904,466 906,466 908,466 909,466 911,466 913,466 915,466 916,465 918,465 920,464 921,463 923,463 925,462 926,461 928,461 930,460 932,459 "/>
<polygon opacity="0.25" fill="#1F78B4" points="164,390 166,387 168,385 169,382 171,380 173,378 175,376 176,374 178,373 180,371 181,370 183,369 185,368 186,367 188,366 190,366 191,365 193,365 195,365 197,365 198,364 200,365 202,365 203,365 205,365 207,365 208,365 210,365 212,365 214,365 215,365 217,364 219,363 220,362 222,361 224,360 225,358 227,356 229,354 230,351 232,348 234,345 236,342 237,338 239,335 241,331 242,327 244,324 246,320 247,317 249,314 251,310 252,308 254,305 256,303 258,301 259,300 261,299 263,298 264,298 266,298 268,299 269,300 271,301 273,302 274,304 276,306 278,308 280,310 281,313 283,315 285,318 286,321 288,324 290,326 291,329 293,332 295,335 296,338 298,341 300,344 302,347 303,350 305,353 307,356 308,359 310,363 312,366 313,369 315,372 317,376 318,379 320,382 322,385 324,389 325,392 327,395 329,398 330,401 332,404 334,408 335,411 337,413 339,416 341,419 342,422 344,424 346,427 347,429 349,432 351,434 352,436 354,439 356,441 357,443 359,444 361,446 363,448 364,450 366,451 368,453 369,454 371,455 373,457 374,458 376,459 378,460 379,461 381,462 383,463 385,464 386,465 388,465 390,466 391,466 393,467 395,468 396,468 398,468 400,469 401,469 403,470 405,470 407,470 408,470 410,471 412,471 413,471 415,471 417,471 418,472 420,472 422,472 423,472 425,472 427,472 429,472 430,472 432,472 434,472 435,472 437,472 439,472 440,472 442,472 444,472 445,472 447,472 449,472 451,472 452,472 454,472 456,472 457,472 459,472 461,472 462,472 464,472 466,472 468,472 469,472 471,472 473,472 474,472 476,472 478,471 479,471 481,470 483,470 484,469 486,468 488,468 490,466 491,465 493,463 495,462 496,459 498,457 500,454 501,450 503,446 505,442 506,437 508,431 510,425 512,418 513,410 515,401 517,392 518,382 520,371 522,360 523,347 525,334 527,321 528,306 530,292 532,277 534,261 535,246 537,231 539,215 540,200 542,186 544,172 545,158 547,146 549,134 550,124 552,115 554,107 556,101 557,96 559,93 561,92 562,92 564,93 566,96 567,101 569,107 571,114 573,123 574,133 576,144 578,155 579,168 581,181 583,195 584,209 586,223 588,237 589,252 591,266 593,280 595,294 596,307 598,320 600,332 601,344 603,356 605,366 606,376 608,386 610,394 611,402 613,410 615,417 617,423 618,429 620,434 622,439 623,443 625,447 627,450 628,453 630,456 632,458 633,460 635,462 637,463 639,464 640,466 642,466 644,467 645,468 647,468 649,468 650,468 652,468 654,468 655,468 657,467 659,466 661,465 662,464 664,463 666,461 667,459 669,457 671,454 672,451 674,447 676,444 677,439 679,435 681,429 683,424 684,418 686,411 688,404 689,397 691,389 693,381 694,373 696,364 698,356 700,347 701,338 703,330 705,322 706,314 708,306 710,299 711,292 713,286 715,281 716,277 718,274 720,271 722,270 723,269 725,269 727,271 728,273 730,277 732,281 733,286 735,291 737,298 738,305 740,312 742,320 744,328 745,336 747,345 749,353 750,362 752,370 754,378 755,386 757,394 759,401 760,408 762,414 764,420 766,426 767,431 769,436 771,440 772,444 774,447 776,450 777,453 779,456 781,458 782,459 784,461 786,462 788,463 789,463 791,463 793,463 794,463 796,463 798,462 799,461 801,460 803,459 804,457 806,456 808,454 810,452 811,449 813,447 815,444 816,442 818,439 820,436 821,433 823,430 825,427 827,424 828,421 830,418 832,415 833,413 835,410 837,408 838,406 840,405 842,404 843,403 845,402 847,402 849,402 850,403 852,404 852,473 164,473 "/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="3" points="563,473 563,92 "/>
<text x="798" y="68" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Bootstrap distribution
</text>
<text x="798" y="83" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Confidence interval
</text>
<text x="798" y="98" dy="0.76em" text-anchor="start" font-family="sans-serif" font-size="9.67741935483871" opacity="1" fill="#000000">
Point estimate
</text>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,73 788,73 "/>
<rect x="768" y="83" width="20" height="10" opacity="0.25" fill="#1F78B4" stroke="none"/>
<polyline fill="none" opacity="1" stroke="#1F78B4" stroke-width="1" points="768,103 788,103 "/>
</svg>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Index - Criterion.rs</title>
    <style type="text/css">
        body {
            font: 14px Helvetica Neue;
            text-rendering: optimizelegibility;
        }

        .body {
            width: 960px;
            margin: auto;
        }

        a:link {
            color: #1F78B4;
            text-decoration: none;
        }

        h2 {
            font-size: 36px;
            font-weight: 300;
        }

        h3 {
            font-size: 24px;
            font-weight: 300;
        }

        #footer {
            height: 40px;
            background: #888;
            color: white;
            font-size: larger;
            font-weight: 300;
        }

        #footer a {
            color: white;
            text-decoration: underline;
        }

        #footer p {
            text-align: center
        }

        table {
            border-collapse: collapse;
        }

        table,
        th,
        td {
            border: 1px solid #888;
        }
    </style>
</head>

<body>
    <div class="body">
        <h2>Criterion.rs Benchmark Index</h2>
        See individual benchmark pages below for more details.
        <ul>
            <li><a href="../log_formatter/report/index.html">log_formatter</a></li>
            
            <ul>
                <li>
                    <table>
                        <tr>
                            <th></th>
                            <th><a href="../log_formatter/format_entry/report/index.html">format_entry</a></th>
                        </tr>
                        <tr>
                            <th>long_message</th>
                            <td><a href="../log_formatter/format_entry/long_message/report/index.html">log_formatter/format_entry/long_message</a></td>
                        </tr>
                        <tr>
                            <th>simple</th>
                            <td><a href="../log_formatter/format_entry/simple/report/index.html">log_formatter/format_entry/simple</a></td>
                        </tr>
                        <tr>
                            <th>with_args</th>
                            <td><a href="../log_formatter/format_entry/with_args/report/index.html">log_formatter/format_entry/with_args</a></td>
                        </tr>
                    </table>
                </li>
            </ul>
            <li><a href="../log_processor/report/index.html">log_processor</a></li>
            
            <ul>
                <li>
                    <table>
                        <tr>
                            <th></th>
                            <th><a href="../log_processor/process_log_entry/report/index.html">process_log_entry</a></th>
                        </tr>
                        <tr>
                            <th>50</th>
                            <td><a href="../log_processor/process_log_entry/50/report/index.html">log_processor/process_log_entry/50</a></td>
                        </tr>
                        <tr>
                            <th>200</th>
                            <td><a href="../log_processor/process_log_entry/200/report/index.html">log_processor/process_log_entry/200</a></td>
                        </tr>
                        <tr>
                            <th>500</th>
                            <td><a href="../log_processor/process_log_entry/500/report/index.html">log_processor/process_log_entry/500</a></td>
                        </tr>
                        <tr>
                            <th>1000</th>
                            <td><a href="../log_processor/process_log_entry/1000/report/index.html">log_processor/process_log_entry/1000</a></td>
                        </tr>
                    </table>
                </li>
            </ul>
            <li>memory_allocations</li>
            
            <ul>
                <li><a href="../memory_allocations/optimized_formatting/report/index.html">optimized_formatting</a></li>
            </ul>
            <li><a href="../rate_limiting/report/index.html">rate_limiting</a></li>
            
            <ul>
                <li>
                    <table>
                        <tr>
                            <th></th>
                            <th><a href="../rate_limiting/rate_limit/report/index.html">rate_limit</a></th>
                        </tr>
                        <tr>
                            <th>0</th>
                            <td><a href="../rate_limiting/rate_limit/0/report/index.html">rate_limiting/rate_limit/0</a></td>
                        </tr>
                        <tr>
                            <th>10</th>
                            <td><a href="../rate_limiting/rate_limit/10/report/index.html">rate_limiting/rate_limit/10</a></td>
                        </tr>
                        <tr>
                            <th>100</th>
                            <td><a href="../rate_limiting/rate_limit/100/report/index.html">rate_limiting/rate_limit/100</a></td>
                        </tr>
                        <tr>
                            <th>1000</th>
                            <td><a href="../rate_limiting/rate_limit/1000/report/index.html">rate_limiting/rate_limit/1000</a></td>
                        </tr>
                    </table>
                </li>
            </ul>
            <li><a href="../sensitive_data/report/index.html">sensitive_data</a></li>
            
            <ul>
                <li>
                    <table>
                        <tr>
                            <th></th>
                            <th><a href="../sensitive_data/process_sensitive/report/index.html">process_sensitive</a></th>
                        </tr>
                        <tr>
                            <th>email_only</th>
                            <td><a href="../sensitive_data/process_sensitive/email_only/report/index.html">sensitive_data/process_sensitive/email_only</a></td>
                        </tr>
                        <tr>
                            <th>id_only</th>
                            <td><a href="../sensitive_data/process_sensitive/id_only/report/index.html">sensitive_data/process_sensitive/id_only</a></td>
                        </tr>
                        <tr>
                            <th>mixed</th>
                            <td><a href="../sensitive_data/process_sensitive/mixed/report/index.html">sensitive_data/process_sensitive/mixed</a></td>
                        </tr>
                        <tr>
                            <th>multiple_phones</th>
                            <td><a href="../sensitive_data/process_sensitive/multiple_phones/report/index.html">sensitive_data/process_sensitive/multiple_phones</a></td>
                        </tr>
                        <tr>
                            <th>no_sensitive</th>
                            <td><a href="../sensitive_data/process_sensitive/no_sensitive/report/index.html">sensitive_data/process_sensitive/no_sensitive</a></td>
                        </tr>
                        <tr>
                            <th>phone_only</th>
                            <td><a href="../sensitive_data/process_sensitive/phone_only/report/index.html">sensitive_data/process_sensitive/phone_only</a></td>
                        </tr>
                    </table>
                </li>
            </ul>
            <li><a href="../simplified_processor/report/index.html">simplified_processor</a></li>
            
            <ul>
                <li>
                    <table>
                        <tr>
                            <th></th>
                            <th><a href="../simplified_processor/process_entry/report/index.html">process_entry</a></th>
                        </tr>
                        <tr>
                            <th>complex</th>
                            <td><a href="../simplified_processor/process_entry/complex/report/index.html">simplified_processor/process_entry/complex</a></td>
                        </tr>
                        <tr>
                            <th>simple</th>
                            <td><a href="../simplified_processor/process_entry/simple/report/index.html">simplified_processor/process_entry/simple</a></td>
                        </tr>
                        <tr>
                            <th>with_sensitive_data</th>
                            <td><a href="../simplified_processor/process_entry/with_sensitive_data/report/index.html">simplified_processor/process_entry/with_sensitive_data</a></td>
                        </tr>
                    </table>
                </li>
            </ul>
        </ul>
    </div>
    <div id="footer">
        <p>This report was generated by
            <a href="https://github.com/bheisler/criterion.rs">Criterion.rs</a>, a statistics-driven benchmarking
            library in Rust.</p>
    </div>
</body>
</html>
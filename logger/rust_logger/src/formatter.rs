use chrono::{Utc, Local};
use uuid::Uuid;
use std::borrow::Cow;
use std::collections::HashMap;
use std::sync::Arc;
use prost::Message;

use crate::{
    config::LoggerConfig,
    core::LogEntry,
    error::{LoggerError, Result},
    protobuf::LogMetaData,
};

/// 日志格式化器（优化：使用Arc减少字符串clone）
pub struct LogFormatter {
    session_id: Arc<String>,
}

impl LogFormatter {
    /// 创建新的格式化器
    pub fn new() -> Self {
        Self {
            session_id: Arc::new(Uuid::new_v4().to_string()),
        }
    }
    
    /// 初始化格式化器
    pub fn initialize(&mut self, config: &LoggerConfig) -> Result<()> {
        // 如果配置中有非空的session_id，使用配置的；否则保持formatter自己生成的UUID
        if !config.session_id.is_empty() {
            self.session_id = Arc::new(config.session_id.clone());
            crate::internal_debug!("FORMATTER", "Using external session_id: {}", self.session_id);
        } else {
            crate::internal_debug!("FORMATTER", "Using internal generated session_id: {}", self.session_id);
        }

        Ok(())
    }
    
    /// 格式化日志条目
    /// 格式："[时间戳]+[sessionID]+[deviceID]+[模块信息]+[LogLevel]+[TestMode]+[userId]+[日志信息]"
    /// 空字段不输出[]
    pub fn format_entry(&self, entry: &LogEntry, config: &LoggerConfig) -> Result<String> {
        let mut formatted = String::new();

        // [时间戳] - 与Android格式保持一致：2025-06-23-18:25:23.027000+0800
        let local_time = entry.timestamp.with_timezone(&Local);
        let timestamp = local_time.format("%Y-%m-%d-%H:%M:%S%.6f%z");
        formatted.push_str(&format!("[{}]", timestamp));

        // [sessionID] - 只有非空时才输出
        if !self.session_id.is_empty() {
            formatted.push_str(&format!("[{}]", self.session_id));
        }

        // [deviceID] - 只有非空且不是UNKNOWN时才输出
        if let Some(device_id) = config.device_id.as_ref() {
            if !device_id.is_empty() && device_id != "UNKNOWN" {
                formatted.push_str(&format!("[{}]", device_id));
            }
        }

        // [模块信息] - 只有非空时才输出
        if !entry.tag.is_empty() {
            formatted.push_str(&format!("[{}]", entry.tag));
        }

        // [LogLevel] - 总是输出
        formatted.push_str(&format!("[{}]", entry.level.as_str()));

        // [TestMode] - 总是输出，与Android格式保持一致
        // Android使用isOpenDebug()判断：mLogSettings.isOpenDebug() ? "测试模式" : "正常模式"
        // 对应我们的is_debug_mode字段
        let test_mode_str = if config.is_debug_mode { "测试模式" } else { "正常模式" };
        formatted.push_str(&format!("[{}]", test_mode_str));

        // [userId] - 只有非空且不是UNKNOWN时才输出
        if !config.user_id.is_empty() && config.user_id != "UNKNOWN" && config.user_id != "0" {
            formatted.push_str(&format!("[{}]", config.user_id));
        }

        // 日志信息 - 总是输出，与Android格式保持一致（不加方括号）
        let content = self.build_content(entry)?;
        formatted.push_str(&content);

        Ok(formatted)
    }
    

    
    /// 构建日志内容
    /// 使用Cow优化性能：无参数时避免clone，有参数时才分配新字符串
    fn build_content<'a>(&self, entry: &'a LogEntry) -> Result<Cow<'a, str>> {
        // 如果没有参数，直接返回引用，避免clone
        if entry.args.is_empty() {
            return Ok(Cow::Borrowed(&entry.message));
        }

        // 有参数时才clone并替换占位符
        let mut content = entry.message.clone();
        let mut arg_index = 0;
        while content.contains("{}") && arg_index < entry.args.len() {
            content = content.replacen("{}", &entry.args[arg_index], 1);
            arg_index += 1;
        }

        Ok(Cow::Owned(content))
    }


    

    
    /// 格式化用于上传的日志
    pub fn format_for_upload(&self, entries: &[LogEntry], config: &LoggerConfig) -> Result<String> {
        let mut upload_content = String::new();
        
        // 添加上传头部信息
        upload_content.push_str(&self.build_upload_header(config)?);
        upload_content.push('\n');
        
        // 添加所有日志条目
        for entry in entries {
            let formatted_entry = self.format_entry(entry, config)?;
            upload_content.push_str(&formatted_entry);
            upload_content.push('\n');
        }
        
        Ok(upload_content)
    }
    
    /// 构建上传头部信息
    fn build_upload_header(&self, config: &LoggerConfig) -> Result<String> {
        let mut header = HashMap::new();

        // 使用配置中的device_id
        let default_device_id = "unknown_device".to_string();
        let device_id = config.device_id.as_ref().unwrap_or(&default_device_id);
        header.insert("device_id", device_id.clone());
        header.insert("session_id", (*self.session_id).clone());
        let default_app_version = "unknown".to_string();
        let app_version = config.app_version.as_ref().unwrap_or(&default_app_version);
        header.insert("app_version", app_version.clone());
        header.insert("platform", Self::get_platform_name().to_string());
        header.insert("upload_time", Utc::now().to_rfc3339());

        if !config.user_id.is_empty() {
            header.insert("user_id", config.user_id.clone());
        }

        serde_json::to_string(&header).map_err(LoggerError::from)
    }
    
    /// 获取平台名称（优化：使用静态字符串避免内存分配）
    fn get_platform_name() -> &'static str {
        #[cfg(target_os = "android")]
        return "Android";

        #[cfg(target_os = "ios")]
        return "iOS";

        #[cfg(target_os = "linux")] // HarmonyOS
        return "HarmonyOS";

        #[cfg(not(any(target_os = "android", target_os = "ios", target_os = "linux")))]
        return "Unknown";
    }
    

    
    /// 获取会话ID
    pub fn get_session_id(&self) -> &str {
        &self.session_id
    }

    /// 将日志条目转换为protobuf格式
    /// 用于服务器通信，确保与Android端格式一致
    pub fn to_protobuf(&self, entry: &LogEntry, config: &LoggerConfig) -> Result<Vec<u8>> {
        // 使用与Android一致的时间格式：yyyy-MM-dd-HH:mm:ss.SSSSSSZ
        let local_time = entry.timestamp.with_timezone(&Local);
        let time_str = local_time.format("%Y-%m-%d-%H:%M:%S%.6f%z").to_string();

        let metadata = LogMetaData {
            time: time_str,
            session_id: (*self.session_id).clone(),
            tag: entry.tag.clone(),
            level: entry.level.as_str().to_string(),
            test_mode: if config.is_debug_mode { "测试模式".to_string() } else { "正常模式".to_string() },
            user_id: config.user_id.clone(),
            log_message: self.build_content(entry)?.to_string(),
        };

        let mut buf = Vec::new();
        metadata.encode(&mut buf)
            .map_err(|e| LoggerError::unknown(format!("Failed to encode protobuf: {}", e)))?;

        Ok(buf)
    }

    /// 批量转换日志条目为protobuf格式
    pub fn entries_to_protobuf(&self, entries: &[LogEntry], config: &LoggerConfig) -> Result<Vec<Vec<u8>>> {
        entries.iter()
            .map(|entry| self.to_protobuf(entry, config))
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::LoggerConfig;
    use crate::core::{LogEntry, LogLevel};
    use chrono::Utc;

    #[test]
    fn test_formatter_session_id() {
        let mut config = LoggerConfig::default();
        config.session_id = "test-session-123".to_string();

        let mut formatter = LogFormatter::new();
        formatter.initialize(&config).unwrap();

        println!("Formatter session_id: '{}'", formatter.session_id);
        println!("Config session_id: '{}'", config.session_id);

        // 创建测试日志条目
        let entry = LogEntry {
            timestamp: Utc::now(),
            level: LogLevel::Info,
            tag: "TestTag".to_string(),
            message: "Test message".to_string(),
            args: vec![],
            sensitive_flags: vec![],
            process_id: Some(1234),
            thread_id: Some("main".to_string()),
        };

        let formatted = formatter.format_entry(&entry, &config).unwrap();
        println!("Formatted output: {}", formatted);

        assert!(formatted.contains("test-session-123"));
    }

    #[test]
    fn test_formatter_empty_session_id() {
        let mut config = LoggerConfig::default();
        config.session_id = "".to_string(); // 模拟HarmonyOS传入空字符串

        let mut formatter = LogFormatter::new();
        let original_session_id = formatter.session_id.clone();
        formatter.initialize(&config).unwrap();

        println!("Original formatter session_id: '{}'", original_session_id);
        println!("After init formatter session_id: '{}'", formatter.session_id);
        println!("Config session_id: '{}'", config.session_id);

        // 应该保持原来的UUID，不被空字符串覆盖
        assert_eq!(formatter.session_id, original_session_id);
        assert!(!formatter.session_id.is_empty());

        // 创建测试日志条目
        let entry = LogEntry {
            timestamp: Utc::now(),
            level: LogLevel::Info,
            tag: "TestTag".to_string(),
            message: "Test message".to_string(),
            args: vec![],
            sensitive_flags: vec![],
            process_id: Some(1234),
            thread_id: Some("main".to_string()),
        };

        let formatted = formatter.format_entry(&entry, &config).unwrap();
        println!("Formatted output: {}", formatted);

        // 应该包含UUID，不是空字符串
        assert!(formatted.contains(&*formatter.session_id));
    }

    #[test]
    fn test_console_vs_protobuf_consistency() {
        let mut config = LoggerConfig::default();
        config.session_id = "test-session-456".to_string();
        config.user_id = "user123".to_string();

        let mut formatter = LogFormatter::new();
        formatter.initialize(&config).unwrap();

        // 创建测试日志条目
        let entry = LogEntry {
            timestamp: Utc::now(),
            level: LogLevel::Warn,
            tag: "TestTag".to_string(),
            message: "Test warning message".to_string(),
            args: vec![],
            sensitive_flags: vec![],
            process_id: Some(1234),
            thread_id: Some("main".to_string()),
        };

        // 控制台格式
        let console_output = formatter.format_entry(&entry, &config).unwrap();
        println!("控制台输出: {}", console_output);

        // protobuf格式
        let protobuf_data = formatter.to_protobuf(&entry, &config).unwrap();
        println!("protobuf数据长度: {} 字节", protobuf_data.len());
        println!("protobuf数据(hex): {}", hex::encode(&protobuf_data[..std::cmp::min(100, protobuf_data.len())]));

        // 验证控制台输出包含所有必要字段
        assert!(console_output.contains("test-session-456"));
        assert!(console_output.contains("TestTag"));
        assert!(console_output.contains("WARN"));
        assert!(console_output.contains("user123"));
        assert!(console_output.contains("Test warning message"));

        // 验证protobuf数据不为空
        assert!(!protobuf_data.is_empty());
    }
}

// 自动生成的protobuf代码
include!("generated/logger.rs");

use chrono::Local;
use crate::config::LoggerConfig;
use std::io::{Read, Write};

impl LogMetaData {
    /// 创建新的日志元数据
    /// 使用与Android完全一致的时间格式：yyyy-MM-dd-HH:mm:ss.SSSSSSZ
    pub fn new(
        level: &str,
        tag: &str,
        message: &str,
        config: &LoggerConfig,
    ) -> Self {
        // 使用与Android一致的时间格式：yyyy-MM-dd-HH:mm:ss.SSSSSSZ
        let now = Local::now();
        let time_str = now.format("%Y-%m-%d-%H:%M:%S%.6f%z").to_string();

        Self {
            time: time_str,
            session_id: config.session_id.clone(),
            tag: tag.to_string(),
            level: level.to_string(),
            test_mode: if config.is_debug_mode { "测试模式".to_string() } else { "正常模式".to_string() },
            user_id: config.user_id.clone(),
            log_message: message.to_string(),
        }
    }

    /// 序列化为字节数组
    pub fn to_bytes(&self) -> Vec<u8> {
        use prost::Message;
        let mut buf = Vec::new();
        self.encode(&mut buf).expect("Failed to encode LogMetaData");
        buf
    }

    /// 从字节数组反序列化
    pub fn from_bytes(data: &[u8]) -> Result<Self, prost::DecodeError> {
        use prost::Message;
        Self::decode(data)
    }

    /// 写入带长度前缀的格式（与Android兼容）
    pub fn write_delimited_to(&self, writer: &mut impl Write) -> std::io::Result<()> {
        let data = self.to_bytes();
        let len = data.len() as u32;

        // 写入长度前缀（varint编码）
        let mut len_buf = Vec::new();
        prost::encoding::encode_varint(len as u64, &mut len_buf);
        writer.write_all(&len_buf)?;

        // 写入数据
        writer.write_all(&data)?;

        Ok(())
    }

    /// 从带长度前缀的格式读取（与Android兼容）
    pub fn read_delimited_from(reader: &mut impl Read) -> std::io::Result<Option<Self>> {
        use prost::Message;
        use std::io::{Error, ErrorKind};

        // 读取长度前缀
        let mut len_buf = [0u8; 1];
        match reader.read_exact(&mut len_buf) {
            Ok(_) => {},
            Err(e) if e.kind() == ErrorKind::UnexpectedEof => return Ok(None),
            Err(e) => return Err(e),
        }

        // 解码varint长度
        let mut len = len_buf[0] as u64;
        if len_buf[0] & 0x80 != 0 {
            // 多字节varint，简化处理
            let mut shift = 7;
            len &= 0x7F;

            loop {
                let mut byte = [0u8; 1];
                reader.read_exact(&mut byte)?;

                len |= ((byte[0] & 0x7F) as u64) << shift;

                if byte[0] & 0x80 == 0 {
                    break;
                }

                shift += 7;
                if shift >= 64 {
                    return Err(Error::new(ErrorKind::InvalidData, "Varint too long"));
                }
            }
        }

        // 读取数据
        let mut data = vec![0u8; len as usize];
        reader.read_exact(&mut data)?;

        // 解码protobuf
        match Self::decode(&data[..]) {
            Ok(metadata) => Ok(Some(metadata)),
            Err(e) => Err(Error::new(ErrorKind::InvalidData, format!("Failed to decode protobuf: {}", e))),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::LoggerConfig;
    
    #[test]
    fn test_log_metadata_serialization() {
        let config = LoggerConfig::default();
        let metadata = LogMetaData::new("INFO", "TestTag", "Test message", &config);
        
        // 测试基本序列化
        let bytes = metadata.to_bytes();
        let deserialized = LogMetaData::from_bytes(&bytes).unwrap();
        
        assert_eq!(metadata.tag, deserialized.tag);
        assert_eq!(metadata.level, deserialized.level);
        assert_eq!(metadata.log_message, deserialized.log_message);
    }
    
    #[test]
    fn test_delimited_format() {
        let config = LoggerConfig::default();
        let metadata = LogMetaData::new("ERROR", "TestTag", "Error message", &config);

        // 测试带长度前缀的格式
        let mut buffer = Vec::new();
        metadata.write_delimited_to(&mut buffer).unwrap();

        let mut cursor = std::io::Cursor::new(buffer);
        let deserialized = LogMetaData::read_delimited_from(&mut cursor).unwrap().unwrap();

        assert_eq!(metadata.tag, deserialized.tag);
        assert_eq!(metadata.level, deserialized.level);
        assert_eq!(metadata.log_message, deserialized.log_message);
    }

    #[test]
    fn test_android_compatibility() {
        let mut config = LoggerConfig::default();
        config.user_id = "test_user_123".to_string();
        config.session_id = "session_456".to_string();
        config.is_debug_mode = true;

        let metadata = LogMetaData::new("INFO", "AndroidTest", "Test log message for Android compatibility", &config);

        // 验证字段值
        assert_eq!(metadata.level, "INFO");
        assert_eq!(metadata.tag, "AndroidTest");
        assert_eq!(metadata.log_message, "Test log message for Android compatibility");
        assert_eq!(metadata.user_id, "test_user_123");
        assert_eq!(metadata.session_id, "session_456");
        assert_eq!(metadata.test_mode, "测试模式");
        assert!(!metadata.time.is_empty());

        // 测试序列化和反序列化
        let bytes = metadata.to_bytes();
        let deserialized = LogMetaData::from_bytes(&bytes).unwrap();

        assert_eq!(metadata.level, deserialized.level);
        assert_eq!(metadata.tag, deserialized.tag);
        assert_eq!(metadata.log_message, deserialized.log_message);
        assert_eq!(metadata.user_id, deserialized.user_id);
        assert_eq!(metadata.session_id, deserialized.session_id);
        assert_eq!(metadata.test_mode, deserialized.test_mode);
        assert_eq!(metadata.time, deserialized.time);

        // 验证protobuf格式（应该包含所有字段的标签）
        assert!(bytes.len() > 0);
        println!("Protobuf size: {} bytes", bytes.len());
        println!("Serialized metadata: {:?}", metadata);
    }
}

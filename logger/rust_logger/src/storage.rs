use std::path::{Path, PathBuf};
use std::fs;
use rust_storage::api::storage_manager::StorageManager;

use crate::{
    config::LoggerConfig,
    error::{LoggerError, Result},
    protobuf::LogMetaData,
    mmap_storage::{ThreadSafeMmapStorage, StorageConfig},
};

/// 日志文件条目（替代walkdir::DirEntry）
#[derive(Debug)]
struct LogFileEntry {
    path: PathBuf,
    metadata: fs::Metadata,
}

impl LogFileEntry {
    /// 获取文件路径
    fn path(&self) -> &Path {
        &self.path
    }

    /// 获取文件元数据
    fn metadata(&self) -> Result<&fs::Metadata> {
        Ok(&self.metadata)
    }
}

/// 日志存储管理器
/// 使用纯Rust mmap存储替代xlog
/// 不存储配置副本，所有配置都从LoggerConfig获取
pub struct LogStorage {
    mmap_storage: Option<ThreadSafeMmapStorage>,
    initialized: bool,
}

// 存储键名常量
const STORAGE_KEY_CONSOLE_ENABLED: &str = "logger_console_enabled";
const STORAGE_KEY_FULL_LOGS_ENABLED: &str = "logger_full_logs_enabled";
const STORAGE_KEY_LOG_LEVEL: &str = "logger_log_level";

impl LogStorage {
    /// 创建新的存储管理器
    pub fn new() -> Self {
        Self {
            mmap_storage: None,
            initialized: false,
        }
    }
    
    /// 初始化存储管理器
    pub fn initialize(&mut self, config: &LoggerConfig) -> Result<()> {
        if self.initialized {
            // 如果已经初始化，先关闭
            if let Some(storage) = &self.mmap_storage {
                storage.close().map_err(|e| LoggerError::storage(&format!("Failed to close mmap storage: {}", e)))?;
            }
        }

        // 使用版本号目录（与Android对齐）
        let log_directory = PathBuf::from(config.get_versioned_log_directory());

        // 创建存储配置（传入max_file_size避免重复存储）
        let storage_config = StorageConfig::new(&log_directory, &config.log_file_prefix, config.max_file_size)
            .map_err(|e| LoggerError::config(&format!("Failed to create storage config: {}", e)))?;

        // 初始化mmap存储
        let mmap_storage = ThreadSafeMmapStorage::new(storage_config)
            .map_err(|e| {
                crate::internal_error!("STORAGE", "Failed to initialize mmap storage: {}", e);
                LoggerError::config(&format!("Failed to initialize mmap storage: {}", e))
            })?;

        self.mmap_storage = Some(mmap_storage);
        self.initialized = true;

        // 清理旧文件（在Rust层管理总文件大小）
        self.cleanup_old_logs(config)?;

        // 保存配置到rust_storage
        self.save_config_to_storage(config)?;
        Ok(())
    }

    /// 保存配置到rust_storage
    fn save_config_to_storage(&self, config: &LoggerConfig) -> Result<()> {
        // 在测试环境中，rust_storage可能没有初始化，使用catch_unwind来处理
        let result = std::panic::catch_unwind(|| {
            let storage = StorageManager::get_instance().get_storage();

            // 保存控制台日志状态
            storage.put_bool(STORAGE_KEY_CONSOLE_ENABLED, config.enable_console_output)
                .map_err(|e| LoggerError::storage(&format!("Failed to save console enabled: {}", e)))?;

            // 保存完整日志状态
            storage.put_bool(STORAGE_KEY_FULL_LOGS_ENABLED, config.enable_full_log)
                .map_err(|e| LoggerError::storage(&format!("Failed to save full logs enabled: {}", e)))?;

            // 保存日志级别
            let level_int = config.log_level as i32;
            storage.put_int(STORAGE_KEY_LOG_LEVEL, level_int)
                .map_err(|e| LoggerError::storage(&format!("Failed to save log level: {}", e)))?;


            Ok::<(), crate::LoggerError>(())
        });

        match result {
            Ok(Ok(())) => Ok(()),
            Ok(Err(e)) => Err(e),
            Err(_) => {
                crate::internal_warn!("STORAGE", "StorageManager not initialized, skipping config save");
                Ok(())
            }
        }
    }

    /// 从rust_storage读取控制台日志状态
    pub fn get_console_enabled_from_storage(&self) -> bool {
        StorageManager::get_instance()
            .get_storage()
            .get_bool(STORAGE_KEY_CONSOLE_ENABLED, false)
            .unwrap_or(false)
    }

    /// 从rust_storage读取完整日志状态
    pub fn get_full_logs_enabled_from_storage(&self) -> bool {
        StorageManager::get_instance()
            .get_storage()
            .get_bool(STORAGE_KEY_FULL_LOGS_ENABLED, false)
            .unwrap_or(false)
    }

    /// 从rust_storage读取日志级别
    pub fn get_log_level_from_storage(&self) -> i32 {
        StorageManager::get_instance()
            .get_storage()
            .get_int(STORAGE_KEY_LOG_LEVEL, 3) // 默认WARN级别
            .unwrap_or(3)
    }

    /// 更新控制台日志状态到rust_storage
    pub fn update_console_enabled_to_storage(&self, enabled: bool) -> Result<()> {
        StorageManager::get_instance()
            .get_storage()
            .put_bool(STORAGE_KEY_CONSOLE_ENABLED, enabled)
            .map_err(|e| LoggerError::storage(&format!("Failed to update console enabled: {}", e)))?;
        Ok(())
    }

    /// 更新完整日志状态到rust_storage
    pub fn update_full_logs_enabled_to_storage(&self, enabled: bool) -> Result<()> {
        StorageManager::get_instance()
            .get_storage()
            .put_bool(STORAGE_KEY_FULL_LOGS_ENABLED, enabled)
            .map_err(|e| LoggerError::storage(&format!("Failed to update full logs enabled: {}", e)))?;
        Ok(())
    }
    
    /// 写入日志（文本格式，向后兼容）
    pub fn write_log(&mut self, log_content: &str, config: &LoggerConfig) -> Result<()> {
        if !self.initialized {
            return Err(LoggerError::initialization("Storage not initialized"));
        }

        // 将文本日志转换为protobuf格式
        self.write_log_protobuf("INFO", "UpLog", log_content, config)
    }

    /// 写入protobuf格式的日志（与Android兼容）
    /// 注意：这个方法直接使用config.session_id，可能与formatter.session_id不一致
    /// 建议使用write_log_protobuf_with_formatter方法确保一致性
    pub fn write_log_protobuf(&mut self, level: &str, tag: &str, message: &str, config: &LoggerConfig) -> Result<()> {
        if !self.initialized {
            return Err(LoggerError::initialization("Storage not initialized"));
        }

        let storage = self.mmap_storage.as_ref()
            .ok_or_else(|| LoggerError::initialization("Mmap storage not initialized"))?;

        // 创建protobuf日志元数据
        let log_metadata = LogMetaData::new(level, tag, message, config);

        // 序列化protobuf数据
        let protobuf_data = log_metadata.to_bytes();

        // 直接将protobuf二进制数据写入mmap存储（不压缩、不加密）
        // ThreadSafeMmapStorage::write_protobuf 内部已经处理了自动flush逻辑
        storage.write_protobuf(&protobuf_data)
            .map_err(|e| LoggerError::storage(&format!("Failed to write protobuf log: {}", e)))?;

        // 检查并清理旧文件（在Rust层管理总文件大小）
        self.cleanup_old_logs(config)?;


        Ok(())
    }

    /// 直接写入protobuf数据（用于core.rs中的直接调用）
    pub fn write_protobuf_data(&self, protobuf_data: &[u8]) -> Result<()> {
        if !self.initialized {
            return Err(LoggerError::initialization("Storage not initialized"));
        }

        let storage = self.mmap_storage.as_ref()
            .ok_or_else(|| LoggerError::initialization("Mmap storage not initialized"))?;

        storage.write_protobuf(protobuf_data)
            .map_err(|e| LoggerError::storage(&format!("Failed to write protobuf data: {}", e)))?;

        Ok(())
    }

    /// 获取当前日志文件路径
    pub fn get_current_log_path(&self, config: &LoggerConfig) -> Option<String> {
        if self.initialized {
            // 生成当前日志文件路径
            let log_directory = PathBuf::from(config.get_versioned_log_directory());
            let now = chrono::Local::now();
            let filename = format!("{}.log", now.format("%Y%m%d"));
            Some(log_directory.join(filename).to_string_lossy().to_string())
        } else {
            None
        }
    }

    /// 获取当前文件路径（用于大文件回调）
    pub fn get_current_file_path(&self, config: &LoggerConfig) -> Result<String> {
        if !self.initialized {
            return Err(LoggerError::initialization("Storage not initialized"));
        }

        self.get_current_log_path(config)
            .ok_or_else(|| LoggerError::storage("Failed to get current log path"))
    }

    /// 获取当前文件大小（用于大文件检查）
    pub fn get_current_file_size(&self, config: &LoggerConfig) -> Result<u64> {
        if !self.initialized {
            return Err(LoggerError::initialization("Storage not initialized"));
        }

        if let Some(current_path_str) = self.get_current_log_path(config) {
            let current_path = PathBuf::from(current_path_str);
            if current_path.exists() {
                let metadata = std::fs::metadata(&current_path)
                    .map_err(|e| LoggerError::storage(&format!("Failed to get file metadata: {}", e)))?;
                Ok(metadata.len())
            } else {
                Ok(0) // 文件不存在，大小为0
            }
        } else {
            Ok(0) // 无法获取路径，大小为0
        }
    }
    
    /// 清理旧日志文件
    /// 当目录达到最大大小时，依次删除最早的日志文件
    fn cleanup_old_logs(&self, config: &LoggerConfig) -> Result<()> {
        let total_size = self.calculate_directory_size(config)?;

        if total_size <= config.max_directory_size {
            return Ok(()); // 如果目录大小未超过限制，直接返回
        }

        crate::internal_info!("STORAGE", "Directory size {} exceeds limit {}, cleaning up old log files",
                              total_size, config.max_directory_size);

        // 获取所有日志文件，按修改时间排序（最旧的在前）
        let mut log_files = self.get_log_files(config)?;
        log_files.sort_by_key(|entry| {
            entry.metadata()
                .ok()
                .and_then(|m| m.modified().ok())
                .unwrap_or(std::time::SystemTime::UNIX_EPOCH)
        });

        let mut current_size = total_size;
        let mut total_deleted_files = 0;
        let mut total_deleted_size = 0u64;

        // 持续删除最早的文件直到目录大小降到限制以下
        while current_size > config.max_directory_size && !log_files.is_empty() {
            let mut file_deleted = false;
            let mut file_to_remove_index = None;

            // 找到第一个可以删除的文件
            for (index, file_entry) in log_files.iter().enumerate() {
                let file_path = file_entry.path();

                // 不删除当前正在使用的文件
                if let Some(current_path_str) = self.get_current_log_path(config) {
                    let current_path = PathBuf::from(current_path_str);
                    if file_path == current_path {
                        continue;
                    }
                }

                // 检查文件是否还存在
                if !file_path.exists() {
                    file_to_remove_index = Some(index);
                    break;
                }

                if let Ok(metadata) = file_entry.metadata() {
                    let file_size = metadata.len();

                    if let Err(e) = std::fs::remove_file(file_path) {
                        crate::internal_warn!("STORAGE", "Failed to remove old log file {:?}: {}", file_path, e);
                    } else {
                        current_size -= file_size;
                        total_deleted_files += 1;
                        total_deleted_size += file_size;
                        file_deleted = true;

                        file_to_remove_index = Some(index);
                        break; // 删除一个文件后继续下一轮
                    }
                }
            }

            // 从列表中移除已处理的文件
            if let Some(index) = file_to_remove_index {
                log_files.remove(index);
            }

            // 如果没有删除任何文件，避免无限循环
            if !file_deleted && file_to_remove_index.is_none() {
                crate::internal_warn!("STORAGE", "No files could be deleted, stopping cleanup");
                break;
            }
        }

        if total_deleted_files > 0 {
            crate::internal_info!("STORAGE", "Cleanup completed. Deleted {} old files totaling {} bytes. Directory size reduced from {} to {} bytes",
                                  total_deleted_files, total_deleted_size, total_size, current_size);
        }

        Ok(())
    }
    
    /// 计算目录总大小
    fn calculate_directory_size(&self, config: &LoggerConfig) -> Result<u64> {
        let log_directory = PathBuf::from(config.get_versioned_log_directory());
        self.calculate_directory_size_recursive(&log_directory)
    }

    /// 递归计算目录大小
    fn calculate_directory_size_recursive(&self, dir: &Path) -> Result<u64> {
        let mut total_size = 0;

        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.is_file() {
                    if let Ok(metadata) = entry.metadata() {
                        total_size += metadata.len();
                    }
                } else if path.is_dir() {
                    // 递归计算子目录大小
                    total_size += self.calculate_directory_size_recursive(&path)?;
                }
            }
        }

        Ok(total_size)
    }
    
    /// 获取所有日志文件
    fn get_log_files(&self, config: &LoggerConfig) -> Result<Vec<LogFileEntry>> {
        let mut log_files = Vec::new();
        let log_directory = PathBuf::from(config.get_versioned_log_directory());
        self.collect_log_files_recursive(&log_directory, &mut log_files)?;
        Ok(log_files)
    }

    /// 递归收集日志文件
    fn collect_log_files_recursive(&self, dir: &Path, log_files: &mut Vec<LogFileEntry>) -> Result<()> {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.is_file() {
                    if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                        if filename.ends_with(".log") {
                            if let Ok(metadata) = entry.metadata() {
                                log_files.push(LogFileEntry {
                                    path: path.clone(),
                                    metadata,
                                });
                            }
                        }
                    }
                } else if path.is_dir() {
                    // 递归处理子目录
                    self.collect_log_files_recursive(&path, log_files)?;
                }
            }
        }
        Ok(())
    }
    
    /// 获取所有日志文件路径（用于上传）
    pub fn get_all_log_files(&self, config: &LoggerConfig) -> Result<Vec<PathBuf>> {
        let log_files = self.get_log_files(config)?;
        Ok(log_files.into_iter().map(|entry| entry.path().to_path_buf()).collect())
    }
    
    /// 读取日志文件内容
    pub fn read_log_file(&self, file_path: &Path) -> Result<String> {
        std::fs::read_to_string(file_path).map_err(LoggerError::from)
    }
    
    /// 删除日志文件
    pub fn delete_log_file(&self, file_path: &Path) -> Result<()> {
        std::fs::remove_file(file_path).map_err(LoggerError::from)
    }
    
    /// 获取日志目录路径
    pub fn get_log_directory(&self, config: &LoggerConfig) -> PathBuf {
        PathBuf::from(config.get_versioned_log_directory())
    }
    
    /// 强制刷新当前文件
    pub fn flush(&mut self) -> Result<()> {
        if self.initialized {
            if let Some(storage) = &self.mmap_storage {
                storage.flush_to_file()
                    .map_err(|e| LoggerError::storage(&format!("Failed to flush: {}", e)))?;
            }
        }
        Ok(())
    }
}

impl Drop for LogStorage {
    fn drop(&mut self) {
        if self.initialized {
            if let Some(storage) = &self.mmap_storage {
    
                let _ = storage.close();
            }
        }
    }
}

use std::time::Instant;
use std::collections::VecDeque;
use parking_lot::Mutex;
use crate::{Result, LoggerConfig};
use crate::core::{LogEntry, LogLevel};
use crate::sensitive_data::SensitiveDataProcessor;

/// 简化的日志处理器
///
/// 这个处理器将原本复杂的责任链模式简化为单一的高效处理器，
/// 合并了以下处理逻辑：
/// 1. 条件过滤：根据隐私协议状态过滤日志
/// 2. 限流控制：防止日志洪水攻击
/// 3. 长度限制：截断过长的日志内容
/// 4. 格式化：添加环境和测试模式标识
/// 5. 敏感信息脱敏：保护用户隐私
///
/// 设计优势：
/// - 减少内存分配：避免多次传递LogEntry对象
/// - 降低锁竞争：统一的锁管理策略
/// - 提高性能：消除责任链的虚函数调用开销
/// - 简化维护：所有逻辑集中在一个地方
pub struct LogProcessor {
    sensitive_data_processor: std::sync::Mutex<SensitiveDataProcessor>,
    rate_limiter: Mutex<VecDeque<Instant>>,
    initialized: std::sync::atomic::AtomicBool,
}

impl LogProcessor {
    /// 创建新的处理器
    pub fn new() -> Self {
        Self {
            sensitive_data_processor: std::sync::Mutex::new(SensitiveDataProcessor::new()),
            rate_limiter: Mutex::new(VecDeque::new()),
            initialized: std::sync::atomic::AtomicBool::new(false),
        }
    }

    /// 确保敏感数据处理器已初始化
    fn ensure_initialized(&self, config: &LoggerConfig) -> Result<()> {
        if !self.initialized.load(std::sync::atomic::Ordering::Relaxed) {
            let mut processor = self.sensitive_data_processor.lock()
                .map_err(|_| crate::error::LoggerError::unknown("Failed to acquire sensitive data processor lock"))?;
            processor.initialize(config)?;
            self.initialized.store(true, std::sync::atomic::Ordering::Relaxed);
        }
        Ok(())
    }

    /// 检查是否允许记录日志（限流）
    ///
    /// 使用滑动窗口算法实现限流：
    /// 1. 维护一个时间戳队列，记录最近1秒内的日志时间
    /// 2. 每次检查时，先清理超过1秒的旧时间戳
    /// 3. 如果当前队列长度超过限制，拒绝新日志
    /// 4. 否则添加当前时间戳并允许日志
    ///
    /// 参数：
    /// - max_logs_per_second: 每秒最大日志数，0表示不限流
    ///
    /// 返回：
    /// - true: 允许记录日志
    /// - false: 触发限流，应拒绝日志
    fn allow_log(&self, max_logs_per_second: u32) -> bool {
        // 如果max_logs_per_second为0，表示不限流
        if max_logs_per_second == 0 {
            return true;
        }

        let mut timestamps = self.rate_limiter.lock();
        let now = Instant::now();

        // 移除超过1秒的时间戳
        while let Some(&front) = timestamps.front() {
            if now.duration_since(front).as_secs() >= 1 {
                timestamps.pop_front();
            } else {
                break;
            }
        }

        // 检查是否超过限制
        if timestamps.len() >= max_logs_per_second as usize {
            false
        } else {
            timestamps.push_back(now);
            true
        }
    }

    /// 处理日志条目（合并所有处理逻辑）
    ///
    /// 这是核心处理方法，按以下顺序执行所有处理步骤：
    ///
    /// 1. **条件过滤**：检查隐私协议状态
    ///    - 如果用户未同意隐私协议，过滤Info及以下级别的日志
    ///    - Error级别的日志始终允许（用于崩溃报告等重要信息）
    ///
    /// 2. **限流检查**：防止日志洪水攻击
    ///    - 使用滑动窗口算法控制每秒日志数量
    ///    - 超过限制时拒绝新日志，保护系统性能
    ///
    /// 3. **长度限制**：防止单条日志过大
    ///    - 截断过长的消息、标签和参数
    ///    - 添加"..."标识表示内容被截断
    ///
    /// 4. **格式化处理**：添加环境标识
    ///    - 测试模式下添加[TEST]前缀
    ///    - 在标签中添加环境信息
    ///
    /// 5. **敏感信息脱敏**：保护用户隐私
    ///    - 脱敏手机号、邮箱、身份证号等敏感信息
    ///    - 只在启用脱敏且同意隐私协议时执行
    ///
    /// 参数：
    /// - entry: 待处理的日志条目
    /// - config: 日志配置，包含各种处理参数
    ///
    /// 返回：
    /// - Ok(LogEntry): 处理后的日志条目
    /// - Err(LoggerError): 处理失败（如被过滤、限流等）
    pub fn process(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        // 1. 条件过滤：检查隐私协议
        if !config.privacy_agreed && entry.level as u8 <= LogLevel::Info as u8 {
            return Err(crate::error::LoggerError::config("Privacy not agreed, filtering log"));
        }

        // 2. 限流检查
        if !self.allow_log(config.max_logs_per_second) {
            return Err(crate::error::LoggerError::config("Rate limit exceeded"));
        }

        // 3. 长度限制
        self.apply_length_limits(&mut entry, config);

        // 4. 格式化处理
        self.apply_formatting(&mut entry, config);

        // 5. 敏感信息脱敏（如果启用）
        if !config.disable_sensitive_words && config.privacy_agreed {
            self.ensure_initialized(config)?;
            let processor = self.sensitive_data_processor.lock()
                .map_err(|_| crate::error::LoggerError::unknown("Failed to acquire sensitive data processor lock"))?;
            entry = processor.process_entry(entry, config)?;
        }

        Ok(entry)
    }

    /// 应用长度限制
    fn apply_length_limits(&self, entry: &mut LogEntry, config: &LoggerConfig) {
        let max_length = config.max_log_length;

        // 限制消息长度
        if entry.message.len() > max_length {
            entry.message.truncate(max_length - 3);
            entry.message.push_str("...");
        }

        // 限制标签长度
        if entry.tag.len() > 100 {
            entry.tag.truncate(97);
            entry.tag.push_str("...");
        }

        // 限制参数长度
        for arg in &mut entry.args {
            if arg.len() > 200 {
                arg.truncate(197);
                arg.push_str("...");
            }
        }
    }

    /// 应用格式化
    fn apply_formatting(&self, entry: &mut LogEntry, config: &LoggerConfig) {
        // 添加测试模式标识
        if config.test_mode {
            entry.message = format!("[TEST] {}", entry.message);
        }

        // 注意：不在这里修改tag，保持与Android一致
        // tag的环境标识只在控制台输出时添加，不影响protobuf存储
    }
}

/// 兼容性：保留原有的LogProcessorChain接口，但内部使用简化的LogProcessor
pub struct LogProcessorChain {
    processor: LogProcessor,
}

impl LogProcessorChain {
    /// 创建新的处理链
    pub fn new() -> Self {
        Self {
            processor: LogProcessor::new(),
        }
    }

    /// 处理日志条目
    pub fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        self.processor.process(entry, config)
    }

    /// 获取启用的处理器数量（简化版本）
    pub fn enabled_count(&self, config: &LoggerConfig) -> usize {
        let mut count = 3; // 基础处理器：条件过滤、限流、长度限制、格式化

        // 敏感信息处理器
        if !config.disable_sensitive_words && config.privacy_agreed {
            count += 1;
        }

        count
    }

    /// 获取所有处理器名称（简化版本）
    pub fn processor_names(&self) -> Vec<&'static str> {
        vec![
            "ConditionalFilter",
            "RateLimit",
            "LengthLimit",
            "Formatting",
            "SensitiveData"
        ]
    }
}

impl Default for LogProcessorChain {
    fn default() -> Self {
        Self::new()
    }
}



/// 创建默认的处理链（简化版本）
pub fn create_default_processor_chain() -> Result<LogProcessorChain> {
    Ok(LogProcessorChain::new())
}

/// 初始化处理链中需要初始化的处理器
pub fn initialize_processor_chain(_chain: &LogProcessorChain, _config: &LoggerConfig) -> Result<()> {
    // 这里可以遍历处理器并初始化需要初始化的处理器
    // 目前只有SensitiveDataProcessorAdapter需要初始化，但由于trait限制，
    // 我们在create_default_processor_chain中已经创建了初始化好的实例
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::{LogLevel, LogEntry};

    #[test]
    fn test_processor_chain() {
        let chain = create_default_processor_chain().unwrap();
        let mut config = crate::config::LoggerConfig::new();
        config.set_privacy_agreed(true); // 设置隐私协议同意

        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Test message with phone 13812345678".to_string(),
            vec!["arg1".to_string()],
            vec![false],
        );

        let result = chain.process(entry, &config).unwrap();

        // 验证脱敏效果
        assert!(result.message.contains("138****5678"));

        // 验证tag保持原始值（环境标识现在在formatter层添加）
        assert_eq!(result.tag, "test");

        // 验证处理链正常工作
        assert!(!result.args.is_empty());
    }

    #[test]
    fn test_sensitive_data_processing() {
        let processor = LogProcessor::new();
        let mut config = crate::config::LoggerConfig::new();
        config.set_privacy_agreed(true);

        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Phone: 13812345678, Email: <EMAIL>, ID: 110101199001011234".to_string(),
            vec![],
            vec![],
        );

        let result = processor.process(entry, &config).unwrap();

        // 验证脱敏效果
        assert!(result.message.contains("138****5678"));
        assert!(result.message.contains("t***@example.com"));
        assert!(result.message.contains("110101********1234"));
    }

    #[test]
    fn test_conditional_filtering() {
        let processor = LogProcessor::new();
        let mut config = crate::config::LoggerConfig::new();

        // 测试隐私协议未同意时的过滤
        config.set_privacy_agreed(false);

        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "This should be filtered".to_string(),
            vec![],
            vec![],
        );

        let result = processor.process(entry, &config);
        assert!(result.is_err()); // 应该被过滤

        // 测试Error级别不被过滤
        let error_entry = LogEntry::new(
            LogLevel::Error,
            "test".to_string(),
            "This should not be filtered".to_string(),
            vec![],
            vec![],
        );

        let result = processor.process(error_entry, &config);
        assert!(result.is_ok()); // Error级别不应该被过滤
    }

    #[test]
    fn test_length_limiting() {
        let processor = LogProcessor::new();
        let mut config = crate::config::LoggerConfig::new();
        config.set_max_log_length(100); // 设置一个较小的限制来测试截断
        config.set_privacy_agreed(true);

        let long_message = "a".repeat(200); // 超过限制的消息
        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            long_message,
            vec!["b".repeat(300)],
            vec![false],
        );

        let result = processor.process(entry, &config).unwrap();

        // 验证消息长度被限制
        assert!(result.message.len() <= config.max_log_length);
        assert!(result.message.ends_with("..."));

        // 验证参数长度被限制
        assert!(result.args[0].len() <= 200);
        assert!(result.args[0].ends_with("..."));
    }

    #[test]
    fn test_rate_limiting() {
        let processor = LogProcessor::new();
        let mut config = crate::config::LoggerConfig::new();
        config.set_privacy_agreed(true);

        // 测试默认不限流（max_logs_per_second = 0）
        config.max_logs_per_second = 0;
        for _ in 0..10 {
            let entry = LogEntry::new(
                LogLevel::Info,
                "test".to_string(),
                "Test message".to_string(),
                vec![],
                vec![],
            );
            let result = processor.process(entry, &config);
            assert!(result.is_ok()); // 默认不限流，所有日志都应该通过
        }

        // 测试限流功能（设置为2条/秒）
        config.max_logs_per_second = 2;

        // 前2条应该通过
        for _ in 0..2 {
            let entry = LogEntry::new(
                LogLevel::Info,
                "test".to_string(),
                "Test message".to_string(),
                vec![],
                vec![],
            );
            let result = processor.process(entry, &config);
            assert!(result.is_ok());
        }

        // 第3条应该被限流
        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Test message".to_string(),
            vec![],
            vec![],
        );
        let result = processor.process(entry, &config);
        assert!(result.is_err()); // 应该被限流
    }
}

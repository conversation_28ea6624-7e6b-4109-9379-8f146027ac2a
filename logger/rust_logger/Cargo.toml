[package]
name = "rust_logger"
version = "1.0.0"
edition = "2021"

[lib]
name = "rust_logger"
crate-type = ["cdylib", "staticlib", "rlib"]

[features]
android = ["dep:jni", "dep:android_logger"]
ohos = ["dep:napi-ohos", "dep:napi-derive-ohos"]
ios = []

# 内部日志级别编译时优化特性
internal_log_level_error = []
internal_log_level_warn = []
internal_log_level_info = []
internal_log_level_debug = []

[dependencies]
# 平台特定依赖（可选）
jni = { workspace = true, optional = true }
android_logger = { workspace = true, optional = true }
napi-ohos = { workspace = true, optional = true }
napi-derive-ohos = { workspace = true, optional = true }

# 核心依赖
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
uuid = { workspace = true, features = ["v4"] }
regex = { workspace = true }
thiserror = { workspace = true }
parking_lot = { workspace = true }

# 文件操作
zip = { workspace = true }
memmap2 = { workspace = true }

# Protobuf支持
prost = { workspace = true }

# FlatBuffers支持
flatbuffers = { workspace = true }

# 公司内部依赖
rust_storage = { path = "../../storage_rust/rust_storage" }
task_manager = { path = "../../task_manager_rust/task_manager_rust" }

[build-dependencies]
prost-build = { workspace = true, features = ["vendored"] }
napi-build-ohos = { workspace = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt-multi-thread", "macros"] }
criterion = { workspace = true, features = ["html_reports"] }

[[bench]]
name = "performance_benchmarks"
harness = false

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use rust_logger::{
    core::{LogEntry, LogLevel},
    config::LoggerConfig,
    processor::LogProcessor,
    formatter::LogFormatter,
};

/// 基准测试：日志处理器性能
fn benchmark_log_processor(c: &mut Criterion) {
    let mut group = c.benchmark_group("log_processor");
    group.warm_up_time(std::time::Duration::from_millis(500));
    group.measurement_time(std::time::Duration::from_secs(2));
    group.sample_size(50);

    // 创建测试数据
    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.set_max_log_length(1000);
    config.max_logs_per_second = 0; // 不限流

    // 减少测试用例，只测试关键尺寸
    let message_sizes = vec![100, 500];

    for size in message_sizes {
        let message = "a".repeat(size);
        let entry = LogEntry::new(
            LogLevel::Info,
            "BenchmarkTag".to_string(),
            message,
            vec!["arg1".to_string(), "arg2".to_string()],
            vec![false, false],
        );

        group.bench_with_input(
            BenchmarkId::new("process_log_entry", size),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }

    group.finish();
}

/// 基准测试：日志格式化性能
fn benchmark_log_formatter(c: &mut Criterion) {
    let mut group = c.benchmark_group("log_formatter");
    group.warm_up_time(std::time::Duration::from_millis(500));
    group.measurement_time(std::time::Duration::from_secs(2));
    group.sample_size(50);

    let mut formatter = LogFormatter::new();
    let mut config = LoggerConfig::new();
    config.session_id = "benchmark-session-12345".to_string();
    config.user_id = "benchmark-user".to_string();
    config.device_id = Some("benchmark-device".to_string());
    formatter.initialize(&mut config).unwrap();

    // 只测试关键场景
    let test_cases = vec![
        ("simple", LogEntry::new(
            LogLevel::Info,
            "Simple".to_string(),
            "Simple message".to_string(),
            vec![],
            vec![],
        )),
        ("with_args", LogEntry::new(
            LogLevel::Warn,
            "WithArgs".to_string(),
            "Message with {} and {}".to_string(),
            vec!["arg1".to_string(), "arg2".to_string()],
            vec![false, false],
        )),
    ];

    for (name, entry) in test_cases {
        group.bench_with_input(
            BenchmarkId::new("format_entry", name),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = formatter.format_entry(black_box(entry), black_box(&config));
                    black_box(result)
                })
            },
        );
    }

    group.finish();
}

/// 基准测试：敏感信息脱敏性能
fn benchmark_sensitive_data_processing(c: &mut Criterion) {
    let mut group = c.benchmark_group("sensitive_data");
    group.warm_up_time(std::time::Duration::from_millis(500));
    group.measurement_time(std::time::Duration::from_secs(2));
    group.sample_size(50);

    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.disable_sensitive_words = false; // 启用脱敏

    // 只测试关键场景
    let test_cases = vec![
        ("no_sensitive", "This is a normal message without sensitive data"),
        ("phone_only", "Contact me at 13812345678 for more info"),
        ("mixed", "Phone: 13812345678, Email: <EMAIL>"),
    ];

    for (name, message) in test_cases {
        let entry = LogEntry::new(
            LogLevel::Info,
            "SensitiveTest".to_string(),
            message.to_string(),
            vec![],
            vec![],
        );

        group.bench_with_input(
            BenchmarkId::new("process_sensitive", name),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }

    group.finish();
}

/// 基准测试：限流性能
fn benchmark_rate_limiting(c: &mut Criterion) {
    let mut group = c.benchmark_group("rate_limiting");
    group.warm_up_time(std::time::Duration::from_millis(300));
    group.measurement_time(std::time::Duration::from_secs(1));
    group.sample_size(30);

    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);

    // 只测试关键场景：无限流和有限流
    let rate_limits = vec![0, 100]; // 0表示不限流

    for rate_limit in rate_limits {
        config.max_logs_per_second = rate_limit;

        let entry = LogEntry::new(
            LogLevel::Info,
            "RateTest".to_string(),
            "Rate limiting test message".to_string(),
            vec![],
            vec![],
        );

        group.bench_with_input(
            BenchmarkId::new("rate_limit", rate_limit),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }

    group.finish();
}

criterion_group!(
    benches,
    benchmark_log_processor,
    benchmark_log_formatter,
    benchmark_sensitive_data_processing,
    benchmark_rate_limiting
);

criterion_main!(benches);

use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use rust_logger::{
    core::{Log<PERSON><PERSON>ry, LogLevel, Logger},
    config::LoggerConfig,
    processor::LogProcessor,
    formatter::LogFormatter,
};
use std::time::Duration;
use std::sync::Arc;

/// 基准测试：日志处理器性能
fn benchmark_log_processor(c: &mut Criterion) {
    let mut group = c.benchmark_group("log_processor");
    
    // 创建测试数据
    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.set_max_log_length(1000);
    config.max_logs_per_second = 0; // 不限流
    
    // 测试不同长度的日志消息
    let message_sizes = vec![50, 200, 500, 1000];
    
    for size in message_sizes {
        let message = "a".repeat(size);
        let entry = LogEntry::new(
            LogLevel::Info,
            "BenchmarkTag".to_string(),
            message,
            vec!["arg1".to_string(), "arg2".to_string()],
            vec![false, false],
        );
        
        group.bench_with_input(
            BenchmarkId::new("process_log_entry", size),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

/// 基准测试：日志格式化性能
fn benchmark_log_formatter(c: &mut Criterion) {
    let mut group = c.benchmark_group("log_formatter");
    
    let mut formatter = LogFormatter::new();
    let mut config = LoggerConfig::new();
    config.session_id = "benchmark-session-12345".to_string();
    config.user_id = "benchmark-user".to_string();
    config.device_id = Some("benchmark-device".to_string());
    formatter.initialize(&mut config).unwrap();
    
    // 测试不同复杂度的日志条目
    let test_cases = vec![
        ("simple", LogEntry::new(
            LogLevel::Info,
            "Simple".to_string(),
            "Simple message".to_string(),
            vec![],
            vec![],
        )),
        ("with_args", LogEntry::new(
            LogLevel::Warn,
            "WithArgs".to_string(),
            "Message with {} and {}".to_string(),
            vec!["arg1".to_string(), "arg2".to_string()],
            vec![false, false],
        )),
        ("long_message", LogEntry::new(
            LogLevel::Error,
            "LongMessage".to_string(),
            "Very long message ".repeat(50),
            vec!["long_arg".repeat(20)],
            vec![false],
        )),
    ];
    
    for (name, entry) in test_cases {
        group.bench_with_input(
            BenchmarkId::new("format_entry", name),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = formatter.format_entry(black_box(entry), black_box(&config));
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

/// 基准测试：敏感信息脱敏性能
fn benchmark_sensitive_data_processing(c: &mut Criterion) {
    let mut group = c.benchmark_group("sensitive_data");
    
    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.disable_sensitive_words = false; // 启用脱敏
    
    // 测试不同类型的敏感信息
    let test_cases = vec![
        ("no_sensitive", "This is a normal message without sensitive data"),
        ("phone_only", "Contact me at 13812345678 for more info"),
        ("email_only", "Send <NAME_EMAIL>"),
        ("id_only", "ID number: 110101199001011234"),
        ("mixed", "Phone: 13812345678, Email: <EMAIL>, ID: 110101199001011234"),
        ("multiple_phones", "Call 13812345678 or 13987654321 or 15612345678"),
    ];
    
    for (name, message) in test_cases {
        let entry = LogEntry::new(
            LogLevel::Info,
            "SensitiveTest".to_string(),
            message.to_string(),
            vec![],
            vec![],
        );
        
        group.bench_with_input(
            BenchmarkId::new("process_sensitive", name),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

/// 基准测试：限流性能
fn benchmark_rate_limiting(c: &mut Criterion) {
    let mut group = c.benchmark_group("rate_limiting");
    
    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    
    // 测试不同的限流设置
    let rate_limits = vec![0, 10, 100, 1000]; // 0表示不限流
    
    for rate_limit in rate_limits {
        config.max_logs_per_second = rate_limit;
        
        let entry = LogEntry::new(
            LogLevel::Info,
            "RateTest".to_string(),
            "Rate limiting test message".to_string(),
            vec![],
            vec![],
        );
        
        group.bench_with_input(
            BenchmarkId::new("rate_limit", rate_limit),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }
    
    group.finish();
}

/// 基准测试：简化的日志处理器性能对比
fn benchmark_simplified_processor_performance(c: &mut Criterion) {
    let mut group = c.benchmark_group("simplified_processor");

    let processor = LogProcessor::new();
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.set_log_level(LogLevel::Debug);
    config.max_logs_per_second = 0; // 不限流

    // 测试不同复杂度的日志条目
    let test_cases = vec![
        ("simple", LogEntry::new(
            LogLevel::Info,
            "Simple".to_string(),
            "Simple message".to_string(),
            vec![],
            vec![],
        )),
        ("with_sensitive_data", LogEntry::new(
            LogLevel::Info,
            "Sensitive".to_string(),
            "Phone: 13812345678, Email: <EMAIL>".to_string(),
            vec![],
            vec![],
        )),
        ("complex", LogEntry::new(
            LogLevel::Warn,
            "Complex".to_string(),
            "Complex message with phone 13812345678 and long content ".repeat(10),
            vec!["arg1".to_string(), "arg2".to_string()],
            vec![false, false],
        )),
    ];

    for (name, entry) in test_cases {
        group.bench_with_input(
            BenchmarkId::new("process_entry", name),
            &entry,
            |b, entry| {
                b.iter(|| {
                    let result = processor.process(black_box(entry.clone()), black_box(&config));
                    black_box(result)
                })
            },
        );
    }

    group.finish();
}

/// 基准测试：内存分配优化效果
fn benchmark_memory_allocations(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_allocations");
    
    let mut formatter = LogFormatter::new();
    let mut config = LoggerConfig::new();
    config.session_id = "mem-test-session".to_string();
    formatter.initialize(&mut config).unwrap();
    
    // 比较优化前后的字符串分配
    let entry = LogEntry::new(
        LogLevel::Info,
        "MemTest".to_string(),
        "Memory allocation test message".to_string(),
        vec!["arg1".to_string()],
        vec![false],
    );
    
    group.bench_function("optimized_formatting", |b| {
        b.iter(|| {
            let result = formatter.format_entry(black_box(&entry), black_box(&config));
            black_box(result)
        })
    });
    
    group.finish();
}

criterion_group!(
    benches,
    benchmark_log_processor,
    benchmark_log_formatter,
    benchmark_sensitive_data_processing,
    benchmark_rate_limiting,
    benchmark_simplified_processor_performance,
    benchmark_memory_allocations
);

criterion_main!(benches);

use rust_logger::{
    core::{LogEntry, LogLevel, Logger},
    config::LoggerConfig,
};
use std::sync::{Arc, Barrier};
use std::thread;
use std::time::{Duration, Instant};
use std::sync::atomic::{AtomicUsize, Ordering};

/// 测试高并发日志写入性能 - 7000条/秒
#[test]
fn test_7000_logs_per_second_performance() {
    println!("🚀 Testing 7000 logs per second performance...");

    let temp_dir = std::env::temp_dir().join("rust_logger_high_volume_test");
    std::fs::create_dir_all(&temp_dir).unwrap();

    // 配置logger
    let mut config = LoggerConfig::new();
    config.set_log_directory(temp_dir.to_string_lossy().to_string());
    config.set_privacy_agreed(true);
    config.set_log_level(LogLevel::Debug);
    config.enable_console_output = true; // 启用控制台输出测试
    config.max_logs_per_second = 0; // 不限流，测试最大性能
    config.session_id = "perf-test-session".to_string();

    let logger = Arc::new({
        let mut logger = Logger::new();
        logger.initialize(config).unwrap();
        logger
    });

    // 测试参数
    const TARGET_LOGS_PER_SECOND: usize = 7000;
    const TEST_DURATION_SECONDS: u64 = 3; // 测试3秒
    const TOTAL_LOGS: usize = TARGET_LOGS_PER_SECOND * TEST_DURATION_SECONDS as usize;
    const NUM_THREADS: usize = 8; // 8个线程并发写入
    const LOGS_PER_THREAD: usize = TOTAL_LOGS / NUM_THREADS;

    println!("📋 Test Configuration:");
    println!("  Target: {} logs/second", TARGET_LOGS_PER_SECOND);
    println!("  Duration: {} seconds", TEST_DURATION_SECONDS);
    println!("  Total logs: {}", TOTAL_LOGS);
    println!("  Threads: {}", NUM_THREADS);
    println!("  Logs per thread: {}", LOGS_PER_THREAD);

    let barrier = Arc::new(Barrier::new(NUM_THREADS));
    let successful_logs = Arc::new(AtomicUsize::new(0));
    let failed_logs = Arc::new(AtomicUsize::new(0));
    let start_time = Arc::new(std::sync::Mutex::new(None));

    // 启动多个线程并发写入
    let handles: Vec<_> = (0..NUM_THREADS).map(|thread_id| {
        let logger = Arc::clone(&logger);
        let barrier = Arc::clone(&barrier);
        let successful_logs = Arc::clone(&successful_logs);
        let failed_logs = Arc::clone(&failed_logs);
        let start_time = Arc::clone(&start_time);

        thread::spawn(move || {
            // 等待所有线程准备就绪
            barrier.wait();

            // 记录开始时间
            {
                let mut start = start_time.lock().unwrap();
                if start.is_none() {
                    *start = Some(Instant::now());
                }
            }

            let thread_start = Instant::now();
            let mut thread_successful = 0;
            let mut thread_failed = 0;

            for log_id in 0..LOGS_PER_THREAD {
                let entry = LogEntry::new(
                    LogLevel::Info,
                    format!("PerfTest{}", thread_id),
                    format!("High volume test log {} from thread {} at {:?}", 
                           log_id, thread_id, Instant::now()),
                    vec![format!("thread_{}", thread_id), format!("log_{}", log_id)],
                    vec![false, false],
                );

                match logger.write_log(entry) {
                    Ok(()) => thread_successful += 1,
                    Err(_) => thread_failed += 1,
                }

                // 控制写入速度，避免过快导致系统负载过高
                if log_id % 100 == 0 {
                    thread::sleep(Duration::from_micros(100));
                }
            }

            let thread_duration = thread_start.elapsed();
            println!("🧵 Thread {} completed: {} successful, {} failed, duration: {:?}", 
                     thread_id, thread_successful, thread_failed, thread_duration);

            successful_logs.fetch_add(thread_successful, Ordering::Relaxed);
            failed_logs.fetch_add(thread_failed, Ordering::Relaxed);

            (thread_successful, thread_failed)
        })
    }).collect();

    // 等待所有线程完成
    let _results: Vec<(usize, usize)> = handles.into_iter()
        .map(|h| h.join().unwrap())
        .collect();

    let total_duration = {
        let start = start_time.lock().unwrap();
        start.unwrap().elapsed()
    };

    let total_successful = successful_logs.load(Ordering::Relaxed);
    let total_failed = failed_logs.load(Ordering::Relaxed);
    let actual_logs_per_second = total_successful as f64 / total_duration.as_secs_f64();

    // 强制flush确保所有日志都写入文件
    drop(logger);

    println!("\n📊 Performance Test Results:");
    println!("  Total duration: {:?}", total_duration);
    println!("  Successful logs: {}", total_successful);
    println!("  Failed logs: {}", total_failed);
    println!("  Success rate: {:.2}%", 
             (total_successful as f64 / TOTAL_LOGS as f64) * 100.0);
    println!("  Actual logs/second: {:.0}", actual_logs_per_second);
    println!("  Target logs/second: {}", TARGET_LOGS_PER_SECOND);
    println!("  Performance ratio: {:.2}%", 
             (actual_logs_per_second / TARGET_LOGS_PER_SECOND as f64) * 100.0);

    // 验证文件写入
    let log_files: Vec<_> = std::fs::read_dir(&temp_dir)
        .unwrap()
        .filter_map(|entry| {
            let entry = entry.ok()?;
            let path = entry.path();
            if path.extension()? == "log" {
                Some(path)
            } else {
                None
            }
        })
        .collect();

    if !log_files.is_empty() {
        let total_file_size: u64 = log_files.iter()
            .map(|file| std::fs::metadata(file).unwrap().len())
            .sum();

        println!("\n📁 File Write Results:");
        println!("  Log files created: {}", log_files.len());
        println!("  Total file size: {} bytes ({:.2} KB)", 
                 total_file_size, total_file_size as f64 / 1024.0);
        println!("  Average bytes per log: {:.1}", 
                 total_file_size as f64 / total_successful as f64);
    }

    // 性能断言
    assert!(total_successful >= TOTAL_LOGS * 80 / 100, 
            "Success rate too low: {}/{} ({:.1}%)", 
            total_successful, TOTAL_LOGS, 
            (total_successful as f64 / TOTAL_LOGS as f64) * 100.0);

    assert!(actual_logs_per_second >= TARGET_LOGS_PER_SECOND as f64 * 0.7, 
            "Performance too low: {:.0} logs/second (target: {})", 
            actual_logs_per_second, TARGET_LOGS_PER_SECOND);

    // 清理测试文件
    let _ = std::fs::remove_dir_all(&temp_dir);

    if actual_logs_per_second >= TARGET_LOGS_PER_SECOND as f64 {
        println!("\n✅ Performance test PASSED! Can handle 7000+ logs/second");
    } else {
        println!("\n⚠️  Performance test PARTIAL: {:.0} logs/second (target: {})", 
                 actual_logs_per_second, TARGET_LOGS_PER_SECOND);
    }
}

/// 测试控制台输出性能影响
#[test]
fn test_console_output_performance_impact() {
    println!("🖥️ Testing console output performance impact...");

    let temp_dir = std::env::temp_dir().join("rust_logger_console_perf_test");
    std::fs::create_dir_all(&temp_dir).unwrap();

    const TEST_LOGS: usize = 1000;

    // 测试1: 禁用控制台输出
    let duration_no_console = {
        let mut config = LoggerConfig::new();
        config.set_log_directory(temp_dir.to_string_lossy().to_string());
        config.set_privacy_agreed(true);
        config.set_log_level(LogLevel::Debug);
        config.enable_console_output = false; // 禁用控制台
        config.max_logs_per_second = 0;

        let mut logger = Logger::new();
        logger.initialize(config).unwrap();

        let start = Instant::now();
        for i in 0..TEST_LOGS {
            let entry = LogEntry::new(
                LogLevel::Info,
                "NoConsole".to_string(),
                format!("Test message {}", i),
                vec![],
                vec![],
            );
            let _ = logger.write_log(entry);
        }
        drop(logger);
        start.elapsed()
    };

    // 测试2: 启用控制台输出
    let duration_with_console = {
        let mut config = LoggerConfig::new();
        config.set_log_directory(temp_dir.to_string_lossy().to_string());
        config.set_privacy_agreed(true);
        config.set_log_level(LogLevel::Debug);
        config.enable_console_output = true; // 启用控制台
        config.max_logs_per_second = 0;

        let mut logger = Logger::new();
        logger.initialize(config).unwrap();

        let start = Instant::now();
        for i in 0..TEST_LOGS {
            let entry = LogEntry::new(
                LogLevel::Info,
                "WithConsole".to_string(),
                format!("Test message {}", i),
                vec![],
                vec![],
            );
            let _ = logger.write_log(entry);
        }
        drop(logger);
        start.elapsed()
    };

    let performance_impact = duration_with_console.as_secs_f64() / duration_no_console.as_secs_f64();

    println!("\n📊 Console Output Performance Impact:");
    println!("  Without console: {:?} ({:.0} logs/second)", 
             duration_no_console, 
             TEST_LOGS as f64 / duration_no_console.as_secs_f64());
    println!("  With console: {:?} ({:.0} logs/second)", 
             duration_with_console,
             TEST_LOGS as f64 / duration_with_console.as_secs_f64());
    println!("  Performance impact: {:.2}x slower", performance_impact);

    // 清理测试文件
    let _ = std::fs::remove_dir_all(&temp_dir);

    // 控制台输出不应该造成超过3倍的性能损失
    assert!(performance_impact < 3.0, 
            "Console output causes too much performance impact: {:.2}x", 
            performance_impact);

    println!("✅ Console output performance impact is acceptable");
}

// 测试mmap存储功能的集成测试

use rust_logger::{
    config::LoggerConfig,
    storage::LogStorage,
    mmap_storage::{StorageConfig, ThreadSafeMmapStorage, MmapLogStorage},
};
use std::path::PathBuf;

fn create_temp_dir() -> PathBuf {
    use std::time::{SystemTime, UNIX_EPOCH};
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_nanos();
    let temp_dir = std::env::temp_dir().join(format!("rust_logger_test_{}_{}",
        std::process::id(), timestamp));
    std::fs::create_dir_all(&temp_dir).unwrap();
    temp_dir
}

fn create_test_config() -> LoggerConfig {
    let mut config = LoggerConfig::default();
    config.log_directory = "/tmp/test_logs".to_string();
    config.log_file_prefix = "test_log".to_string();
    config.max_file_size = 1024 * 1024; // 1MB
    config.max_directory_size = 10 * 1024 * 1024; // 10MB
    config.enable_console_output = true;
    config.enable_full_log = true;
    config.log_level = rust_logger::core::LogLevel::Debug;
    config.is_debug_mode = true; // 设置为测试模式
    config.app_version = Some("1.0.0".to_string());
    config.device_id = Some("test_device".to_string());
    config.user_id = "test_user".to_string();
    config
}

#[test]
fn test_mmap_storage_creation() {
    let log_dir = create_temp_dir();
    
    let config = StorageConfig::new(&log_dir, "test_log", 10 * 1024 * 1024).unwrap(); // 10MB
    let storage = ThreadSafeMmapStorage::new(config).unwrap();
    
    // 检查缓冲区使用情况
    let (used, total) = storage.buffer_usage();
    assert_eq!(used, 0);
    assert_eq!(total, 150 * 1024); // 150KB
}

#[test]
fn test_mmap_storage_write() {
    let log_dir = create_temp_dir();
    
    let config = StorageConfig::new(&log_dir, "test_log", 10 * 1024 * 1024).unwrap(); // 10MB
    let storage = ThreadSafeMmapStorage::new(config).unwrap();
    
    // 写入一些测试数据
    let test_data = b"Hello, mmap storage!";
    storage.write_protobuf(test_data).unwrap();
    
    // 检查缓冲区使用情况（包含varint长度前缀）
    let (used, _) = storage.buffer_usage();
    // varint编码：小于128的数字用1字节编码
    let expected_varint_len = if test_data.len() < 128 { 1 } else { 2 };
    assert_eq!(used, test_data.len() + expected_varint_len);
}

#[test]
fn test_mmap_storage_flush() {
    let log_dir = create_temp_dir();

    let config = StorageConfig::new(&log_dir, "test_log", 10 * 1024 * 1024).unwrap(); // 10MB
    let storage = ThreadSafeMmapStorage::new(config).unwrap();

    // 写入测试数据
    let test_data = b"Test flush functionality";
    storage.write_protobuf(test_data).unwrap();

    // 手动flush
    storage.flush_to_file().unwrap();

    // 检查缓冲区已清空
    let (used, _) = storage.buffer_usage();
    assert_eq!(used, 0);

    // 检查文件是否创建（使用实际的文件名格式）
    // 实际格式：YYYY-MM-DD-HH_MM_SS.microseconds+timezone.log
    let log_files: Vec<_> = std::fs::read_dir(&log_dir)
        .unwrap()
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            entry.path().extension().map_or(false, |ext| ext == "log")
        })
        .collect();
    assert!(!log_files.is_empty(), "No log files found in directory");

    // 检查文件内容（protobuf二进制数据）
    let log_file_path = log_files[0].path();
    let file_content = std::fs::read(&log_file_path).unwrap();
    assert!(!file_content.is_empty());
    // protobuf数据包含varint长度前缀，所以文件内容应该比原始数据更长
    // varint编码：小于128的数字用1字节编码
    let expected_varint_len = if test_data.len() < 128 { 1 } else { 2 };
    assert_eq!(file_content.len(), test_data.len() + expected_varint_len);
}

#[test]
fn test_mmap_storage_auto_flush() {
    let log_dir = create_temp_dir();
    
    let config = StorageConfig::new(&log_dir, "test_log", 10 * 1024 * 1024).unwrap(); // 10MB
    let storage = ThreadSafeMmapStorage::new(config).unwrap();
    
    // 写入大量数据触发自动flush（超过50KB阈值）
    let large_data = vec![b'A'; 60 * 1024]; // 60KB
    storage.write_protobuf(&large_data).unwrap();
    
    // 应该已经自动flush了
    let (used, _) = storage.buffer_usage();
    assert_eq!(used, 0);
    
    // 检查文件是否创建（使用实际的文件名格式）
    let log_files: Vec<_> = std::fs::read_dir(&log_dir)
        .unwrap()
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            entry.path().extension().map_or(false, |ext| ext == "log")
        })
        .collect();
    assert!(!log_files.is_empty(), "No log files found in directory");
}

#[test]
fn test_log_storage_integration() {
    let log_dir = create_temp_dir();
    let mut config = create_test_config();
    config.log_directory = log_dir.to_str().unwrap().to_string();
    
    let mut storage = LogStorage::new();
    storage.initialize(&config).unwrap();
    
    // 写入protobuf日志
    storage.write_log_protobuf("INFO", "TestTag", "Test message", &config).unwrap();
    
    // 手动flush
    storage.flush().unwrap();
    
    // 检查日志文件是否创建
    let log_files = storage.get_all_log_files(&config).unwrap();
    assert!(!log_files.is_empty());
    
    // 检查文件扩展名是.log
    let log_file = &log_files[0];
    assert!(log_file.extension().unwrap() == "log");
}

#[test]
fn test_should_flush_threshold() {
    let log_dir = create_temp_dir();

    let config = StorageConfig::new(&log_dir, "test_log", 10 * 1024 * 1024).unwrap(); // 10MB
    let storage = ThreadSafeMmapStorage::new(config).unwrap();

    // 写入接近阈值的数据（49KB）
    let data_49kb = vec![b'B'; 49 * 1024];
    storage.write_protobuf(&data_49kb).unwrap();

    // 应该还不需要flush
    assert!(!storage.should_flush());

    // 检查缓冲区使用情况（包含varint长度前缀）
    let (used, _total) = storage.buffer_usage();
    // 49KB = 50176字节，需要3字节varint编码（50176 > 16383）
    assert_eq!(used, 49 * 1024 + 3); // +3 for varint prefix

    // 检查如果再加2KB是否会超过阈值
    let would_exceed = used + 2 * 1024 > 50 * 1024;
    assert!(would_exceed);
}

#[test]
fn test_multiple_writes_and_flushes() {
    let log_dir = create_temp_dir();

    let config = StorageConfig::new(&log_dir, "test_log", 10 * 1024 * 1024).unwrap(); // 10MB
    let storage = ThreadSafeMmapStorage::new(config).unwrap();

    // 多次写入和flush
    let mut expected_content = Vec::new();
    for i in 0..5 {
        let test_data = format!("Test message {}", i);
        expected_content.extend_from_slice(test_data.as_bytes());
        storage.write_protobuf(test_data.as_bytes()).unwrap();
        storage.flush_to_file().unwrap();
    }

    // 检查文件内容
    let log_files: Vec<_> = std::fs::read_dir(&log_dir)
        .unwrap()
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            entry.path().extension().map_or(false, |ext| ext == "log")
        })
        .collect();
    assert!(!log_files.is_empty(), "No log files found in directory");
    let log_file_path = log_files[0].path();

    // 检查文件内容（二进制数据）
    let file_content = std::fs::read(&log_file_path).unwrap();
    assert!(!file_content.is_empty());
    // protobuf格式的数据包含varint前缀，所以文件大小应该大于原始数据
    // 但不一定等于原始内容长度，因为有编码开销
    assert!(file_content.len() > 0);
    // 验证文件包含了多次写入的数据（5次写入，每次都有varint前缀）
    assert!(file_content.len() >= 5); // 至少有5个varint前缀
}

#[test]
fn test_app_restart_recovery() {
    let log_dir = create_temp_dir();

    // 第一次运行：写入数据但不flush
    {
        let config = StorageConfig::new(&log_dir, "test_recovery", 10 * 1024 * 1024).unwrap(); // 10MB
        let mut storage = MmapLogStorage::new(config).unwrap();

        // 写入一些数据但不flush（模拟应用异常退出）
        let data1 = b"First log entry";
        let data2 = b"Second log entry";
        storage.write_protobuf(data1).unwrap();
        storage.write_protobuf(data2).unwrap();

        // 验证数据在缓冲区中
        let (used, _) = storage.buffer_usage();
        assert!(used > 0);

        // 不调用flush，直接drop（模拟应用退出时的Drop行为）
    }

    // 第二次运行：应该自动恢复mmap中的数据
    {
        let config = StorageConfig::new(&log_dir, "test_recovery", 10 * 1024 * 1024).unwrap(); // 10MB
        let storage = MmapLogStorage::new(config).unwrap();

        // 新创建的storage应该已经flush了之前的数据
        let (used, _) = storage.buffer_usage();
        assert_eq!(used, 0, "Buffer should be empty after recovery flush");

        // 检查log文件是否被创建
        let log_files: Vec<_> = std::fs::read_dir(&log_dir)
            .unwrap()
            .filter_map(|entry| entry.ok())
            .filter(|entry| {
                entry.path().extension().map_or(false, |ext| ext == "log")
            })
            .collect();
        assert!(!log_files.is_empty(), "Log file should exist after recovery");

        // 检查文件内容
        let log_file_path = log_files[0].path();
        let file_content = std::fs::read(&log_file_path).unwrap();
        assert!(!file_content.is_empty(), "Log file should contain recovered data");
    }
}

#[test]
fn test_threadsafe_storage_drop() {
    let log_dir = create_temp_dir();

    // 创建ThreadSafeMmapStorage并写入数据
    {
        let config = StorageConfig::new(&log_dir, "test_drop", 10 * 1024 * 1024).unwrap(); // 10MB
        let storage = ThreadSafeMmapStorage::new(config).unwrap();

        // 写入数据但不手动flush
        let data = b"Data that should be flushed on drop";
        storage.write_protobuf(data).unwrap();

        // 验证数据在缓冲区中
        let (used, _) = storage.buffer_usage();
        assert!(used > 0);

        // 当storage被drop时，应该自动flush
    }

    // 检查文件是否被创建
    let log_files: Vec<_> = std::fs::read_dir(&log_dir)
        .unwrap()
        .filter_map(|entry| entry.ok())
        .filter(|entry| {
            entry.path().extension().map_or(false, |ext| ext == "log")
        })
        .collect();
    assert!(!log_files.is_empty(), "Log file should exist after ThreadSafeMmapStorage drop");

    // 检查文件内容
    let log_file_path = log_files[0].path();
    let file_content = std::fs::read(&log_file_path).unwrap();
    assert!(!file_content.is_empty(), "Log file should contain data flushed on drop");
}

#[test]
fn test_protobuf_serialization_in_files() {
    use rust_logger::protobuf::LogMetaData;

    let log_dir = create_temp_dir();
    let mut config = create_test_config();
    config.log_directory = log_dir.to_str().unwrap().to_string();

    let mut storage = LogStorage::new();
    storage.initialize(&config).unwrap();

    // 写入一条测试日志
    let test_message = "Test protobuf serialization in file";
    storage.write_log_protobuf("INFO", "TestTag", test_message, &config).unwrap();

    // 手动flush确保数据写入文件
    storage.flush().unwrap();

    // 获取日志文件
    let log_files = storage.get_all_log_files(&config).unwrap();
    assert!(!log_files.is_empty(), "Should have at least one log file");

    // 读取文件内容（二进制）
    let log_file_path = &log_files[0];
    let file_content = std::fs::read(log_file_path).unwrap();
    assert!(!file_content.is_empty(), "Log file should not be empty");

    // 验证文件内容是protobuf格式
    // 文件格式：varint长度前缀 + protobuf数据
    let mut cursor = std::io::Cursor::new(&file_content);

    // 尝试解析protobuf数据
    let decoded_metadata = LogMetaData::read_delimited_from(&mut cursor).unwrap();
    assert!(decoded_metadata.is_some(), "Should be able to decode protobuf data from file");

    let metadata = decoded_metadata.unwrap();

    // 验证解析出的数据
    assert_eq!(metadata.level, "INFO");
    assert_eq!(metadata.tag, "TestTag");
    assert_eq!(metadata.log_message, test_message);
    assert_eq!(metadata.user_id, "test_user");
    assert_eq!(metadata.test_mode, "测试模式"); // is_debug_mode = true in create_test_config
    assert!(!metadata.time.is_empty());
    assert!(!metadata.session_id.is_empty());

    println!("✅ 验证成功：文件中的日志确实是protobuf序列化格式");
    println!("📄 文件大小: {} bytes", file_content.len());
    println!("📋 解析出的日志: {:?}", metadata);
}

#[test]
fn test_multiple_protobuf_logs_in_file() {
    use rust_logger::protobuf::LogMetaData;

    let log_dir = create_temp_dir();
    let mut config = create_test_config();
    config.log_directory = log_dir.to_str().unwrap().to_string();

    let mut storage = LogStorage::new();
    storage.initialize(&config).unwrap();

    // 写入多条不同级别的日志
    let test_logs = vec![
        ("DEBUG", "DebugTag", "Debug message 1"),
        ("INFO", "InfoTag", "Info message 2"),
        ("WARN", "WarnTag", "Warning message 3"),
        ("ERROR", "ErrorTag", "Error message 4"),
    ];

    for (level, tag, message) in &test_logs {
        storage.write_log_protobuf(level, tag, message, &config).unwrap();
    }

    // 手动flush确保所有数据写入文件
    storage.flush().unwrap();

    // 获取日志文件
    let log_files = storage.get_all_log_files(&config).unwrap();
    assert!(!log_files.is_empty(), "Should have at least one log file");

    // 读取文件内容并解析所有protobuf条目
    let log_file_path = &log_files[0];
    let file_content = std::fs::read(log_file_path).unwrap();
    assert!(!file_content.is_empty(), "Log file should not be empty");

    let mut cursor = std::io::Cursor::new(&file_content);
    let mut parsed_logs = Vec::new();

    // 解析文件中的所有protobuf条目
    while let Ok(Some(metadata)) = LogMetaData::read_delimited_from(&mut cursor) {
        parsed_logs.push(metadata);
    }

    // 验证解析出的日志数量
    assert_eq!(parsed_logs.len(), test_logs.len(), "Should parse all {} log entries", test_logs.len());

    // 验证每条日志的内容
    for (i, (expected_level, expected_tag, expected_message)) in test_logs.iter().enumerate() {
        let metadata = &parsed_logs[i];
        assert_eq!(metadata.level, *expected_level);
        assert_eq!(metadata.tag, *expected_tag);
        assert_eq!(metadata.log_message, *expected_message);
        assert_eq!(metadata.user_id, "test_user");
        assert_eq!(metadata.test_mode, "测试模式");
        assert!(!metadata.time.is_empty());
        assert!(!metadata.session_id.is_empty());

        // 验证时间格式符合Android标准：yyyy-MM-dd-HH:mm:ss.SSSSSSZ
        assert!(metadata.time.contains("-"), "Time should contain date separators");
        assert!(metadata.time.contains(":"), "Time should contain time separators");
        assert!(metadata.time.contains("+") || metadata.time.contains("Z"), "Time should contain timezone");
    }

    println!("✅ 验证成功：文件中包含 {} 条protobuf序列化的日志", parsed_logs.len());
    println!("📄 文件总大小: {} bytes", file_content.len());
    println!("📋 平均每条日志大小: {:.1} bytes", file_content.len() as f64 / parsed_logs.len() as f64);

    // 验证文件格式：每条日志都是 varint长度前缀 + protobuf数据
    let mut pos = 0;
    let mut total_parsed_bytes = 0;
    let mut entry_count = 0;

    while pos < file_content.len() {
        let start_pos = pos;

        // 手动解析varint长度前缀
        let mut len = 0u64;
        let mut shift = 0;
        let mut varint_bytes = 0;

        loop {
            if pos >= file_content.len() {
                break;
            }

            let byte = file_content[pos];
            pos += 1;
            varint_bytes += 1;

            len |= ((byte & 0x7F) as u64) << shift;

            if byte & 0x80 == 0 {
                break;
            }

            shift += 7;
            if shift >= 64 {
                break; // 防止无限循环
            }
        }

        if pos + len as usize > file_content.len() {
            break; // 数据不完整
        }

        // 跳过protobuf数据
        pos += len as usize;

        let entry_size = pos - start_pos;
        total_parsed_bytes += entry_size;
        entry_count += 1;

        println!("📝 条目 {}: varint前缀长度={}, protobuf数据长度={}, 总长度={}",
                entry_count,
                varint_bytes,
                len,
                entry_size);
    }

    assert_eq!(total_parsed_bytes, file_content.len(), "All bytes should be accounted for");
    assert_eq!(entry_count, test_logs.len(), "Should have correct number of entries");
}

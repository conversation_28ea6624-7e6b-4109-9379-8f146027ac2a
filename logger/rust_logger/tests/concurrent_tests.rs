use rust_logger::{
    core::{Log<PERSON><PERSON>ry, LogLevel, Logger},
    config::LoggerConfig,
    processor::LogProcessor,
};
use std::sync::{Arc, Barrier};
use std::thread;
use std::time::{Duration, Instant};
use std::collections::HashSet;

/// 测试多线程并发写入日志
#[test]
fn test_concurrent_logging() {
    let temp_dir = std::env::temp_dir().join("rust_logger_concurrent_test");
    std::fs::create_dir_all(&temp_dir).unwrap();

    let mut config = LoggerConfig::new();
    config.set_log_directory(temp_dir.to_string_lossy().to_string());
    config.set_privacy_agreed(true);
    config.enable_console_output = false; // 禁用控制台输出以专注于文件写入
    config.set_log_level(LogLevel::Debug); // 设置为Debug级别以允许Info日志

    let logger = Arc::new({
        let mut logger = Logger::new();
        logger.initialize(config).unwrap();
        logger
    });

    const NUM_THREADS: usize = 8;
    const LOGS_PER_THREAD: usize = 100;
    
    let barrier = Arc::new(Barrier::new(NUM_THREADS));
    let start_time = Arc::new(std::sync::Mutex::new(None));

    let handles: Vec<_> = (0..NUM_THREADS).map(|thread_id| {
        let logger = Arc::clone(&logger);
        let barrier = Arc::clone(&barrier);
        let start_time = Arc::clone(&start_time);
        
        thread::spawn(move || {
            // 等待所有线程准备就绪
            barrier.wait();
            
            // 记录开始时间（只有第一个线程记录）
            {
                let mut start = start_time.lock().unwrap();
                if start.is_none() {
                    *start = Some(Instant::now());
                }
            }

            let mut successful_logs = 0;
            for log_id in 0..LOGS_PER_THREAD {
                let entry = LogEntry::new(
                    LogLevel::Info,
                    format!("Thread{}", thread_id),
                    format!("Concurrent log message {} from thread {}", log_id, thread_id),
                    vec![format!("thread_{}", thread_id), format!("log_{}", log_id)],
                    vec![false, false],
                );

                match logger.write_log(entry) {
                    Ok(()) => successful_logs += 1,
                    Err(e) => eprintln!("Thread {} failed to write log {}: {}", thread_id, log_id, e),
                }
            }
            
            successful_logs
        })
    }).collect();

    // 等待所有线程完成并收集结果
    let results: Vec<usize> = handles.into_iter().map(|h| h.join().unwrap()).collect();
    
    let total_successful = results.iter().sum::<usize>();
    let expected_total = NUM_THREADS * LOGS_PER_THREAD;
    
    println!("Concurrent logging test results:");
    println!("  Threads: {}", NUM_THREADS);
    println!("  Logs per thread: {}", LOGS_PER_THREAD);
    println!("  Expected total: {}", expected_total);
    println!("  Successful logs: {}", total_successful);
    println!("  Success rate: {:.2}%", (total_successful as f64 / expected_total as f64) * 100.0);

    // 验证大部分日志都成功写入（允许一些由于限流等原因失败）
    assert!(total_successful >= expected_total * 80 / 100, 
            "Success rate too low: {}/{}", total_successful, expected_total);

    // 清理测试文件
    let _ = std::fs::remove_dir_all(&temp_dir);
}

/// 测试处理器的线程安全性
#[test]
fn test_processor_thread_safety() {
    let processor = Arc::new(LogProcessor::new());
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.max_logs_per_second = 0; // 不限流
    let config = Arc::new(config);

    const NUM_THREADS: usize = 10;
    const OPERATIONS_PER_THREAD: usize = 50;

    let handles: Vec<_> = (0..NUM_THREADS).map(|thread_id| {
        let processor = Arc::clone(&processor);
        let config = Arc::clone(&config);
        
        thread::spawn(move || {
            let mut successful_operations = 0;
            
            for op_id in 0..OPERATIONS_PER_THREAD {
                let entry = LogEntry::new(
                    LogLevel::Info,
                    format!("ProcessorTest{}", thread_id),
                    format!("Thread safety test {} from thread {}", op_id, thread_id),
                    vec![format!("data_{}", op_id)],
                    vec![false],
                );

                match processor.process(entry, &config) {
                    Ok(_) => successful_operations += 1,
                    Err(e) => eprintln!("Thread {} operation {} failed: {}", thread_id, op_id, e),
                }
            }
            
            successful_operations
        })
    }).collect();

    let results: Vec<usize> = handles.into_iter().map(|h| h.join().unwrap()).collect();
    let total_successful = results.iter().sum::<usize>();
    let expected_total = NUM_THREADS * OPERATIONS_PER_THREAD;

    println!("Processor thread safety test results:");
    println!("  Total operations: {}", expected_total);
    println!("  Successful operations: {}", total_successful);

    // 处理器应该是完全线程安全的
    assert_eq!(total_successful, expected_total, 
               "Processor should handle all operations successfully");
}

/// 测试限流器在并发环境下的正确性
#[test]
fn test_rate_limiter_concurrency() {
    let processor = Arc::new(LogProcessor::new());
    let mut config = LoggerConfig::new();
    config.set_privacy_agreed(true);
    config.max_logs_per_second = 10; // 设置较低的限流值
    let config = Arc::new(config);

    const NUM_THREADS: usize = 5;
    const ATTEMPTS_PER_THREAD: usize = 20;

    let barrier = Arc::new(Barrier::new(NUM_THREADS));
    
    let handles: Vec<_> = (0..NUM_THREADS).map(|thread_id| {
        let processor = Arc::clone(&processor);
        let config = Arc::clone(&config);
        let barrier = Arc::clone(&barrier);
        
        thread::spawn(move || {
            // 等待所有线程同时开始
            barrier.wait();
            
            let mut successful = 0;
            let mut rate_limited = 0;
            
            for attempt in 0..ATTEMPTS_PER_THREAD {
                let entry = LogEntry::new(
                    LogLevel::Info,
                    format!("RateTest{}", thread_id),
                    format!("Rate limit test {} from thread {}", attempt, thread_id),
                    vec![],
                    vec![],
                );

                match processor.process(entry, &config) {
                    Ok(_) => successful += 1,
                    Err(e) => {
                        if e.to_string().contains("Rate limit exceeded") {
                            rate_limited += 1;
                        } else {
                            eprintln!("Unexpected error in thread {}: {}", thread_id, e);
                        }
                    }
                }
                
                // 小延迟以避免所有请求在同一毫秒内
                thread::sleep(Duration::from_millis(1));
            }
            
            (successful, rate_limited)
        })
    }).collect();

    let results: Vec<(usize, usize)> = handles.into_iter().map(|h| h.join().unwrap()).collect();
    
    let total_successful: usize = results.iter().map(|(s, _)| *s).sum();
    let total_rate_limited: usize = results.iter().map(|(_, r)| *r).sum();
    let total_attempts = NUM_THREADS * ATTEMPTS_PER_THREAD;

    println!("Rate limiter concurrency test results:");
    println!("  Total attempts: {}", total_attempts);
    println!("  Successful: {}", total_successful);
    println!("  Rate limited: {}", total_rate_limited);
    println!("  Rate limit: {} logs/second", config.max_logs_per_second);

    // 验证限流器正常工作
    assert!(total_rate_limited > 0, "Rate limiter should have blocked some requests");
    assert_eq!(total_successful + total_rate_limited, total_attempts, 
               "All attempts should be accounted for");
}

/// 测试配置更新的线程安全性
#[test]
fn test_config_update_thread_safety() {
    let temp_dir = std::env::temp_dir().join("rust_logger_config_test");
    std::fs::create_dir_all(&temp_dir).unwrap();

    let mut config = LoggerConfig::new();
    config.set_log_directory(temp_dir.to_string_lossy().to_string());
    config.set_privacy_agreed(true);
    config.set_log_level(LogLevel::Debug); // 设置为Debug级别以允许Info日志

    let logger = Arc::new({
        let mut logger = Logger::new();
        logger.initialize(config).unwrap();
        logger
    });

    const NUM_THREADS: usize = 4;
    const OPERATIONS_PER_THREAD: usize = 25;

    let handles: Vec<_> = (0..NUM_THREADS).map(|thread_id| {
        let logger = Arc::clone(&logger);
        
        thread::spawn(move || {
            for op_id in 0..OPERATIONS_PER_THREAD {
                // 交替进行日志写入和配置更新
                if op_id % 2 == 0 {
                    // 写入日志
                    let entry = LogEntry::new(
                        LogLevel::Info,
                        format!("ConfigTest{}", thread_id),
                        format!("Config test log {} from thread {}", op_id, thread_id),
                        vec![],
                        vec![],
                    );
                    let _ = logger.write_log(entry);
                } else {
                    // 更新配置
                    let new_user_id = format!("user_{}_{}", thread_id, op_id);
                    let _ = logger.update_user_id(new_user_id);
                }
                
                thread::sleep(Duration::from_millis(1));
            }
        })
    }).collect();

    // 等待所有线程完成
    for handle in handles {
        handle.join().unwrap();
    }

    println!("Config update thread safety test completed successfully");

    // 清理测试文件
    let _ = std::fs::remove_dir_all(&temp_dir);
}

/// 测试内存泄漏和资源清理
#[test]
fn test_memory_and_resource_cleanup() {
    const NUM_ITERATIONS: usize = 100;
    
    for iteration in 0..NUM_ITERATIONS {
        let temp_dir = std::env::temp_dir().join(format!("rust_logger_cleanup_test_{}", iteration));
        std::fs::create_dir_all(&temp_dir).unwrap();

        let mut config = LoggerConfig::new();
        config.set_log_directory(temp_dir.to_string_lossy().to_string());
        config.set_privacy_agreed(true);
        config.set_log_level(LogLevel::Debug); // 设置为Debug级别以允许Info日志

        {
            let mut logger = Logger::new();
            logger.initialize(config).unwrap();

            // 写入一些日志
            for i in 0..10 {
                let entry = LogEntry::new(
                    LogLevel::Info,
                    "CleanupTest".to_string(),
                    format!("Cleanup test message {}", i),
                    vec![],
                    vec![],
                );
                let _ = logger.write_log(entry);
            }
            
            // logger在这里被drop
        }

        // 清理测试文件
        let _ = std::fs::remove_dir_all(&temp_dir);
        
        if iteration % 20 == 0 {
            println!("Completed {} cleanup iterations", iteration + 1);
        }
    }

    println!("Memory and resource cleanup test completed successfully");
}

use rust_logger::{
    core::{Log<PERSON>ntry, Log<PERSON><PERSON>l, Logger},
    config::LoggerConfig,
    formatter::LogFormatter,
    protobuf::LogMetaData,
};
use prost::Message;

/// 测试tag字段在控制台输出和protobuf存储中的不同处理
#[test]
fn test_tag_format_difference() {
    println!("Testing tag format difference between console and protobuf...");

    let mut config = LoggerConfig::new();
    config.session_id = "test-session".to_string();
    config.user_id = "test-user".to_string();
    config.set_log_level(LogLevel::Debug);

    let mut formatter = LogFormatter::new();
    formatter.initialize(&mut config).unwrap();

    // 创建测试日志条目
    let entry = LogEntry::new(
        LogLevel::Info,
        "testTag".to_string(),
        "Test message".to_string(),
        vec![],
        vec![],
    );

    println!("Original tag: '{}'", entry.tag);

    // 1. 控制台输出格式
    let console_output = formatter.format_entry(&entry, &config).unwrap();
    println!("Console output: {}", console_output);

    // 2. Protobuf格式
    let protobuf_data = formatter.to_protobuf(&entry, &config).unwrap();
    let decoded_metadata = LogMetaData::decode(&protobuf_data[..]).unwrap();
    
    println!("Protobuf tag: '{}'", decoded_metadata.tag);

    // 验证控制台输出包含环境标识
    assert!(console_output.contains("testTag [env:SC]"), 
            "Console output should contain environment identifier");

    // 验证protobuf中的tag保持原始值，不包含环境标识
    assert_eq!(decoded_metadata.tag, "testTag", 
               "Protobuf tag should be original tag without environment identifier");

    // 确保protobuf中没有环境标识
    assert!(!decoded_metadata.tag.contains("[env:"), 
            "Protobuf tag should not contain environment identifier");

    println!("✅ Tag format test passed!");
    println!("   Console: testTag [env:SC] (with environment identifier)");
    println!("   Protobuf: testTag (original tag only)");
}

/// 测试完整的日志流程中tag的处理
#[test]
fn test_full_logging_tag_handling() {
    println!("Testing full logging pipeline tag handling...");

    let temp_dir = std::env::temp_dir().join("rust_logger_tag_test");
    std::fs::create_dir_all(&temp_dir).unwrap();

    let mut config = LoggerConfig::new();
    config.set_log_directory(temp_dir.to_string_lossy().to_string());
    config.set_privacy_agreed(true);
    config.set_log_level(LogLevel::Debug);
    config.enable_console_output = false;
    config.session_id = "tag-test-session".to_string();

    let mut logger = Logger::new();
    logger.initialize(config).unwrap();

    // 写入测试日志
    let test_tag = "MyTestTag";
    let entry = LogEntry::new(
        LogLevel::Info,
        test_tag.to_string(),
        "Test message for tag handling".to_string(),
        vec![],
        vec![],
    );

    logger.write_log(entry).unwrap();

    // 强制flush
    drop(logger);

    // 查找生成的日志文件
    let log_files: Vec<_> = std::fs::read_dir(&temp_dir)
        .unwrap()
        .filter_map(|entry| {
            let entry = entry.ok()?;
            let path = entry.path();
            if path.extension()? == "log" {
                Some(path)
            } else {
                None
            }
        })
        .collect();

    assert!(!log_files.is_empty(), "No log files found");

    // 读取并验证文件内容
    let log_file = &log_files[0];
    let file_data = std::fs::read(log_file).unwrap();
    
    println!("Log file size: {} bytes", file_data.len());

    // 解析protobuf数据
    if file_data.len() > 0 {
        // 跳过varint长度前缀，直接解析protobuf
        let mut cursor = std::io::Cursor::new(&file_data);
        if let Ok(Some(metadata)) = LogMetaData::read_delimited_from(&mut cursor) {
            println!("Parsed tag from file: '{}'", metadata.tag);
            
            // 验证文件中的tag是原始值，不包含环境标识
            assert_eq!(metadata.tag, test_tag, 
                       "Tag in log file should be original tag without environment identifier");
            
            assert!(!metadata.tag.contains("[env:"), 
                    "Tag in log file should not contain environment identifier");
            
            println!("✅ File tag verification passed!");
        } else {
            panic!("Failed to parse protobuf data from log file");
        }
    }

    // 清理测试文件
    let _ = std::fs::remove_dir_all(&temp_dir);

    println!("✅ Full logging tag handling test passed!");
}
